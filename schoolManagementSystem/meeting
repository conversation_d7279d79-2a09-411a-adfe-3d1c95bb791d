products
quality check
on sale
order
in use

information on each section
	

which technology are you guys using


product name, 
image, 
id (ipc)
description
date
categogies of product
variant (color, stripes, or shapes)
quantity
manufacturing cost
the system will generate sales price by multiplying by the finance given %
status (wether is manufactured or being manufactured), should be a dropdown

on the actions button, put an optoin to put the proudct on sale


options
on_sale
being_distributed
being_proxied
ordered
in_use


#Product search 



Product
Merchandize
Ordered
In_Use
Category


export to pdf
