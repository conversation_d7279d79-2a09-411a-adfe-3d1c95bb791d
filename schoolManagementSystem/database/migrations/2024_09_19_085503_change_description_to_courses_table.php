<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add a temporary column to hold the data
        Schema::table('courses', function (Blueprint $table) {
            $table->text('description_temp')->nullable();
        });

        // Migrate data from old column to temporary column
        DB::statement('UPDATE courses SET description_temp = description');

        // Drop the old column
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('description');
        });

        // Add the new column with the desired type, allowing NULL values
        Schema::table('courses', function (Blueprint $table) {
            $table->text('description')->nullable();
        });

        // Migrate data back from the temporary column to the new column
        DB::statement('UPDATE courses SET description = COALESCE(description_temp, "")');

        // Drop the temporary column
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('description_temp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add a temporary column to hold the data
        Schema::table('courses', function (Blueprint $table) {
            $table->string('description_temp')->nullable();
        });

        // Migrate data from old column to temporary column
        DB::statement('UPDATE courses SET description_temp = description');

        // Drop the old column
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('description');
        });

        // Add the new column with the desired type
        Schema::table('courses', function (Blueprint $table) {
            $table->string('description');
        });

        // Migrate data back from the temporary column to the new column
        DB::statement('UPDATE courses SET description = COALESCE(description_temp, "")');

        // Drop the temporary column
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('description_temp');
        });
    }
};
