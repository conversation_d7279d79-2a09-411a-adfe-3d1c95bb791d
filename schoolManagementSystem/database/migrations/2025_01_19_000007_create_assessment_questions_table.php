<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assessment_id')->constrained('online_assessments')->onDelete('cascade');
            $table->text('question');
            $table->enum('type', ['multiple_choice', 'true_false', 'short_answer', 'essay', 'fill_blank']);
            $table->json('options')->nullable(); // For multiple choice questions
            $table->json('correct_answers')->nullable(); // Correct answer(s)
            $table->integer('marks')->default(1);
            $table->integer('order')->default(0);
            $table->text('explanation')->nullable(); // Explanation for correct answer
            $table->string('image_path')->nullable(); // Optional question image
            $table->json('metadata')->nullable(); // Additional question data
            $table->timestamps();
            
            $table->index(['assessment_id', 'order']);
            $table->index(['type', 'marks']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_questions');
    }
};
