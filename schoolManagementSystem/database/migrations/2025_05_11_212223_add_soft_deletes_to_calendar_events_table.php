<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            // Adding soft delete column
            $table->softDeletes();

            // Adding index to the deleted_at column for faster queries
            $table->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            // Dropping the index on deleted_at column
            $table->dropIndex(['deleted_at']);

            // Dropping the soft delete column
            $table->dropSoftDeletes();
        });
    }
};
