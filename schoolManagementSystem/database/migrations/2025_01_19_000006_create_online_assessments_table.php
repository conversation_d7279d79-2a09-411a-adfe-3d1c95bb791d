<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('online_assessments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('courses')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained('academic_years')->onDelete('cascade');
            $table->foreignId('level_id')->constrained('levels')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('instructions')->nullable();
            $table->enum('type', ['ca', 'exam', 'quiz', 'test', 'midterm', 'final']);
            $table->integer('total_marks')->default(100);
            $table->integer('duration_minutes')->nullable(); // Time limit in minutes
            $table->datetime('start_time');
            $table->datetime('end_time');
            $table->boolean('shuffle_questions')->default(false);
            $table->boolean('show_results_immediately')->default(false);
            $table->boolean('allow_review')->default(true);
            $table->integer('max_attempts')->default(1);
            $table->boolean('is_published')->default(false);
            $table->boolean('require_password')->default(false);
            $table->string('password')->nullable();
            $table->json('settings')->nullable(); // Additional settings
            $table->timestamps();
            
            $table->index(['course_id', 'type', 'is_published']);
            $table->index(['start_time', 'end_time']);
            $table->index(['academic_year_id', 'level_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('online_assessments');
    }
};
