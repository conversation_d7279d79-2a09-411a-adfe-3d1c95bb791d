<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('courses')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained('academic_years')->onDelete('cascade');
            $table->foreignId('level_id')->constrained('levels')->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->text('instructions')->nullable();
            $table->enum('type', ['assignment', 'project', 'essay', 'quiz', 'presentation', 'other']);
            $table->integer('max_score')->default(100);
            $table->integer('weight_percentage')->default(10); // Weight in final grade
            $table->datetime('start_date');
            $table->datetime('due_date');
            $table->datetime('late_submission_deadline')->nullable();
            $table->boolean('allow_late_submission')->default(false);
            $table->integer('late_penalty_percentage')->default(0);
            $table->boolean('is_group_assignment')->default(false);
            $table->integer('max_group_size')->nullable();
            $table->json('allowed_file_types')->nullable(); // ['pdf', 'doc', 'docx', etc.]
            $table->integer('max_file_size_mb')->default(10);
            $table->integer('max_files')->default(1);
            $table->boolean('is_published')->default(false);
            $table->text('grading_rubric')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['course_id', 'is_published']);
            $table->index(['due_date', 'is_published']);
            $table->index(['academic_year_id', 'level_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignments');
    }
};
