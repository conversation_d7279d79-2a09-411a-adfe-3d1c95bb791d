<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->morphs('transactionable'); // Polymorphic relation to payments, expenses, etc.
            $table->decimal('amount', 10, 2);
            $table->date('transaction_date');
            $table->string('transaction_type'); // Income or Expenditure
            $table->string('reference');
            $table->enum('status', ['pending', 'approved', 'failed'])->default('pending');
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
