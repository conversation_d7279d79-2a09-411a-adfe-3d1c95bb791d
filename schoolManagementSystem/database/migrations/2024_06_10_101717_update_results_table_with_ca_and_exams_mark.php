<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('results', function (Blueprint $table) {
            $table->renameColumn('mark', 'total_mark');
            $table->float('ca_mark')->nullable()->after('student_id');
            $table->float('exam_mark')->nullable()->after('ca_mark');
        });
    }

    public function down()
    {
        Schema::table('results', function (Blueprint $table) {
            $table->renameColumn('total_mark', 'mark');
            $table->dropColumn('ca_mark');
            $table->dropColumn('exam_mark');
        });
    }
};
