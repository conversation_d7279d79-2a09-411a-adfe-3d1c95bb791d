<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assignment_id')->constrained('assignments')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->foreignId('group_id')->nullable()->constrained('assignment_groups')->onDelete('cascade');
            $table->text('submission_text')->nullable();
            $table->json('file_paths')->nullable(); // Array of file paths
            $table->json('file_metadata')->nullable(); // File names, sizes, types
            $table->datetime('submitted_at');
            $table->boolean('is_late')->default(false);
            $table->enum('status', ['draft', 'submitted', 'graded', 'returned'])->default('draft');
            $table->integer('score')->nullable();
            $table->text('feedback')->nullable();
            $table->text('private_notes')->nullable(); // For instructor use
            $table->datetime('graded_at')->nullable();
            $table->foreignId('graded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->integer('attempt_number')->default(1);
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->unique(['assignment_id', 'student_id', 'attempt_number']);
            $table->index(['assignment_id', 'status']);
            $table->index(['student_id', 'submitted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_submissions');
    }
};
