<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('fees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained('students');
            $table->foreignId('academic_year_id')->constrained();
            $table->foreignId('fee_type_id')->constrained();
            $table->foreignId('account_id')->nullable()->constrained();
            $table->decimal('amount', 8, 2);
            $table->enum('status', ['incomplete', 'complete'])->default('incomplete');
            $table->string('reference')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('fees');
    }
};
