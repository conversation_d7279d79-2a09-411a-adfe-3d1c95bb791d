<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use App\Models\School;
use App\Models\Program;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Loop through all schools and migrate their 'programs' array
        School::chunk(100, function ($schools) {
            foreach ($schools as $school) {
                $programAbbreviations = $school->programs;

                // json_decode the abbreavations
                $programAbbreviations = json_decode($programAbbreviations, true);

                if (is_array($programAbbreviations)) {
                    $programs = Program::whereIn('abbreviation', $programAbbreviations)->get();

                    foreach ($programs as $program) {
                        if (!$school->programRelation()->where('program_id', $program->id)->exists()) {
                            $school->programRelation()->attach($program->id);
                        }
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Optionally, you can define how to revert this operation
        // This could mean detaching the programs from the schools
        Schema::disableForeignKeyConstraints();
        DB::table('program_school')->truncate();
        Schema::enableForeignKeyConstraints();
    }
};
