<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_group_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('group_id')->constrained('assignment_groups')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->datetime('joined_at');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['group_id', 'student_id']);
            $table->index(['student_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_group_members');
    }
};
