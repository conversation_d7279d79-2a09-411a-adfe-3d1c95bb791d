<?php

use App\Enums\IncomeCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('incomes', function (Blueprint $table) {
            $table->string('from')->nullable()->after('amount');
            $table->string('reference');
            $table->enum('type', IncomeCategory::values());
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('incomes', function (Blueprint $table) {
            $table->dropColumn('from');
            $table->dropColumn('reference');
            $table->dropColumn('type');
        });
    }
};
