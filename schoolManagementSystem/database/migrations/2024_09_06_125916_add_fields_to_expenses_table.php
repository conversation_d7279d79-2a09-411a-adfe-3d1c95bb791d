<?php

use App\Enums\ExpenseCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->foreignId('lecturer_id')->nullable()->after('account_id')->constrained('users', 'id')->onDelete('set null');
            $table->foreignId('student_id')->nullable()->after('account_id')->constrained('students', 'id')->onDelete('set null');
            $table->string('to')->nullable()->after('account_id');
            $table->string('reference');
            $table->enum('type', ExpenseCategory::values())->nullable()->after('account_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropColumn('lecturer_id');
            $table->dropColumn('student_id');
            $table->dropColumn('to');
            $table->dropColumn('reference');
            $table->dropColumn('type');
        });
    }
};
