<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_answers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attempt_id')->constrained('assessment_attempts')->onDelete('cascade');
            $table->foreignId('question_id')->constrained('assessment_questions')->onDelete('cascade');
            $table->json('answer')->nullable(); // Student's answer(s)
            $table->integer('marks_awarded')->nullable();
            $table->boolean('is_correct')->nullable();
            $table->text('feedback')->nullable(); // Instructor feedback for essay questions
            $table->datetime('answered_at')->nullable();
            $table->json('metadata')->nullable(); // Additional answer data
            $table->timestamps();
            
            $table->unique(['attempt_id', 'question_id']);
            $table->index(['question_id', 'is_correct']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_answers');
    }
};
