<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('incomes', function (Blueprint $table) {
            // if it does not exist
            if (!Schema::hasColumn('incomes', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        Schema::table('transactions', function (Blueprint $table){
            if (!Schema::hasColumn('transactions', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        Schema::table('expenses', function (Blueprint $table){
            if (!Schema::hasColumn('expenses', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        Schema::table('fees', function (Blueprint $table){
            if (!Schema::hasColumn('fees', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        Schema::table('installments', function (Blueprint $table){
            if (!Schema::hasColumn('installments', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        Schema::table('payrolls', function (Blueprint $table){
            if (!Schema::hasColumn('payrolls', 'deleted_at')) {
                $table->softDeletes();
            }
        });



    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('incomes', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('transactions', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('expenses', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('fees', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('installments', function (Blueprint $table){
            $table->dropSoftDeletes();
        });

        Schema::table('payrolls', function (Blueprint $table){
            $table->dropSoftDeletes();
        });
    }
};
