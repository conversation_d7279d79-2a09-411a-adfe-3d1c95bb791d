<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->id();
            $table->string('title');

            // Event type codes with clear meaning:
            $table->enum('type', [
                'CA', // Continuous Assessment
                'EO', // End of Lectures
                'SE', // Semester Examination
                'EX', // Extra-curricular Event
                'OT', // Other (miscellaneous events)
                'PR', // Project/Presentation
                'OR', // Orientation
                'RE', // Resumption
                'GR', // Graduation
                'HO', // Holiday
                'ME', // Meeting
            ]);

            $table->text('description')->nullable();
            $table->date('event_date');
            $table->string('academic_year');
            $table->enum('semester', ['First', 'Second', 'Third']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('calendar_events');
    }
};
