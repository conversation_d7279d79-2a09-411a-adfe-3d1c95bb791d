<?php

use App\Enums\Semester;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure any null or invalid data is cleaned up before modifying the column
        DB::table('courses')
            ->whereNull('semester')
            ->orWhereNotIn('semester', Semester::values())
            ->update(['semester' => Semester::FIRST]);  // Set to default first semester (1)

        // Now modify the column to set the default and ensure it's not nullable
        Schema::table('courses', function (Blueprint $table) {
            $table->enum('semester', Semester::values())
                  ->default(Semester::FIRST)
                  ->change();  // Apply the change to the column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Revert the semester column back to nullable without default
            $table->enum('semester', Semester::values())
                  ->nullable()
                  ->change();
        });
    }
};
