<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProgramsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('programs')->insert([
            [
                'name' => 'Higher National Diploma',
                'description' => 'A post-secondary education qualification.',
                'abbreviation' => 'HND',
                'cost' => 1500.00,
                'duration' => 2.0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'National Diploma',
                'description' => 'A diploma awarded by polytechnics in various fields.',
                'abbreviation' => 'ND',
                'cost' => 1000.00,
                'duration' => 2.0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Bachelor of Science',
                'description' => 'An undergraduate academic degree.',
                'abbreviation' => 'BSc',
                'cost' => 3000.00,
                'duration' => 4.0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Master of Science',
                'description' => 'A graduate degree in science awarded by universities.',
                'abbreviation' => 'MSc',
                'cost' => 5000.00,
                'duration' => 2.0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
