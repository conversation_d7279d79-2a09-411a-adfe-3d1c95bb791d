<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\School;
use App\Models\Department;
use App\Models\User;

class SchoolsAndDepartmentSeeders extends Seeder
{
    public function run(): void
    {
        $schoolsData = [
            [
                'name' => 'School A',
                'admin_id' => 1,
                'school_email' => '<EMAIL>',
                'address' => '123 School A Street',
                'logo' => 'path/to/logo_a.png',
                'programs' => ['Computer Science', 'Physics', 'Mathematics']
            ],
            [
                'name' => 'School B',
                'admin_id' => 1,
                'school_email' => '<EMAIL>',
                'address' => '456 School B Street',
                'logo' => 'path/to/logo_b.png',
                'programs' => ['Biology', 'Chemistry', 'Geology']
            ],
            [
                'name' => 'School C',
                'admin_id' => 1,
                'school_email' => '<EMAIL>',
                'address' => '789 School C Street',
                'logo' => 'path/to/logo_c.png',
                'programs' => ['History', 'Literature', 'Languages']
            ]
        ];

        foreach ($schoolsData as $schoolData) {
            $school = School::create($schoolData);

            $departmentsData = [
                [
                    'name' => 'Department 1',
                    'code' => $school->name . 'D1',
                    'description' => 'Description of Department 1',
                    'admin_id' => 1,
                    'school_id' => $school->id,
                ],
                [
                    'name' => 'Department 2',
                    'code' => $school->name . 'D2',
                    'description' => 'Description of Department 2',
                    'admin_id' => 1,
                    'school_id' => $school->id,
                ],
                [
                    'name' => 'Department 3',
                    'code' => $school->name . 'D3',
                    'description' => 'Description of Department 3',
                    'admin_id' => 1,
                    'school_id' => $school->id,
                ]
            ];

            foreach ($departmentsData as $departmentData) {
                Department::create($departmentData);
            }
        }
    }
}
