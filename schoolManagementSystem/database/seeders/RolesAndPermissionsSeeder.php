<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Student;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        $permissions = [
            'create-student',
            'edit-student',
            'delete-student',
            'create-lecturer',
            'edit-lecturer',
            'delete-lecturer',
            'create-accountant',
            'edit-accountant',
            'delete-accountant',
            'create-developer',
            'edit-developer',
            'delete-developer',
            'view-result',
            'download-form',
            'view-transcript',
            'manage-departments',
            'manage-courses',
            'manage-faculties',
            'manage-users',
            'view-student-grades',
            'upload-assignments',
            'submit-assignments',
            'create-invoice',
            'edit-invoice',
            'view-payments',
            'manage-fees',
            'manage-scholarships',
            'access-admin-panel',
            'modify-system-settings',
            'manage-database',
            'manage-server',
            'view-grades',

        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        $roles = [
            'superAdmin' => [
                'create-student',
                'edit-student',
                'delete-student',
                'create-lecturer',
                'edit-lecturer',
                'delete-lecturer',
                'create-accountant',
                'edit-accountant',
                'delete-accountant',
                'create-developer',
                'edit-developer',
                'delete-developer',
                'view-result',
                'download-form',
                'view-transcript',
                'manage-departments',
                'manage-courses',
                'manage-faculties',
                'manage-users',
                // Add more permissions as needed
            ],
            'admin' => [
                'create-student',
                'edit-student',
                'delete-student',
                'view-result',
                'download-form',
                'view-transcript',
                'manage-departments',
                'manage-courses',
            ],
            'lecturer' => [
                'create-student',
                'edit-student',
                'view-student-grades',
                'upload-assignments',
                'view-result',
            ],
            'student' => [
                'view-grades',
                'submit-assignments',
                'view-result',
                'download-form',
                'view-transcript',
            ],
            'accountant' => [
                'create-invoice',
                'edit-invoice',
                'view-payments',
                'manage-fees',
                'manage-scholarships',
            ],
            'developer' => [
                'access-admin-panel',
                'modify-system-settings',
                'manage-database',
                'manage-server',
            ],
        ];

        // Create roles and assign permissions
        foreach ($roles as $roleName => $rolePermissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            $role->syncPermissions($rolePermissions);

            // Create a user for each role with alternating genders
            $gender = ($roleName == 'superAdmin' || $roleName == 'admin' || $roleName == 'lecturer' || $roleName == 'developer') ? 'male' : 'female';

            $user = User::create([
                'first_name' => ucfirst($roleName),
                'last_name' => 'User',
                'gender' => $gender,
                'email' => $roleName . '@example.com',
                'password' => Hash::make('password'),
                'phone_number' => rand(1000000000, 9999999999),
            ]);

            $user->assignRole($roleName);

            // Create a student profile if the role is 'student'
            if ($roleName == 'student') {
                Student::create([
                    'user_id' => $user->id,
                    'matricule' => 'MAT-' . strtoupper(uniqid()),
                    'place_of_birth' => 'Some Place',
                    'region_of_origin' => 'Some Region',
                    'marital_status' => 'Single',
                    'program_id' => random_int(1, 3),

                    'date_of_admission' => now(),
                    'nationality' => 'Some Nationality',
                ]);
            }
        }

        // Additional students creation
        for ($i = 1; $i <= 5; $i++) {
            $user = User::create([
                'first_name' => 'Student' . $i,
                'last_name' => 'User',
                'gender' => $i % 2 == 0 ? 'male' : 'female',
                'email' => 'student' . $i . '@example.com',
                'password' => Hash::make('password'),
            ]);

            $user->assignRole('student');

            Student::create([
                'user_id' => $user->id,
                'matricule' => 'MAT-' . strtoupper(uniqid()),
                'place_of_birth' => 'Some Place',
                'region_of_origin' => 'Some Region',
                'marital_status' => 'Single',
                'program_id' => random_int(1, 3),
                'date_of_admission' => now(),
                'nationality' => 'Some Nationality',
            ]);
        }
    }
}
