<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SystemSettingsSeeder extends Seeder
{
    public function run()
    {
        // Insert system settings for the current semester
        DB::table('system_settings')->insert([
            'key' => 'current_semester',
            'value' => '1',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Insert the current academic year setting
        DB::table('system_settings')->insert([
            'key' => 'current_academic_year_id',
            'value' => '2023-2024',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Insert other system settings
        DB::table('system_settings')->insert([
            'key' => 'system_maintenance_mode',
            'value' => 'false',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('system_settings')->insert([
            'key' => 'max_login_attempts',
            'value' => '5',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('system_settings')->insert([
            'key' => 'contact_email',
            'value' => '<EMAIL>',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
