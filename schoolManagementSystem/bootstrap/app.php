<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Illuminate\Auth\AuthenticationException; // Ensure this is imported
use Symfony\Component\Routing\Exception\RouteNotFoundException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'api.rate.limit' => \App\Http\Middleware\ApiRateLimitMiddleware::class,
            'security.headers' => \App\Http\Middleware\SecurityHeadersMiddleware::class,
            'api.logging' => \App\Http\Middleware\ApiLoggingMiddleware::class,
        ]);

        // Apply security headers to all API routes
        $middleware->api(append: [
            \App\Http\Middleware\SecurityHeadersMiddleware::class,
            \App\Http\Middleware\ApiLoggingMiddleware::class,
        ]);

        // Apply rate limiting to API routes
        $middleware->group('api', [
            \App\Http\Middleware\ApiRateLimitMiddleware::class . ':120,1', // 120 requests per minute
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {

        // Handle Authentication Exception
        $exceptions->render(function (AuthenticationException $e, Request $request) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        });

        // Handle Validation Exception
        $exceptions->render(function (ValidationException $e, Request $request) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => $e->errors(),
            ], 422);
        });

        // Handle Route Nof Found
        $exceptions->render(function (RouteNotFoundException $e, Request $request) {

            // check if $e has any thing related to login
            if (str_contains($e->getMessage(), 'Route [login] not defined')) {

                return response()->json(
                    [
                        'success' => false,
                        'message' => 'Unauthenticated, You are not logged in'
                    ],
                    401
                );
            }

            return response()->json(
                [
                    'success' => false,
                    'message' => 'Endpoint not found.'
                ],
                404);
        });

        // Handle Model Not Found Exception
        $exceptions->render(function (ModelNotFoundException $e, Request $request) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Resource not found.'
                ],
                404);
        });

        // Handle Not Found HTTP Exception
        $exceptions->render(function (NotFoundHttpException $e, Request $request) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Endpoint not found.'
                ],
                404);
        });

        // Handle Method Not Allowed HTTP Exception
        $exceptions->render(function (MethodNotAllowedHttpException $e, Request $request) {
            return response()->json([
                    'success' => false,
                    'message' => 'Method not allowed.'
                ], 405);
        });

        // Handle Too Many Requests Exception (Rate Limiting)
        $exceptions->render(function (TooManyRequestsHttpException $e, Request $request) {
            return response()->json([
                    'success' => false,
                    'message' => 'Too many attempts, please try again later.'], 429);
        });

        // Handle Generic HTTP Exception
        $exceptions->render(function (HttpException $e, Request $request) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()], $e->getStatusCode());
        });

        // Handle Generic Exception
        $exceptions->render(function (Throwable $e, Request $request) {
            return response()->json([
                'success' => false,
                'message' => 'Server error. Please try again later.'], 500);
        });

        // Decide if JSON should be returned for all requests (since all are API)
        $exceptions->shouldRenderJsonWhen(function (Request $request, Throwable $e) {
            return true; // Always return JSON
        });

    })->create();
