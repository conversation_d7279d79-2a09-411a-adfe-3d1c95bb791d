<?php

use App\Http\Controllers\Api\AdminStatisticsController;
use App\Http\Controllers\Api\AcademicYearController;
use App\Http\Controllers\Api\AcademicYearImportController;
use App\Http\Controllers\Api\Accounting\AccountController;
use App\Http\Controllers\Api\Accounting\CategoryCrudController;
use App\Http\Controllers\Api\Accounting\ExpenseController;
use App\Http\Controllers\Api\Accounting\FeePaymentController;
use App\Http\Controllers\Api\Accounting\FeeTypeController;
use App\Http\Controllers\Api\Accounting\FeeTypeImportController;
use App\Http\Controllers\Api\Accounting\IncomeController;
use App\Http\Controllers\Api\Accounting\StatisticController;
use App\Http\Controllers\Api\Accounting\TransactionsController;
use App\Http\Controllers\Api\Auth\AdminLoginController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\LecturerLoginController;
use App\Http\Controllers\Api\Auth\StudentLoginController;
use App\Http\Controllers\Api\Courses\CoursesController;
use App\Http\Controllers\Api\Courses\ImportCoursesController;
use App\Http\Controllers\Api\Departments\DepartmentController;
use App\Http\Controllers\Api\Departments\ImportDepartmentsController;
use App\Http\Controllers\Api\LevelController;
use App\Http\Controllers\Api\Profile\UpdateProfileController;
use App\Http\Controllers\Api\Programs\ImportProgramsController;
use App\Http\Controllers\Api\Programs\ProgramController;
use App\Http\Controllers\Api\Schools\ImportSchoolsController;
use App\Http\Controllers\Api\Schools\SchoolController;
use App\Http\Controllers\Api\Students\CourseRegistrationController;
use App\Http\Controllers\Api\SystemSettingController;
use App\Http\Controllers\Api\Tutors\ImportLecturerController;
use App\Http\Controllers\Api\Tutors\LecturerController;
use App\Http\Controllers\Api\Lecturer\LecturerDashboardController;
use App\Http\Controllers\Api\Lecturer\LecturerCoursesController;
use App\Http\Controllers\Api\Lecturer\LecturerProfileController;
use App\Http\Controllers\Api\Lecturer\LecturerAssignmentController;
use App\Http\Controllers\Api\Lecturer\LecturerNotificationController;
use App\Http\Controllers\CalendarEventController;
use App\Http\Controllers\Results\ImportCAMarksController;
use App\Http\Controllers\Results\ImportExamMarksController;
use App\Http\Controllers\Results\ResultsController;
use App\Http\Controllers\StudentExportController;
use App\Http\Controllers\Students\ImportStudentsControler;
use App\Http\Controllers\Students\StudentsController;
use App\Http\Controllers\Testcontroller;
use App\Http\Controllers\SchoolCalendarController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');




Route::prefix('v1')->group(function () {
    Route::get('/', function () {
        return response()->json([
            'message' => 'Welcome to the API',
            'success' => true,
            'description' => 'API Documentation',
            'version' => '1.0.0',
        ]);
    });

    Route::post('/student/login', StudentLoginController::class);
    Route::post('/admin/login', AdminLoginController::class);
    Route::post('/lecturer/login', LecturerLoginController::class);

    Route::post('/logout', [AuthController::class, 'logout'])->middleware(['auth:sanctum']);

    //testing routes
    Route::get('all-students', [Testcontroller::class, 'allstudents']);
    Route::get('all-staffs', [Testcontroller::class, 'allStaffs']);

    Route::group(['middleware' => 'auth:sanctum'], function () {

        /**
         * School routes
         */
        Route::group(['prefix' => 'schools', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::post('import-schools', ImportSchoolsController::class);
            Route::get('search', [SchoolController::class, 'search']);
            Route::get('/deparments/{id}', [SchoolController::class, 'getDepartmentsBySchoolId']);
        });

        Route::group(['prefix' => 'system', 'role:admin|superAdmin'], function () {
            Route::get('', [SystemSettingController::class, 'index']);
            Route::put('', [SystemSettingController::class, 'update']);
            Route::get('{key}', [SystemSettingController::class, 'get']);
            Route::delete('', [SystemSettingController::class, 'clear']);
            Route::delete('{key|', [SystemSettingController::class, 'delete']);
        });
        Route::apiResource('schools', SchoolController::class);


        /**
         * Department routes
         */
        Route::group(['prefix' => 'departments', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::post('import-departments', ImportDepartmentsController::class);
            route::get('with-pagination', [DepartmentController::class, 'withPagination']);
            Route::get('search', [DepartmentController::class, 'search']);

        });
        Route::apiResource('departments', DepartmentController::class);
        Route::group(['prefix' => 'programs', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::post('import-programs', ImportProgramsController::class);
        });

        Route::group(['prefix' => 'academic-years', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::get('search', [AcademicYearController::class, 'search']);
            Route::get('import-academic-years', AcademicYearImportController::class);
        });


        Route::group(['prefix' => 'fee-types', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::get('import', FeeTypeImportController::class);
            Route::get('search', [FeeTypeController::class, 'search']);
        });

        Route::group(['prefix' => 'fees', 'middleware' => 'role:admin|accountant'], function () {
            Route::get('', [FeePaymentController::class, 'index']);
            Route::post('', [FeePaymentController::class, 'store']);
            Route::put('/{id}', [FeePaymentController::class, 'update']);
        });


        Route::group(['prefix' => 'fees', 'middleware' => 'role:admin|accountant|student'], function () {
            Route::get('all-fees-by-student/{id}', [FeePaymentController::class, 'getFeesByStudent']);
            Route::get('/{id}', [FeePaymentController::class, 'show']);
            Route::get('installments', [FeePaymentController::class, 'installments']);
        });

        Route::group(['prefix' => 'transactions', 'middleware' => 'role:admin|accountant|student'], function () {
            Route::get('', [TransactionsController::class, 'index']);
            Route::get('/transaction/{id}', [TransactionsController::class, 'show']);
            Route::get('get-user-transactions', [TransactionsController::class, 'getUserTransactions']);
            Route::get('get-transaction-by-reference', [TransactionsController::class, 'getTransactionByReference']);
            Route::get('{id}/pdf', [TransactionsController::class, 'downloadPdf']);

            // get receipt
            Route::get('fetch-dashboard-stats', StatisticController::class);
        });

        Route::group(['prefix' => 'incomes', 'middleware' => 'role:accountant'], function () {
            Route::get('', [IncomeController::class, 'index']);
            Route::get('/income/{id}', [IncomeController::class, 'show']);
            Route::post('', [IncomeController::class, 'store']);
            Route::put('/{id}', [IncomeController::class, 'update']);
            Route::delete('/{id}', [IncomeController::class, 'destroy']);
        });

        Route::group(['prefix' => 'expenditures', 'middleware' => 'role:accountant'], function () {
            Route::get('', [ExpenseController::class, 'index']);
            Route::get('/expenditure/{id}', [ExpenseController::class, 'show']);
            Route::post('', [ExpenseController::class, 'store']);
            Route::put('/{id}', [ExpenseController::class, 'update']);
            Route::delete('/{id}', [ExpenseController::class, 'destroy']);
        });


        Route::group(['prefix' => 'categories', 'middleware' => 'role:accountant|admin'], function () {
            Route::get('income', [CategoryCrudController::class, 'allIncomeCategories']);
            Route::get('expense', [CategoryCrudController::class, 'allExpenseCategories']);

            Route::post('income', [CategoryCrudController::class, 'createIncomeCategory']);
            Route::post('expense', [CategoryCrudController::class, 'createExpenseCategory']);

            Route::put('income/{id}', [CategoryCrudController::class, 'updateIncomeCategory']);
            Route::put('expense/{id}', [CategoryCrudController::class, 'updateExpenseCategory']);

            Route::delete('income/{id}', [CategoryCrudController::class, 'deleteIncomeCategory']);
            Route::delete('expense/{id}', [CategoryCrudController::class, 'deleteExpenseCategory']);

            Route::get('search/{model}', [CategoryCrudController::class, 'search']);

            Route::post('import', [CategoryCrudController::class, 'import']);
        });

        Route::group(['prefix' => 'fees', 'middleware' => 'role:admin|accountant|student'], function () {
            Route::post('status', [FeePaymentController::class, 'getStudentFeeStatus']);
            Route::get('get-receipt/{id}', [FeePaymentController::class, 'getReceipt']);
            Route::get('installment/{id}', [FeePaymentController::class, 'getInstallmentReceipt']);

        });

        Route::apiResource('programs', ProgramController::class);
        Route::apiResource('academic-years', AcademicYearController::class);
        Route::apiResource('fee-types', FeeTypeController::class);
        Route::apiResource('accounts', AccountController::class);

        Route::get('/statistics', action: [AdminStatisticsController::class, 'getStatistics'])->middleware('role:admin|superAdmin');
        // export statistis
        Route::get('/statistics/export', action: [AdminStatisticsController::class, 'exportStatisticsToCSV'])->middleware('role:admin|superAdmin');

        /**
         * Lecturer routes
         */
        Route::group(['prefix' => 'lecturers', 'middleware' => 'role:admin|superAdmin'], function () {
            Route::post('import-lecturers', ImportLecturerController::class);
            Route::post('{id}', [LecturerController::class, 'update']);
            Route::get('search', [DepartmentController::class, 'search']);
        });
        Route::apiResource('lecturers', LecturerController::class)->middleware('role:admin|superAdmin')
            ->except('put');
        Route::apiResource('levels', LevelController::class);

        /**
         * Course routes
         */

        Route::group(['prefix' => 'courses'], function () {
            Route::get('search', [CoursesController::class, 'search']);
            Route::get('course/course-by-department', [CoursesController::class, 'getCourseByDepartment']);
            Route::get('course/course-by-student-department', [CoursesController::class, 'getCourseByStudentDepartment']);
            Route::post('import-courses', ImportCoursesController::class);
        });
        Route::apiResource('courses', CoursesController::class);


        Route::group(['prefix' => 'students'], function () {
            Route::get('/student/search', [StudentsController::class, 'search']);
            Route::post('import-students', ImportStudentsControler::class);
            Route::put('{student}', [StudentsController::class, 'updateStudent']);
            Route::put('{student}/update-academic-year-an-level', [StudentsController::class, 'updateLevelAndAcademicYear']);
            Route::put('{student}/account', [StudentsController::class, 'updateUserAccount']);
        });
        Route::apiResource('students', StudentsController::class)->except(['update']);


        Route::post('users/update', [UpdateProfileController::class, 'updateProfile']);
        Route::post('users/profile-image', [UpdateProfileController::class, 'updateProfileImage']);

        Route::post('/import-ca-marks', ImportCAMarksController::class);
        Route::post('/import-exam-marks', ImportExamMarksController::class);

        Route::group(['prefix' => 'results', 'middleware' => 'role:student|admin'], function () {
            Route::get('/', [ResultsController::class, 'getResults']);
            Route::post('/ca-mark', [ResultsController::class, 'updateCAMark']);
            Route::post('/exam-mark', [ResultsController::class, 'updateExamMark']);

            // New routes
            Route::get('/ca-marks', [ResultsController::class, 'getAllCAMarks']);
            Route::get('/ca-marks/student', [ResultsController::class, 'getCAMarksForStudent']);
            Route::get('/exam-marks', [ResultsController::class, 'getAllExamMarks']);
            Route::get('/exam-marks/student', [ResultsController::class, 'getExamMarksForStudent']);
            Route::get('/all-results', [ResultsController::class, 'getAllResultsBySemesterAndYearAndLevel']);
            Route::get('/all-results/print', [ResultsController::class, 'getAllResultsGrouped']);
        });

        Route::group(['prefix' => 'course-registration'], function () {
            Route::post('register-course', [CourseRegistrationController::class, 'registerCourse'])->name('course.register');
            Route::post('unregister-course', [CourseRegistrationController::class, 'unregisterCourse'])->name('course.unregister');
            Route::get('registered-courses', [CourseRegistrationController::class, 'getRegisteredCourses'])->name('courses.registered');
        });

        /**
         * LMS Routes
         */
        Route::group(['prefix' => 'courses/{courseId}'], function () {
            // Course Materials
            Route::group(['prefix' => 'materials'], function () {
                Route::get('/', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'index']);
                Route::post('/', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'store'])->middleware('role:admin|lecturer');
                Route::get('/{materialId}', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'show']);
                Route::put('/{materialId}', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'update'])->middleware('role:admin|lecturer');
                Route::delete('/{materialId}', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'destroy'])->middleware('role:admin|lecturer');
                Route::get('/{materialId}/download', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'download']);
                Route::post('/reorder', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'reorder'])->middleware('role:admin|lecturer');
            });

            // Assignments
            Route::group(['prefix' => 'assignments'], function () {
                Route::get('/', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'index']);
                Route::post('/', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'store'])->middleware('role:admin|lecturer');
                Route::get('/{assignmentId}', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'show']);
                Route::put('/{assignmentId}', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'update'])->middleware('role:admin|lecturer');
                Route::delete('/{assignmentId}', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'destroy'])->middleware('role:admin|lecturer');
                Route::post('/{assignmentId}/publish', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'publish'])->middleware('role:admin|lecturer');
                Route::post('/{assignmentId}/unpublish', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'unpublish'])->middleware('role:admin|lecturer');
                Route::get('/{assignmentId}/submissions', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'submissions'])->middleware('role:admin|lecturer');
                Route::get('/{assignmentId}/statistics', [\App\Http\Controllers\Api\LMS\AssignmentController::class, 'statistics'])->middleware('role:admin|lecturer');

                // Assignment Submissions
                Route::group(['prefix' => '{assignmentId}/submissions'], function () {
                    Route::post('/', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'store'])->middleware('role:student');
                    Route::get('/{submissionId}', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'show']);
                    Route::put('/{submissionId}', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'update']);
                    Route::delete('/{submissionId}', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'destroy']);
                    Route::get('/{submissionId}/files/{fileIndex}/download', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'download']);
                });
            });
        });

        // Student-specific LMS routes
        Route::group(['prefix' => 'student', 'middleware' => 'role:student'], function () {
            Route::get('/submissions', [\App\Http\Controllers\Api\LMS\AssignmentSubmissionController::class, 'mySubmissions']);
        });

        /**
         * File Management Routes
         */
        Route::group(['prefix' => 'files'], function () {
            Route::post('/upload', [\App\Http\Controllers\Api\FileManagementController::class, 'upload']);
            Route::post('/upload-single', [\App\Http\Controllers\Api\FileManagementController::class, 'uploadSingle']);
            Route::delete('/delete', [\App\Http\Controllers\Api\FileManagementController::class, 'delete']);
            Route::delete('/delete-multiple', [\App\Http\Controllers\Api\FileManagementController::class, 'deleteMultiple']);
            Route::get('/download', [\App\Http\Controllers\Api\FileManagementController::class, 'download']);
            Route::get('/info', [\App\Http\Controllers\Api\FileManagementController::class, 'getFileInfo']);
            Route::post('/thumbnail', [\App\Http\Controllers\Api\FileManagementController::class, 'createThumbnail']);
            Route::get('/list', [\App\Http\Controllers\Api\FileManagementController::class, 'listFiles']);
        });

        /**
         * Notification Routes
         */
        Route::group(['prefix' => 'notifications'], function () {
            Route::get('/', [\App\Http\Controllers\Api\NotificationController::class, 'index']);
            Route::get('/unread-count', [\App\Http\Controllers\Api\NotificationController::class, 'unreadCount']);
            Route::get('/recent', [\App\Http\Controllers\Api\NotificationController::class, 'getRecent']);
            Route::get('/stats', [\App\Http\Controllers\Api\NotificationController::class, 'getStats']);
            Route::get('/type/{type}', [\App\Http\Controllers\Api\NotificationController::class, 'getByType']);
            Route::post('/create', [\App\Http\Controllers\Api\NotificationController::class, 'create'])->middleware('role:admin|lecturer');
            Route::put('/{notificationId}/read', [\App\Http\Controllers\Api\NotificationController::class, 'markAsRead']);
            Route::put('/mark-all-read', [\App\Http\Controllers\Api\NotificationController::class, 'markAllAsRead']);
            Route::put('/mark-multiple-read', [\App\Http\Controllers\Api\NotificationController::class, 'markMultipleAsRead']);
            Route::delete('/{notificationId}', [\App\Http\Controllers\Api\NotificationController::class, 'delete']);
            Route::delete('/multiple', [\App\Http\Controllers\Api\NotificationController::class, 'deleteMultiple']);
            Route::delete('/all-read', [\App\Http\Controllers\Api\NotificationController::class, 'deleteAllRead']);
        });

        /**
         * System Monitoring Routes (Admin Only)
         */
        Route::group(['prefix' => 'system', 'middleware' => 'role:admin'], function () {
            Route::get('/health', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'getSystemHealth']);
            Route::get('/metrics', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'getSystemMetrics']);
            Route::get('/logs', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'getSystemLogs']);
            Route::post('/security-audit', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'performSecurityAudit']);
            Route::post('/optimize-database', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'optimizeDatabase']);
            Route::post('/clear-cache', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'clearCache']);
            Route::post('/warm-cache', [\App\Http\Controllers\Api\SystemMonitoringController::class, 'warmUpCache']);
        });

        Route::group(['prefix' => 'calendar-events'], function () {
            Route::get('/', [CalendarEventController::class, 'index']);
            Route::post('/', [CalendarEventController::class, 'store']);
            Route::put('/{id}', [CalendarEventController::class, 'update']);
        });

    });

    Route::group(['prefix' => 'results', 'middleware' => "throttle:custom-api"], function () {
        Route::get('generate-pdf', [ResultsController::class, 'exportResultsToPDF']);
        Route::get('formb', [CourseRegistrationController::class, 'downloadFormB']);
    });

    /**
     * Lecturer Dashboard Routes
     */
    Route::group(['prefix' => 'lecturer', 'middleware' => ['auth:sanctum', 'role:lecturer']], function () {
        // Dashboard
        Route::get('/dashboard/stats', [LecturerDashboardController::class, 'getStats']);
        Route::get('/dashboard/activities', [LecturerDashboardController::class, 'getRecentActivities']);

        // Courses
        Route::get('/courses', [LecturerCoursesController::class, 'index']);
        Route::get('/courses/{id}', [LecturerCoursesController::class, 'show']);
        Route::put('/courses/{id}', [LecturerCoursesController::class, 'update']);
        Route::get('/courses/{id}/students', [LecturerCoursesController::class, 'getStudents']);

        // Course Materials
        Route::get('/courses/{courseId}/materials', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'index']);
        Route::post('/courses/{courseId}/materials', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'store']);
        Route::put('/courses/{courseId}/materials/{materialId}', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'update']);
        Route::delete('/courses/{courseId}/materials/{materialId}', [\App\Http\Controllers\Api\LMS\CourseMaterialController::class, 'destroy']);

        // Assignments
        Route::get('/assignments', [LecturerAssignmentController::class, 'index']);
        Route::post('/assignments', [LecturerAssignmentController::class, 'store']);
        Route::get('/assignments/{id}', [LecturerAssignmentController::class, 'show']);
        Route::put('/assignments/{id}', [LecturerAssignmentController::class, 'update']);
        Route::delete('/assignments/{id}', [LecturerAssignmentController::class, 'destroy']);
        Route::get('/assignments/{id}/submissions', [LecturerAssignmentController::class, 'getSubmissions']);
        Route::put('/assignments/{assignmentId}/submissions/{submissionId}/grade', [LecturerAssignmentController::class, 'gradeSubmission']);

        // Assessments (using existing LMS controllers)
        Route::get('/assessments', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'index']);
        Route::post('/assessments', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'store']);
        Route::get('/assessments/{id}', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'show']);
        Route::put('/assessments/{id}', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'update']);
        Route::delete('/assessments/{id}', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'destroy']);
        Route::get('/assessments/{id}/results', [\App\Http\Controllers\Api\Assessment\OnlineAssessmentController::class, 'getResults']);

        // Analytics
        Route::get('/analytics/students', [LecturerDashboardController::class, 'getStudentAnalytics']);
        Route::get('/analytics/courses/{courseId}/performance', [LecturerDashboardController::class, 'getCoursePerformance']);

        // Profile
        Route::get('/profile', [LecturerProfileController::class, 'show']);
        Route::put('/profile', [LecturerProfileController::class, 'update']);
        Route::put('/profile/password', [LecturerProfileController::class, 'changePassword']);
        Route::post('/profile/image', [LecturerProfileController::class, 'uploadProfileImage']);
        Route::delete('/profile/image', [LecturerProfileController::class, 'deleteProfileImage']);

        // Notifications
        Route::get('/notifications', [LecturerNotificationController::class, 'index']);
        Route::get('/notifications/unread-count', [LecturerNotificationController::class, 'getUnreadCount']);
        Route::put('/notifications/{id}/read', [LecturerNotificationController::class, 'markAsRead']);
        Route::put('/notifications/read-all', [LecturerNotificationController::class, 'markAllAsRead']);

        // Students
        Route::get('/students', [LecturerCoursesController::class, 'getStudents']);
        Route::get('/students/{id}', [\App\Http\Controllers\Students\StudentsController::class, 'show']);
        Route::get('/students/{studentId}/progress/{courseId}', [LecturerDashboardController::class, 'getStudentProgress']);
    });

});

Route::redirect('/', '/api/v1');


