<?php

use App\Console\Commands\UpdateSystemSettings;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();


Artisan::command('system:update', function () {
    $this->call(UpdateSystemSettings::class);
});

Artisan::command('schedule:run', function () {
    $schedule = app()->make(Schedule::class);
    $schedule->command('system:update')->cron('0 0 1 */1 *');  // Runs every two months on the first day
});
