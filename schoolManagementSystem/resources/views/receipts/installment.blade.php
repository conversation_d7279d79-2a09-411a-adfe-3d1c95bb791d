<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            position: relative; /* Required for absolute positioning of the watermark */
        }
        .watermark {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-45%, -55%) rotate(-45deg); /* Rotate the watermark */
            font-size: 150px; /* Adjust font size as needed */
            color: rgba(94, 126, 111, 0.1); /* Light color for watermark */
            z-index: -1; /* Send it to the back */
            white-space: nowrap; /* Prevent wrapping */
            font-weight: bold; /* Make the watermark bold */
        }
        .header {
            width: 100%;
            background-color: white;
            color: black;
            border-collapse: collapse;
        }
        .header td {
            padding: 10px;
            vertical-align: top;
            border: none;
        }
        .header .left, .header .right {
            width: 220px;
        }
        .header .middle {
            text-align: center;
        }
        .header img {
            max-height: 95px;
            max-width: 95px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        .receipt-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            font-style: italic;
        }
        .receipt-details td:first-child {
            font-weight: bold; /* Bold for keys */
            width: 30%;
        }
        .footer {
            margin-top: 20px;
            border-top: 1px solid #dddddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="watermark">E.H.I.S.T</div>

    <table class="header">
        <tr>
            <td class="left">
                <p>
                    EBENEZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
                </p>
            </td>
            <td class="middle">
                <img src="{{ public_path('images/logo.png') }}" alt="School Logo" style="margin-top: 20px" />
            </td>
            <td class="right">
                <p>BAMENDA - SONAC STREET </p>
                <p>info@ebenezerinst.com4</p>
                <p>+237 67833250</p>
            </td>
        </tr>

        {{-- thick horizontal line --}}
        <tr>
            <td colspan="3">
                <hr>
            </td>
        </tr>
        <tr>
            <td class="left" colspan="2">
                <p>School: {{ strtoupper($receipt['student']['department']['name']) }}</p>
                <p>Program: {{ strtoupper($receipt['student']['department']['school']['name']) }}</p>
                <p>Name: {{ $receipt['student']['user']['first_name'] . " " . $receipt['student']['user']['last_name'] }}</p>
                <p>Matricule: {{ $receipt['student']['matricule'] }}</p>
            </td>

            <td class="right">
                <p>Level: {{ $receipt['student']['level']['name'] }}</p>
                <p>Academic year: {{ $receipt['academicYear'] }} </p>
            </td>
        </tr>

        <tr>
            {{-- the name of the schooo here --}}
    </table>

    <div class="receipt-header">
        Fee Receipt - <code style="color: rgb(137, 34, 34)">Installment</code>
    </div>

    <table class="receipt-details">
        <tr>
            <td><strong>Payment Motive</strong></td>
            <td>{{$receipt['fee']->feeType->name }}</td>
        </tr>
        <tr>
            <td><strong>Amount</strong></td>
            <td>{{ number_format($receipt['installment']->amount, 2) }} FCFA</td>
        </tr>

        <tr>
            <td><strong>Bank Reference</strong></td>
            <td>{{ $receipt['fee']->reference }}</td>
        </tr>

        <tr>
            <td><strong>Transaction Reference</strong></td>
            <td>{{ $receipt['transaction'][0]->reference }}</td>
        </tr>
        <tr>
            <td><strong>Payment Channel</strong></td>
            <td>{{ $receipt['transaction'][0]->payment_channel ?? "Bank" }}</td>
        </tr>
        <tr>
            <td><strong>Payment Date</strong></td>
            <td>{{ \Carbon\Carbon::parse($receipt['fee']->payment_date)->format('d-m-Y') }}</td>
        </tr>
        <tr>
            <td><strong>Academic Year</strong></td>
            <td>{{ $receipt['academicYear'] }}</td>
        </tr>
    </table>

    <table class="footer">
        <tr>
            <td>Date: {{ date('Y-m-d') }}</td>
            <td style="text-align: right;">Signature: _________________________</td>
        </tr>
    </table>

</body>
</html>
