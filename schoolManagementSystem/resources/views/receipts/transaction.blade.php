<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $transaction->transaction_type == 'income' ? 'Income Receipt' : 'Expenditure Receipt' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            position: relative;
        }
        .watermark {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-45%, -55%) rotate(-45deg);
            font-size: 150px;
            color: rgba(94, 126, 111, 0.1);
            z-index: -1;
            white-space: nowrap;
            font-weight: bold;
        }
        .header {
            width: 100%;
            background-color: white;
            color: black;
            border-collapse: collapse;
        }
        .header td {
            padding: 10px;
            vertical-align: top;
            border: none;
        }
        .header .left, .header .right {
            width: 220px;
        }
        .header .middle {
            text-align: center;
        }
        .header img {
            max-height: 95px;
            max-width: 95px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        .receipt-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            font-style: italic;
        }
        .receipt-details td:first-child {
            font-weight: bold;
            width: 30%;
        }
        .footer {
            margin-top: 20px;
            border-top: 1px solid #dddddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="watermark">E.H.I.S.T</div>

    <table class="header">
        <tr>
            <td class="left">
                <p>
                    EBENEZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
                </p>
            </td>
            <td class="middle">
                <img src="{{ public_path('images/logo.png') }}" alt="School Logo" style="margin-top: 20px" />
            </td>
            <td class="right">
                <p>BAMENDA - SONAC STREET </p>
                <p><EMAIL></p>
                <p>+237 67833250</p>
            </td>
        </tr>

        <tr>
            <td colspan="3">
                <hr>
            </td>
        </tr>
        <tr>
            <td class="left" colspan="2">
                <p>Reference: <u style="font-style: bold italic" >{{ $transaction->reference }} </u></p>
            </td>

            <td class="right">
                <p>Date: <u style="font-style: bold italic" > {{ \Carbon\Carbon::parse($transaction->transaction_date)->format('d-m-Y') }} </u></p>
            </td>
        </tr>
    </table>

    <div class="receipt-header">
        {{ $transaction->transaction_type == 'Income' ? 'Income Receipt' : 'Expenditure Receipt' }}
    </div>

    <table class="receipt-details">
        <tr>
            <td><strong>Motive</strong></td>
            <td><b>{{ strtoupper($transaction->transactionable->category->name) }}</b></td>
        </tr>
        <tr>
            <td><strong>Amount</strong></td>
            <td>{{ number_format($transaction->amount, 2) }} FCFA</td>
        </tr>

        @if ($transaction->transactionable_type === \App\Models\Expense::class)
        <tr>
            <td><strong>To</strong></td>
            <td>{{ $transaction->transaction_type == 'Expense' ? $transaction->transactionable->to : 'N/A' }}</td>
        </tr>
        @endif

        <tr>
            <td><strong>Description</strong></td>
            <td>{{ $transaction->transactionable->description }}</td>
        </tr>
    </table>

    <table class="footer">
        <tr>
            <td>Date: {{ date('Y-m-d') }}</td>
            <td style="text-align: right;">Signature: _________________________</td>
        </tr>
    </table>

</body>
</html>
