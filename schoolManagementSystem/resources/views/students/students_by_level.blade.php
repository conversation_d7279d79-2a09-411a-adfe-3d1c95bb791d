<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students Export</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            position: relative;
        }
        .header {
            width: 100%;
            background-color: white;
            color: black;
            border-collapse: collapse;
        }
        .header td {
            padding: 10px;
            vertical-align: top;
            border: none;
        }
        .header .left, .header .right {
            width: 220px;
        }
        .header .middle {
            text-align: center;
        }
        .watermark {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-45%, -55%) rotate(-45deg);
            font-size: 150px;
            color: rgba(94, 126, 111, 0.1);
            z-index: -1;
            white-space: nowrap;
            font-weight: bold;
        }
        .header img {
            max-height: 105px;
            max-width: 105px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        .results-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            font-style: italic;
        }
        .footer {
            margin-top: 20px;
            border-top: 1px solid #dddddd;
            padding-top: 10px;
        }
        .level-header {
            background-color: #f2f2f2;
            padding: 8px;
            margin: 20px 0 10px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="watermark">E.H.I.S.T</div>

    <table class="header">
        <tr>
            <td class="left">
                <p>
                    EBENEZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
                </p>
            </td>
            <td class="middle">
                <img src="{{ public_path('images/logo.png') }}" alt="School Logo" style="margin-top: 20px" />
            </td>
            <td class="right">
                <p>BAMENDA - SONAC STREET</p>
                <p><EMAIL></p>
                <p>+237 67833250</p>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <hr>
            </td>
        </tr>
    </table>

    <div class="results-header">
        Students List by Level
    </div>

    @foreach($groupedStudents as $levelName => $students)
        @if($students->count() > 0)
            <div class="level-header">
                Level: {{ $levelName }}
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Matricule</th>
                        <th>Phone Number</th>
                        <th>Program</th>
                        <th>Department</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($students as $student)
                    <tr>
                        <td>{{ $student->user->first_name }} {{ $student->user->last_name }}</td>
                        <td>{{ $student->matricule }}</td>
                        <td>{{ $student->user->phone_number }}</td>
                        <td>{{ $student->program->name ?? 'N/A' }}</td>
                        <td>{{ $student->department->name ?? 'N/A' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
    @endforeach

    <table class="footer">
        <tr>
            <td><strong>Total Students:</strong> {{ array_sum(array_map(fn($group) => count($group), $groupedStudents->toArray())) }}</td>
            <td><strong>Generated On:</strong> {{ now()->format('Y-m-d H:i:s') }}</td>
        </tr>
    </table>
</body>
</html>
