<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form B - Registered Courses</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .header {
            width: 100%;
            background-color: white; /* White background color */
            color: black; /* Black text color */
            border-collapse: collapse; /* Remove borders */
        }
        .header td {
            padding: 10px;
            vertical-align: top; /* Align text to the top of the cell */
            border: none; /* Remove borders from table cells */
        }
        .header .left, .header .right {
            width: 220px; /* Fixed width for left and right sections */
        }
        .header .middle {
            text-align: center;
        }

        .watermark {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-45%, -55%) rotate(-45deg); /* Rotate the watermark */
            font-size: 150px; /* Adjust font size as needed */
            color: rgba(94, 126, 111, 0.1); /* Light color for watermark */
            z-index: -1; /* Send it to the back */
            white-space: nowrap; /* Prevent wrapping */
            font-weight: bold; /* Make the watermark bold */
        }
        .header img {
            max-height: 105px;
            max-width: 105px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px; /* Add margin to separate table from header */
        }
        th, td {
            border: 1px solid #dddddd; /* Border for the course table */
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #4CAF50; /* Green header background color */
            color: white;
        }

        .results-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            font-style: italic;

        }
    </style>
</head>
<body>
    @if(count($registered_courses['registered_courses']) > 0)
    <div class="watermark">E.H.I.S.T</div>

<table class="header">
    <tr>
        <td class="left">
            <p>
                EBENEZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
            </p>
        </td>
        <td class="middle">
            <img src="{{ public_path('images/logo.png') }}" alt="School Logo" style="margin-top: 20px" />
        </td>
        <td class="right">
            <p>BAMENDA - SONAC STREET </p>
            <p><EMAIL></p>
            <p>+237 67833250</p>
        </td>
    </tr>

    {{-- thick horizontal line --}}
    <tr>
        <td colspan="3">
            <hr>
        </td>
    </tr>
    <tr>
        <td class="left" colspan="2">
            <p>School: {{ strtoupper($registered_courses['registered_courses']['student']['department']) }}</p>
            <p>Program: {{ strtoupper($registered_courses['registered_courses']['student']['school']) }}</p>
            <p>Name: {{ $registered_courses['registered_courses']['student']['first_name'] . " " . $registered_courses['registered_courses']['student']['last_name'] }}</p>
            <p>Matricule: {{ $registered_courses['registered_courses']['student']['matricule'] }}</p>
        </td>

        <td class="right">
            <p>Level: {{ $registered_courses['registered_courses']['student']['level']}}</p>
            <p>Academic year: {{ str_replace('_', '/', $registered_courses['registered_courses']['student']['academic_year']) }} </p>

        </td>
    </tr>

    </table>

    <div class="results-header">
        Form B - Registered Courses
    </div>
    <table>
        <thead>
            <tr>
                <th>Course Name</th>
                <th>Course Code</th>
                <th>Credit Value</th>
                <th>Lecturer</th>
            </tr>
        </thead>
        <tbody>
            @foreach($registered_courses['registered_courses']['courses'] as $course)
            <tr>
                <td>{{ $course['name'] }}</td>
                <td>{{ $course['code'] }}</td>
                <td>{{ $course['credit_value'] }}</td>
                <td>{{ $course['lecturer'] }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <table class="footer">
        <tr>
            <td><strong>Total Credits:</strong> {{ $registered_courses['registered_courses']['stats']['total_credits'] }}</td>
        </tr>
    </table>

    @else

    <div class="results-header">
        Form B - Registered Courses
    </div>
    <table>
        <thead>
            <tr>
                <th>Course Name</th>
                <th>Course Code</th>
                <th>Credit Value</th>
                <th>Lecturer</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>No courses registered</td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>

    @endif
</body>
</html>
