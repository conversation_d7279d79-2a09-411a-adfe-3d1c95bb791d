<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            position: relative; /* Required for absolute positioning of the watermark */
        }
        .watermark {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-45%, -55%) rotate(-45deg); /* Rotate the watermark */
            font-size: 150px; /* Adjust font size as needed */
            color: rgba(94, 126, 111, 0.1); /* Light color for watermark */
            z-index: -1; /* Send it to the back */
            white-space: nowrap; /* Prevent wrapping */
            font-weight: bold; /* Make the watermark bold */
        }
        .header {
            width: 100%;
            background-color: white;
            color: black;
            border-collapse: collapse;
        }
        .header td {
            padding: 10px;
            vertical-align: top;
            border: none;
        }
        .header .left, .header .right {
            width: 220px;
        }
        .header .middle {
            text-align: center;
        }
        .header img {
            max-height: 95px;
            max-width: 95px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        .results-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            font-style: italic;
        }

        .footer {
            margin-top: 20px;
            border-top: 1px solid #dddddd;
            padding-top: 10px;
        }
    </style>
</head>

<body>
    <table class="header">

        <tr>
            <td class="left">
                <p>
                    EBENEZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
                </p>
            </td>
            <td class="middle">
                <img src="{{ public_path('images/logo.png') }}" alt="School Logo" style="margin-top: 20px" />
            </td>
            <td class="right">
                <p>BAMENDA - SONAC STREET </p>
                <p>info@ebenezerinst.com4</p>
                <p>+237 67833250</p>
            </td>
        </tr>

        {{-- thick horizontal line --}}
        <tr>
            <td colspan="3">
                <hr>
            </td>
        </tr>

        <tr>
            {{-- check if results exists first --}}
            @if (count($data['results']) > 0)
            <td class="left" colspan="2">
                <p>School: {{ $data['results'][0]->student->department->school->name }}</p>
                <p>Program: {{ $data['results'][0]->student->program->name }}</p>
                <p>Name: {{ $data['results'][0]->student->user->first_name }}
                    {{ $data['results'][0]->student->user->last_name }}</p>
                <p>Matricule: {{ $data['results'][0]->student->matricule }}</p>
            </td>
            <td class="right">
                <p>Level: {{ $data['results'][0]->student->level->name }}</p>
                <p>Academic year: {{ $data['academicYear']->name }} </p>
            </td>
            @endif
        </tr>
    </table>

    <div class="results-header">
        <p> {{ $data['semester'] }} Semester Results</p>
    </div>

    <table class="results-table">
        <thead>
            <tr>
                <th>Course Code</th>
                <th>Course Name</th>
                <th>CA Mark</th>
                <th>Exam Mark</th>
                <th>Total Mark</th>
                <th>Grade</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($data['results'] as $result)
                <tr>
                    <td>{{ $result->course->code }}</td>
                    <td>{{ $result->course->name }}</td>
                    <td>{{ $result->ca_mark }}</td>
                    <td>{{ $result->exam_mark }}</td>
                    <td>{{ $result->total_mark }}</td>
                    <td>{{ $result->grade }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="footer">
        <tr>
            <td><strong>Credit Attempted:</strong> {{ $data['total_credits'] }}</td>
            <td><strong>Credit Gotten:</strong> {{ $data['gotten_credits'] }}</td>
            <td><strong>GPA:</strong> {{ number_format($data['gpa'], 2) }}</td>
        </tr>
    </table>
</body>

</html>
