<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Student;
use App\Models\AcademicYear;
use App\Models\Level;
use App\Models\SystemSetting;
use Carbon\Carbon;

class UpdateSystemSettings extends Command
{
    protected $signature = 'system:update-settings';
    protected $description = 'Update system settings such as current semester, academic year, and student levels based on time intervals';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Update the current semester based on the date
        $this->updateCurrentSemester();

        // Update students' current levels based on their time in the program
        $this->updateStudentLevels();

        // Update the current academic year
        $this->updateAcademicYear();

        $this->info('System settings have been updated successfully.');
    }

    protected function updateCurrentSemester()
    {
        $today = Carbon::today();

        // Fetch the current academic year based on today's date
        $currentAcademicYear = AcademicYear::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->first();

        if (!$currentAcademicYear) {
            $this->error('No active academic year found. Unable to update semester.');
            return;
        }

        // Convert start and end dates to Carbon instances
        $semester1Start = Carbon::parse($currentAcademicYear->start_date);
        $semester1End = $semester1Start->copy()->addMonths(5);
        $semester2Start = $semester1End->copy()->addDay();
        $semester2End = $semester2Start->copy()->addMonths(5);
        $resitStart = $semester2End->copy()->addDay();
        $resitEnd = Carbon::parse($currentAcademicYear->end_date);

        // Determine the current semester based on today’s date
        if ($today->between($semester1Start, $semester1End)) {
            $currentSemester = 1;
        } elseif ($today->between($semester2Start, $semester2End)) {
            $currentSemester = 2;
        } elseif ($today->between($resitStart, $resitEnd)) {
            $currentSemester = 3;
        } else {
            $this->error('Unable to determine the current semester.');
            return;
        }

        // Update the system setting for current semester
        SystemSetting::updateOrCreate(
            ['key' => 'current_semester'],
            ['value' => $currentSemester]
        );

        $this->info('Current semester updated to: ' . $currentSemester);
    }

    protected function updateStudentLevels()
    {
        // Fetch all students with their program information
        $students = Student::with('program')->get();

        foreach ($students as $student) {
            // Parse admission date and calculate months since admission
            $admissionDate = Carbon::parse($student->date_of_admission);
            $monthsSinceAdmission = abs(Carbon::now()->diffInMonths($admissionDate));

            // Calculate level increment: assume each level increment is every 10 months
            $levelIncrement = floor($monthsSinceAdmission / 10);

            // Base level is 200 (starting point)
            // Ensure target level does not go below 200
            if ($levelIncrement < 0) {
                continue; // Skip if negative level increment (should not happen)
            }


            // Calculate target level based on time in the program
            $targetLevelName = 200 + ($levelIncrement * 100);

            // Ensure target level does not exceed the program's duration
            $maxLevelName = 200 + (($student->program->duration - 1) * 100);
            
            if ($targetLevelName > $maxLevelName) {
                $targetLevelName = $maxLevelName;
            }

            // Find the target level in the database
            $targetLevel = Level::where('name', $targetLevelName)->first();
            if (!$targetLevel) {
                $this->error('Level ' . $targetLevelName . ' not found in the levels table.');
                continue;
            }

            // Update the student's current level if it's different from the target level
            if ($student->current_level_id != $targetLevel->id) {
                $student->update(['current_level_id' => $targetLevel->id]);
                $this->info('Updated level for student ID ' . $student->id . ' to level ' . $targetLevelName);
            }
        }
    }

    protected function updateAcademicYear()
    {
        $today = Carbon::today();

        // Find the current academic year based on today's date
        $currentAcademicYear = AcademicYear::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->first();

        if ($currentAcademicYear) {
            // Update system setting for current academic year ID
            SystemSetting::updateOrCreate(
                ['key' => 'current_academic_year_id'],
                ['value' => $currentAcademicYear->id]
            );

            // Update each student's academic_year_id to the current academic year ID
            Student::query()->update(['academic_year_id' => $currentAcademicYear->id]);

            // Log information about updating students' academic years
            $this->info('Updated all students to academic year: ' . $currentAcademicYear->name);

            // Log information about updating system settings
            SystemSetting::updateOrCreate(
                ['key' => 'current_academic_year_name'],
                ['value' => $currentAcademicYear->name]
            );

            return;

        } else {
           // Error message if no active academic year found.
           return;
       }
   }
}
