<?php

namespace App\Http\Controllers\Students;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Imports\UserImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportStudentsControler extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new UserImport ('student'), $request->file('file'));
            return $this->success('Schools imported successfully', null, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to import schools', 'details' => $e->getMessage()], 500);
        }
    }
}
