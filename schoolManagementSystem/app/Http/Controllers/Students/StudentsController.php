<?php

namespace App\Http\Controllers\Students;

use App\Http\Controllers\BaseController;
use App\Http\Requests\StudentRequest;
use App\Http\Requests\UpdateAcademicYearAndLevelRequest;
use App\Http\Resources\StudentResource;
use App\Http\Resources\UserResource;
use App\Repositories\StudentRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class StudentsController extends BaseController
{
    private $studentRepository;
    private $userRepository;
    public function __construct(StudentRepository $studentRepository, UserRepository $userRepository)
    {
        $this->studentRepository = $studentRepository;
        $this->userRepository = $userRepository;
    }

    public function index(Request $request)
    {
        $students = StudentResource::collection($this->studentRepository->all());
        return $this->success('Students fetched successfully', $students);
    }

    public function show($id)
    {
        $student = $this->studentRepository->findById($id);
        return $this->success('Student fetched successfully', StudentResource::make($student));
    }

    public function store(StudentRequest $request)
    {
        $data = $request->all();
        try {
            $student = $this->userRepository->create($data, 'student');
            $student->load('student.department', 'student.level');
            return $this->success('Student created successfully', UserResource::make($student));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 500);
        }

    }

    public function updateStudent(Request $request, $id)
    {
        $data = $request->all();
        try
        {
        $student = $this->studentRepository->updateUserAccount($data, $id);
        return $this->success('Student updated successfully', StudentResource::make($student));
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function updateUserAccount(Request $request, $id)
    {
        $data = $request->all();
        $student = $this->studentRepository->updateUserAccount($data, $id);
        return $this->success('Student updated successfully', StudentResource::make($student));
    }

    public function destroy($id)
    {
        $student = $this->studentRepository->findById($id);
        if (!$student) {
            return $this->notFound('Student');
        }
        $this->studentRepository->delete($id);
        return $this->success('Student deleted successfully');
    }

    public function search()
    {
        $searchQuery = request('q');
        return $this->success('Students fetched successfully', StudentResource::collection($this->studentRepository->search($searchQuery)));
    }

    public function findByMatricule($matricule)
    {
        return $this->success("Students by academic year", UserResource::collection($this->studentRepository->findByMatricule($matricule)));
    }

    public function updateLevelAndAcademicYear(UpdateAcademicYearAndLevelRequest $request)
    {
        $data = $request->validated();
        $student = $this->studentRepository->updateAcaemdicYearAndLevel($data);
        $student->load('user', 'level');
        return $this->success('Student updated successfully', StudentResource::make($student));
    }

}
