<?php

namespace App\Http\Controllers\Api\Tutors;

use App\Http\Controllers\BaseController;
use App\Http\Requests\LecturerRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Resources\LecturerResource;
use App\Repositories\LecturerRepository;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Log;

class LecturerController extends BaseController
{
    private $lecturerRepository;

    public function __construct(LecturerRepository $lecturerRepository)
    {
        $this->lecturerRepository = $lecturerRepository;
    }

    public function index()
    {
        return $this->success("fetched", LecturerResource::collection($this->lecturerRepository->all()));
    }

    public function show($id)
    {

        $lecturer = $this->lecturerRepository->findById($id);
        if (!$lecturer) {
            return $this->notFound('Lecturer');
        }

        return $this->success("feteched", new LecturerResource($this->lecturerRepository->findById($id)));
    }

    public function store(LecturerRequest $request)
    {

        $request->merge(['admin_id' => FacadesAuth::id()]);
        try {

            if($request->hasFile('profile_image')) {
                $path = $this->uploadFile($request->file('profile_image'), 'lecturers');
                if (!$path) {
                    return $this->internalServerError("Could not upload profile image");
                }

                $request->merge(['profile' => $path]);

            }
            $lecturer = $this->lecturerRepository->create($request->all());
            return $this->success('Lecturer created successfully', LecturerResource::make($lecturer));
        } catch (\Exception $e) {
            Log::error('Error creating lecturer: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function update(UpdateProfileRequest $request, $id)
    {
        $data = $request->validated();


        $lecturer = $this->lecturerRepository->findById($id);
        if (!$lecturer) {
            return $this->notFound('Lecturer');
        }

        if ($request->hasFile('profile_image')) {
            $path = $this->uploadFile($request->file('profile_image'), 'lecturers');
            if (!$path) {
                return $this->internalServerError("Could not upload profile image");
            }

            $data['profile'] = $path;
        }

        $request->merge(['admin_id' => FacadesAuth::id()]);

        $this->lecturerRepository->update($data, $id);

        return $this->success('Lecturer updated successfully', LecturerResource::make($this->lecturerRepository->findById($id)));

    }

    public function destroy($id)
    {
        $lecturer = $this->lecturerRepository->findById($id);
        if (!$lecturer) {
            return $this->notFound('Lecturer');
        }
        $this->lecturerRepository->delete($id);
        return $this->success('Lecturer deleted successfully');
    }
}
