<?php

namespace App\Http\Controllers\Api\Tutors;

use App\Http\Controllers\BaseController;
use App\Imports\UserImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportLecturerController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new UserImport ('lecturer'), $request->file('file'));
            return $this->success('Lecturers imported successfully', null, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to import schools', 'details' => $e->getMessage()], 500);
        }
    }
}
