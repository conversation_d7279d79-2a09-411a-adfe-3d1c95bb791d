<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Assignment;
use App\Models\Course;
use App\Models\Lecturer;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LecturerController extends Controller
{
    /**
     * Get lecturer's courses
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCourses(Request $request)
    {
        try {
            $lecturer = Auth::user();
            
            if (!$lecturer || $lecturer->role !== 'lecturer') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $courses = Course::where('lecturer_id', $lecturer->id)
                ->with(['department', 'level', 'students'])
                ->get()
                ->map(function ($course) {
                    return [
                        'id' => $course->id,
                        'name' => $course->name,
                        'code' => $course->code,
                        'description' => $course->description,
                        'credit_hours' => $course->credit_hours,
                        'department' => $course->department ? [
                            'id' => $course->department->id,
                            'name' => $course->department->name,
                        ] : null,
                        'level' => $course->level ? [
                            'id' => $course->level->id,
                            'name' => $course->level->name,
                        ] : null,
                        'students_count' => $course->students->count(),
                        'assignments_count' => $course->assignments()->count(),
                        'created_at' => $course->created_at,
                        'updated_at' => $course->updated_at,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $courses
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Get lecturer courses error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch courses',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get lecturer statistics
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            $lecturer = Auth::user();
            
            if (!$lecturer || $lecturer->role !== 'lecturer') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            // Get lecturer's courses
            $courses = Course::where('lecturer_id', $lecturer->id)->get();
            $courseIds = $courses->pluck('id');

            // Calculate statistics
            $totalCourses = $courses->count();
            $totalStudents = DB::table('course_student')
                ->whereIn('course_id', $courseIds)
                ->distinct('student_id')
                ->count();

            $activeAssignments = Assignment::whereIn('course_id', $courseIds)
                ->where('status', 'active')
                ->where('due_date', '>=', now())
                ->count();

            $pendingGrading = DB::table('assignment_submissions')
                ->join('assignments', 'assignment_submissions.assignment_id', '=', 'assignments.id')
                ->whereIn('assignments.course_id', $courseIds)
                ->where('assignment_submissions.status', 'submitted')
                ->whereNull('assignment_submissions.grade')
                ->count();

            $recentSubmissions = DB::table('assignment_submissions')
                ->join('assignments', 'assignment_submissions.assignment_id', '=', 'assignments.id')
                ->whereIn('assignments.course_id', $courseIds)
                ->where('assignment_submissions.created_at', '>=', now()->subDays(7))
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_courses' => $totalCourses,
                    'total_students' => $totalStudents,
                    'active_assignments' => $activeAssignments,
                    'pending_grading' => $pendingGrading,
                    'recent_submissions' => $recentSubmissions,
                    'courses_this_semester' => $totalCourses, // Can be refined based on semester logic
                ]
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Get lecturer stats error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get lecturer profile
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfile(Request $request)
    {
        try {
            $lecturer = Auth::user();
            
            if (!$lecturer || $lecturer->role !== 'lecturer') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $lecturerData = [
                'id' => $lecturer->id,
                'first_name' => $lecturer->first_name,
                'last_name' => $lecturer->last_name,
                'email' => $lecturer->email,
                'phone' => $lecturer->phone,
                'employee_id' => $lecturer->employee_id,
                'department_id' => $lecturer->department_id,
                'department' => $lecturer->department ? [
                    'id' => $lecturer->department->id,
                    'name' => $lecturer->department->name,
                ] : null,
                'profile_image' => $lecturer->profile_image,
                'bio' => $lecturer->bio,
                'qualifications' => $lecturer->qualifications,
                'specialization' => $lecturer->specialization,
                'is_active' => $lecturer->is_active,
                'created_at' => $lecturer->created_at,
                'updated_at' => $lecturer->updated_at,
            ];

            return response()->json([
                'success' => true,
                'data' => $lecturerData
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Get lecturer profile error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch profile',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Update lecturer profile
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        try {
            $lecturer = Auth::user();
            
            if (!$lecturer || $lecturer->role !== 'lecturer') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $validatedData = $request->validate([
                'first_name' => 'sometimes|string|max:255',
                'last_name' => 'sometimes|string|max:255',
                'phone' => 'sometimes|string|max:20',
                'bio' => 'sometimes|string|max:1000',
                'qualifications' => 'sometimes|string|max:500',
                'specialization' => 'sometimes|string|max:255',
            ]);

            $lecturer->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => $lecturer->fresh()
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Update lecturer profile error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
