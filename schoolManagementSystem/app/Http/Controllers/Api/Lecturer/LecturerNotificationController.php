<?php

namespace App\Http\Controllers\Api\Lecturer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Notification;

class LecturerNotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('role:lecturer');
    }

    public function index(Request $request)
    {
        try {
            $lecturer = Auth::user();
            
            $query = Notification::where('user_id', $lecturer->id);
            
            // Apply filters
            if ($request->has('read')) {
                $isRead = $request->boolean('read');
                if ($isRead) {
                    $query->whereNotNull('read_at');
                } else {
                    $query->whereNull('read_at');
                }
            }
            
            if ($request->has('type')) {
                $query->where('type', $request->get('type'));
            }
            
            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 15));
            
            return response()->json([
                'status' => 'success',
                'data' => $notifications
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function markAsRead($id)
    {
        try {
            $lecturer = Auth::user();
            
            $notification = Notification::where('user_id', $lecturer->id)
                ->findOrFail($id);
            
            $notification->update([
                'read_at' => now()
            ]);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Notification marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark notification as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function markAllAsRead()
    {
        try {
            $lecturer = Auth::user();
            
            Notification::where('user_id', $lecturer->id)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
            
            return response()->json([
                'status' => 'success',
                'message' => 'All notifications marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark all notifications as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getUnreadCount()
    {
        try {
            $lecturer = Auth::user();
            
            $count = Notification::where('user_id', $lecturer->id)
                ->whereNull('read_at')
                ->count();
            
            return response()->json([
                'status' => 'success',
                'data' => ['unread_count' => $count]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get unread count',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
