<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class FileManagementController extends BaseController
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required|array|max:10',
            'files.*' => 'required|file|max:51200', // 50MB max per file
            'directory' => 'required|string|max:255',
            'type' => 'nullable|in:documents,images,videos,audio',
            'optimize_images' => 'boolean',
            'preserve_name' => 'boolean',
            'max_width' => 'nullable|integer|min:100|max:4000',
            'max_height' => 'nullable|integer|min:100|max:4000',
            'image_quality' => 'nullable|integer|min:10|max:100',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $options = [
                'optimize_images' => $request->boolean('optimize_images', true),
                'preserve_name' => $request->boolean('preserve_name', false),
                'max_width' => $request->input('max_width', 1920),
                'max_height' => $request->input('max_height', 1080),
                'image_quality' => $request->input('image_quality', 85),
                'prefix' => auth()->id() . '_',
            ];

            // Set allowed types based on request type
            if ($request->has('type')) {
                $allowedTypes = $this->getAllowedMimeTypes($request->type);
                if ($allowedTypes) {
                    $options['allowed_types'] = $allowedTypes;
                }
            }

            $uploadedFiles = $this->fileUploadService->uploadMultipleFiles(
                $request->file('files'),
                $request->directory,
                $options
            );

            return $this->success('Files uploaded successfully', $uploadedFiles);

        } catch (\Exception $e) {
            return $this->error('Upload failed: ' . $e->getMessage(), 500);
        }
    }

    public function uploadSingle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:51200', // 50MB max
            'directory' => 'required|string|max:255',
            'type' => 'nullable|in:documents,images,videos,audio',
            'optimize_images' => 'boolean',
            'preserve_name' => 'boolean',
            'max_width' => 'nullable|integer|min:100|max:4000',
            'max_height' => 'nullable|integer|min:100|max:4000',
            'image_quality' => 'nullable|integer|min:10|max:100',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $options = [
                'optimize_images' => $request->boolean('optimize_images', true),
                'preserve_name' => $request->boolean('preserve_name', false),
                'max_width' => $request->input('max_width', 1920),
                'max_height' => $request->input('max_height', 1080),
                'image_quality' => $request->input('image_quality', 85),
                'prefix' => auth()->id() . '_',
            ];

            // Set allowed types based on request type
            if ($request->has('type')) {
                $allowedTypes = $this->getAllowedMimeTypes($request->type);
                if ($allowedTypes) {
                    $options['allowed_types'] = $allowedTypes;
                }
            }

            $uploadedFile = $this->fileUploadService->uploadFile(
                $request->file('file'),
                $request->directory,
                $options
            );

            return $this->success('File uploaded successfully', $uploadedFile);

        } catch (\Exception $e) {
            return $this->error('Upload failed: ' . $e->getMessage(), 500);
        }
    }

    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'nullable|in:public,private',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $deleted = $this->fileUploadService->deleteFile($request->path, $disk);

            if ($deleted) {
                return $this->success('File deleted successfully');
            } else {
                return $this->error('File not found or could not be deleted', 404);
            }

        } catch (\Exception $e) {
            return $this->error('Delete failed: ' . $e->getMessage(), 500);
        }
    }

    public function deleteMultiple(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'paths' => 'required|array|max:50',
            'paths.*' => 'required|string',
            'disk' => 'nullable|in:public,private',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $deleted = $this->fileUploadService->deleteMultipleFiles($request->paths, $disk);

            return $this->success('Files deleted successfully', ['deleted_count' => count($deleted)]);

        } catch (\Exception $e) {
            return $this->error('Delete failed: ' . $e->getMessage(), 500);
        }
    }

    public function download(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'nullable|in:public,private',
            'name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $name = $request->input('name');

            if (!$this->fileUploadService->fileExists($request->path, $disk)) {
                return $this->error('File not found', 404);
            }

            return $this->fileUploadService->downloadFile($request->path, $name, $disk);

        } catch (\Exception $e) {
            return $this->error('Download failed: ' . $e->getMessage(), 500);
        }
    }

    public function getFileInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'nullable|in:public,private',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $fileInfo = $this->fileUploadService->getFileInfo($request->path, $disk);

            if (!$fileInfo) {
                return $this->error('File not found', 404);
            }

            return $this->success('File info retrieved successfully', $fileInfo);

        } catch (\Exception $e) {
            return $this->error('Failed to get file info: ' . $e->getMessage(), 500);
        }
    }

    public function createThumbnail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'width' => 'nullable|integer|min:50|max:1000',
            'height' => 'nullable|integer|min:50|max:1000',
            'disk' => 'nullable|in:public,private',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $width = $request->input('width', 300);
            $height = $request->input('height', 300);

            $thumbnailPath = $this->fileUploadService->createThumbnail(
                $request->path,
                $width,
                $height,
                $disk
            );

            return $this->success('Thumbnail created successfully', [
                'thumbnail_path' => $thumbnailPath,
                'thumbnail_url' => $this->fileUploadService->getFileUrl($thumbnailPath, $disk)
            ]);

        } catch (\Exception $e) {
            return $this->error('Thumbnail creation failed: ' . $e->getMessage(), 500);
        }
    }

    public function listFiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'directory' => 'required|string',
            'disk' => 'nullable|in:public,private',
            'recursive' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        try {
            $disk = $request->input('disk', 'public');
            $directory = $request->directory;
            $recursive = $request->boolean('recursive', false);

            $storage = Storage::disk($disk);
            $files = $recursive ? $storage->allFiles($directory) : $storage->files($directory);

            $fileList = collect($files)->map(function ($file) use ($storage, $disk) {
                return [
                    'path' => $file,
                    'name' => basename($file),
                    'size' => $storage->size($file),
                    'last_modified' => $storage->lastModified($file),
                    'url' => $this->fileUploadService->getFileUrl($file, $disk),
                    'mime_type' => $storage->mimeType($file),
                ];
            });

            return $this->success('Files listed successfully', $fileList);

        } catch (\Exception $e) {
            return $this->error('Failed to list files: ' . $e->getMessage(), 500);
        }
    }

    protected function getAllowedMimeTypes($type)
    {
        $mimeTypes = [
            'documents' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
                'text/csv',
            ],
            'images' => [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/svg+xml',
            ],
            'videos' => [
                'video/mp4',
                'video/avi',
                'video/quicktime',
                'video/x-msvideo',
            ],
            'audio' => [
                'audio/mpeg',
                'audio/wav',
                'audio/ogg',
                'audio/mp4',
            ],
        ];

        return $mimeTypes[$type] ?? null;
    }
}
