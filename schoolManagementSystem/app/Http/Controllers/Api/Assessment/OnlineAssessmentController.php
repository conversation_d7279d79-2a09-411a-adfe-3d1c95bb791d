<?php

namespace App\Http\Controllers\Api\Assessment;

use App\Http\Controllers\BaseController;
use App\Models\OnlineAssessment;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OnlineAssessmentController extends BaseController
{
    public function index(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);
        
        $query = $course->hasMany(OnlineAssessment::class, 'course_id')
            ->with(['createdBy:id,first_name,last_name']);
        
        // Filter by type
        if ($request->has('type')) {
            $query->byType($request->type);
        }
        
        // Filter by status for students
        if (auth()->user()->hasRole('student')) {
            $query->published();
            
            if ($request->has('status')) {
                switch ($request->status) {
                    case 'active':
                        $query->active();
                        break;
                    case 'upcoming':
                        $query->upcoming();
                        break;
                    case 'expired':
                        $query->expired();
                        break;
                }
            }
        }

        $assessments = $query->orderBy('start_time', 'desc')->get();

        // Add attempt information for students
        if (auth()->user()->hasRole('student')) {
            $studentId = auth()->user()->student->id;
            $assessments->each(function ($assessment) use ($studentId) {
                $attempts = $assessment->getStudentAttempts($studentId);
                $assessment->student_attempts = $attempts;
                $assessment->remaining_attempts = $assessment->getRemainingAttempts($studentId);
                $assessment->can_take = $assessment->canTakeAssessment($studentId);
                $assessment->latest_attempt = $assessment->getLatestAttempt($studentId);
            });
        }

        return $this->success('Assessments retrieved successfully', $assessments);
    }

    public function store(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'type' => 'required|in:ca,exam,quiz,test,midterm,final',
            'total_marks' => 'required|integer|min:1|max:1000',
            'duration_minutes' => 'nullable|integer|min:1|max:600',
            'start_time' => 'required|date|after_or_equal:now',
            'end_time' => 'required|date|after:start_time',
            'shuffle_questions' => 'boolean',
            'show_results_immediately' => 'boolean',
            'allow_review' => 'boolean',
            'max_attempts' => 'required|integer|min:1|max:10',
            'require_password' => 'boolean',
            'password' => 'nullable|string|min:4|max:50',
            'academic_year_id' => 'required|exists:academic_years,id',
            'level_id' => 'required|exists:levels,id'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $data = $validator->validated();
        $data['course_id'] = $courseId;
        $data['created_by'] = auth()->id();

        $assessment = OnlineAssessment::create($data);
        $assessment->load('createdBy:id,first_name,last_name');

        return $this->success('Assessment created successfully', $assessment, 201);
    }

    public function show($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->with(['createdBy:id,first_name,last_name', 'course:id,name,code'])
            ->firstOrFail();

        // Add attempt information for students
        if (auth()->user()->hasRole('student')) {
            $studentId = auth()->user()->student->id;
            $attempts = $assessment->getStudentAttempts($studentId);
            $assessment->student_attempts = $attempts;
            $assessment->remaining_attempts = $assessment->getRemainingAttempts($studentId);
            $assessment->can_take = $assessment->canTakeAssessment($studentId);
            $assessment->latest_attempt = $assessment->getLatestAttempt($studentId);
        }

        return $this->success('Assessment retrieved successfully', $assessment);
    }

    public function update(Request $request, $courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'total_marks' => 'integer|min:1|max:1000',
            'duration_minutes' => 'nullable|integer|min:1|max:600',
            'start_time' => 'date',
            'end_time' => 'date|after:start_time',
            'shuffle_questions' => 'boolean',
            'show_results_immediately' => 'boolean',
            'allow_review' => 'boolean',
            'max_attempts' => 'integer|min:1|max:10',
            'is_published' => 'boolean',
            'require_password' => 'boolean',
            'password' => 'nullable|string|min:4|max:50'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $assessment->update($validator->validated());
        $assessment->load('createdBy:id,first_name,last_name');

        return $this->success('Assessment updated successfully', $assessment);
    }

    public function destroy($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        // Check if there are attempts
        if ($assessment->attempts()->count() > 0) {
            return $this->error('Cannot delete assessment with existing attempts', 422);
        }

        $assessment->delete();

        return $this->success('Assessment deleted successfully');
    }

    public function publish($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        // Validate that assessment has questions
        if ($assessment->questions()->count() === 0) {
            return $this->error('Cannot publish assessment without questions', 422);
        }

        $assessment->update(['is_published' => true]);

        return $this->success('Assessment published successfully', $assessment);
    }

    public function unpublish($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        $assessment->update(['is_published' => false]);

        return $this->success('Assessment unpublished successfully', $assessment);
    }

    public function attempts($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        $attempts = $assessment->attempts()
            ->with(['student.user:id,first_name,last_name'])
            ->submitted()
            ->latest('submitted_at')
            ->get();

        return $this->success('Assessment attempts retrieved successfully', $attempts);
    }

    public function statistics($courseId, $assessmentId)
    {
        $assessment = OnlineAssessment::where('course_id', $courseId)
            ->where('id', $assessmentId)
            ->firstOrFail();

        $stats = [
            'total_attempts' => $assessment->attempt_count,
            'completed_attempts' => $assessment->completed_attempts,
            'average_score' => round($assessment->getAverageScore(), 2),
            'highest_score' => $assessment->getHighestScore(),
            'pass_rate' => $assessment->getPassRate(),
            'total_questions' => $assessment->total_questions,
        ];

        return $this->success('Assessment statistics retrieved successfully', $stats);
    }
}
