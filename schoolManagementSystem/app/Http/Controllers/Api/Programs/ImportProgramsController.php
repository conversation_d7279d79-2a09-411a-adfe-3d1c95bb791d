<?php

namespace App\Http\Controllers\Api\Programs;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Imports\ProgramsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportProgramsController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new ProgramsImport, $request->file('file'));
            return $this->success('Programs imported successfully', null, 200);
        } catch (\Exception $e) {
            return $this->internalServerError(exception: $e);
        }
    }
}
