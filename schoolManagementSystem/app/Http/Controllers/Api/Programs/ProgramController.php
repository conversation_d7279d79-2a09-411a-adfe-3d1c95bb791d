<?php

namespace App\Http\Controllers\Api\Programs;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Repositories\Interfaces\ProgramRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProgramController extends BaseController
{

    private $programRepository;

    public function __construct(ProgramRepositoryInterface $programRepository)
    {
        $this->programRepository = $programRepository;
    }
    public function index(Request $request)
    {
        return $this->success("programs fetched", $this->programRepository->all());
    }

    public function store(Request $request)
    {
        $validated = Validator::make($request->all(), [
            'name' => 'required',
            'abbreviation' => 'required',
            'cost' => 'required|numeric',
            'description' => 'nullable|string',
            'degree' => 'nullable|string',
            'duration' => 'required|numeric',
        ]);

        if ($validated->fails()) {
            return $this->success('Validation error', $validated->errors());
        }

        try {
            $program = $this->programRepository->createOrUpdate($request->all());
            return $this->success('Program created', $program);
        }catch (\Exception $exception) {
            return $this->internalServerError(exception: $exception);
        }
    }

    public function show($id){
        return $this->success('program fetched', $this->programRepository->getById($id));
    }

    public function destroy($id){
        $program = $this->programRepository->getById($id);
        if (empty($program)) {
            return $this->notFound('Program');
        }
        return $this->programRepository->delete($id) ? $this->success('Program deleted') : $this->error("Something went wrong");
    }
}
