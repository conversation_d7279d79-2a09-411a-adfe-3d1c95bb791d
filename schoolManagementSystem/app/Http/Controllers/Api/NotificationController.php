<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends BaseController
{
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = $user->notifications()->notExpired();
        
        // Filter by type
        if ($request->has('type')) {
            $query->byType($request->type);
        }
        
        // Filter by read status
        if ($request->has('unread_only') && $request->boolean('unread_only')) {
            $query->unread();
        }
        
        // Filter by priority
        if ($request->has('priority')) {
            $query->byPriority($request->priority);
        }
        
        $notifications = $query->latest()->paginate($request->input('per_page', 20));
        
        return $this->success('Notifications retrieved successfully', $notifications);
    }

    public function unreadCount()
    {
        $count = auth()->user()->notifications()
            ->unread()
            ->notExpired()
            ->count();
            
        return $this->success('Unread count retrieved successfully', ['count' => $count]);
    }

    public function markAsRead(Request $request, $notificationId)
    {
        $notification = auth()->user()->notifications()
            ->where('id', $notificationId)
            ->firstOrFail();
            
        $notification->markAsRead();
        
        return $this->success('Notification marked as read', $notification);
    }

    public function markAllAsRead(Request $request)
    {
        $user = auth()->user();
        
        $updated = $user->notifications()
            ->unread()
            ->notExpired()
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);
            
        return $this->success('All notifications marked as read', ['updated_count' => $updated]);
    }

    public function markMultipleAsRead(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'required|integer|exists:notifications,id'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $user = auth()->user();
        
        $updated = $user->notifications()
            ->whereIn('id', $request->notification_ids)
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);
            
        return $this->success('Notifications marked as read', ['updated_count' => $updated]);
    }

    public function delete(Request $request, $notificationId)
    {
        $notification = auth()->user()->notifications()
            ->where('id', $notificationId)
            ->firstOrFail();
            
        $notification->delete();
        
        return $this->success('Notification deleted successfully');
    }

    public function deleteMultiple(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'required|integer|exists:notifications,id'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $user = auth()->user();
        
        $deleted = $user->notifications()
            ->whereIn('id', $request->notification_ids)
            ->delete();
            
        return $this->success('Notifications deleted successfully', ['deleted_count' => $deleted]);
    }

    public function deleteAllRead(Request $request)
    {
        $user = auth()->user();
        
        $deleted = $user->notifications()
            ->read()
            ->delete();
            
        return $this->success('All read notifications deleted', ['deleted_count' => $deleted]);
    }

    public function getByType(Request $request, $type)
    {
        $user = auth()->user();
        
        $notifications = $user->notifications()
            ->byType($type)
            ->notExpired()
            ->latest()
            ->paginate($request->input('per_page', 20));
            
        return $this->success("Notifications of type '{$type}' retrieved successfully", $notifications);
    }

    public function getStats()
    {
        $user = auth()->user();
        
        $stats = [
            'total' => $user->notifications()->notExpired()->count(),
            'unread' => $user->notifications()->unread()->notExpired()->count(),
            'by_type' => $user->notifications()
                ->notExpired()
                ->selectRaw('type, COUNT(*) as count, SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count')
                ->groupBy('type')
                ->get()
                ->keyBy('type'),
            'by_priority' => $user->notifications()
                ->notExpired()
                ->selectRaw('priority, COUNT(*) as count, SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count')
                ->groupBy('priority')
                ->get()
                ->keyBy('priority'),
        ];
        
        return $this->success('Notification statistics retrieved successfully', $stats);
    }

    public function create(Request $request)
    {
        // Only allow admins and lecturers to create notifications
        if (!auth()->user()->hasRole(['admin', 'lecturer'])) {
            return $this->error('Unauthorized', 403);
        }

        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'required|integer|exists:users,id',
            'type' => 'required|string|max:50',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'action_url' => 'nullable|string|max:500',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'expires_at' => 'nullable|date|after:now',
            'data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $created = Notification::createForMultipleUsers(
            $request->user_ids,
            $request->type,
            $request->title,
            $request->message,
            $request->data,
            $request->action_url,
            $request->input('priority', 'medium')
        );

        return $this->success('Notifications created successfully', [
            'created_count' => count($request->user_ids)
        ]);
    }

    public function getRecent(Request $request)
    {
        $user = auth()->user();
        $limit = $request->input('limit', 10);
        
        $notifications = $user->notifications()
            ->notExpired()
            ->latest()
            ->limit($limit)
            ->get();
            
        return $this->success('Recent notifications retrieved successfully', $notifications);
    }
}
