<?php

namespace App\Http\Controllers\Api\Students;

use App\Http\Controllers\BaseController;
use App\Http\Requests\CourseRegistrationRequest;
use App\Repositories\CourseRegistrationRepository;
use App\Models\Course; // Import Course model
use App\Models\Student; // Import Student model
use App\Http\Resources\RegisteredCoursesResources;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CourseRegistrationController extends BaseController
{
    private $courseRegistrationRepository;

    public function __construct(CourseRegistrationRepository $courseRegistrationRepository)
    {
        $this->courseRegistrationRepository = $courseRegistrationRepository;
    }

    public function registerCourse(CourseRegistrationRequest $request)
    {
        try {
            $student = Student::findOrFail($request->input('student_id'));

            if ($request->has('course_ids')) {
                return $this->registerMultipleCourses($request);
            }

            $course = Course::findOrFail($request->input('course_id'));

            if (!$course || !$student) {
                return $this->notFound('Course or Student');
            }

            $registeredCourse = $this->courseRegistrationRepository->getRegisteredCourse(
                $student->id,
                $request->input('academic_year_id'),
                $request->input('semester'),
                $request->input('level_id'),
                $course->id
            );

            if ($registeredCourse) {
                return $this->error('Course already registered');
            }

            $this->courseRegistrationRepository->registerCourse(
                $student->id,
                $request->input('academic_year_id'),
                $request->input('level_id'),
                $request->input('semester'),
                $course->id
            );

            return $this->success('Course registered successfully', $course);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    private function registerMultipleCourses(CourseRegistrationRequest $request)
    {
        $student = Student::findOrFail($request->input('student_id'));
        $courseIds = $request->input('course_ids');
        $academicYear = $request->input('academic_year_id');
        $levelId = $request->input('level_id');
        $semester = $request->input('semester');

        $courses = Course::whereIn('id', $courseIds)->get();

        if ($courses->count() !== count($courseIds)) {
            return $this->error('Some courses not found');
        }

        foreach ($courses as $course) {
            $registeredCourse = $this->courseRegistrationRepository->getRegisteredCourse(
                $student->id,
                $academicYear,
                $semester,
                $levelId,
                $course->id
            );

            if (!$registeredCourse) {
                $this->courseRegistrationRepository->registerCourse(
                    $student->id,
                    $academicYear,
                    $semester,
                    $levelId,
                    $course->id
                );
            }
        }

        return $this->success('Courses registered successfully', $courses);
    }

    public function unregisterCourse(CourseRegistrationRequest $request)
    {
        try {
            $student = Student::find($request->input('student_id'));

            $course = Course::find($request->input('course_id'));

            if (!$course || !$student) {
                return $this->notFound('Course or student');
            }

            $registeredCourse = $this->courseRegistrationRepository->getRegisteredCourse($student->id, $request->input('academic_year_id'), $request->input('semester'), $request->input('level_id'), $course->id);

            if (!$registeredCourse) {
                return $this->error('Course not registered');
            }

            $this->courseRegistrationRepository->unregisterCourse($student->id,  $course->id);

            return $this->success('Course unregistered successfully', $course);
        } catch (\Exception $e) {
            return $this->internalServerError($e->getMessage());
        }
    }

    public function getRegisteredCourses(Request $request): \Illuminate\Http\JsonResponse
    {
        $validators = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'level_id' => 'required|exists:levels,id',
            'semester' => 'required',
            'academic_year_id' => 'required|exists:academic_years,id'
        ]);

        if($validators->fails()){
            return $this->validationError($validators->errors(), "validation error");
        }
        try {
            $student = Student::find($request->input('student_id'));

            if (!$student) {
                return $this->notFound('Student');
            }

            $registeredCourses = $this->courseRegistrationRepository->getRegisteredCourses($student->id, $request->input('academic_year_id'), $request->input('semester'), $request->input('level_id'));

            // dd($registeredCourses);
            return $this->success('Registered courses retrieved successfully', $registeredCourses);
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function downloadFormB(Request $request)
{
    // Validate the request input
    $validated = $request->validate([
        'student_id' => 'required|exists:students,id',
        'academic_year_id' => 'required|exists:academic_years,id',
        'semester' => 'required|string',
        'level_id' => 'required|integer|exists:levels,id'
    ]);

    try {


        // Retrieve the registered courses based on the provided criteria
        $registeredCourses = $this->courseRegistrationRepository->getRegisteredCourses(
            $validated['student_id'],
            $validated['academic_year_id'],
            $validated['semester'],
            $validated['level_id']
        );


        // Prepare data for the PDF view
        $data = [
            'registered_courses' => $registeredCourses,
        ];



        $student = Student::find($validated['student_id']);


        $pdf = Pdf::loadView('results.form_b', ["registered_courses" => $data]);

        return $pdf->stream($student->matricule . ' Form B.pdf');
    }catch(Exception $e){
        return $this->forbidden($e->getMessage());
    }
}


}
