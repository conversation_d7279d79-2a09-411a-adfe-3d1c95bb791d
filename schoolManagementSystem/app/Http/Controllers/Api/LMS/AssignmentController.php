<?php

namespace App\Http\Controllers\Api\LMS;

use App\Http\Controllers\BaseController;
use App\Models\Assignment;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AssignmentController extends BaseController
{
    public function index(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);

        $query = $course->assignments()->with(['createdBy:id,first_name,last_name']);

        // Filter by status for students
        if (auth()->user()->hasRole('student')) {
            $query->published();
        }

        // Filter by status
        if ($request->has('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active();
                    break;
                case 'overdue':
                    $query->overdue();
                    break;
                case 'upcoming':
                    $query->upcoming();
                    break;
            }
        }

        $assignments = $query->orderBy('due_date', 'asc')->get();

        // Add submission status for students
        if (auth()->user()->hasRole('student')) {
            $studentId = auth()->user()->student->id;
            $assignments->each(function ($assignment) use ($studentId) {
                $submission = $assignment->getSubmissionForStudent($studentId);
                $assignment->student_submission = $submission;
                $assignment->can_submit = $assignment->canSubmit($studentId);
            });
        }

        return $this->success('Assignments retrieved successfully', $assignments);
    }

    public function store(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructions' => 'nullable|string',
            'type' => 'required|in:assignment,project,essay,quiz,presentation,other',
            'max_score' => 'required|integer|min:1|max:1000',
            'weight_percentage' => 'required|integer|min:1|max:100',
            'start_date' => 'required|date|after_or_equal:today',
            'due_date' => 'required|date|after:start_date',
            'late_submission_deadline' => 'nullable|date|after:due_date',
            'allow_late_submission' => 'boolean',
            'late_penalty_percentage' => 'integer|min:0|max:100',
            'is_group_assignment' => 'boolean',
            'max_group_size' => 'nullable|integer|min:2|max:10',
            'allowed_file_types' => 'nullable|array',
            'max_file_size_mb' => 'integer|min:1|max:100',
            'max_files' => 'integer|min:1|max:10',
            'grading_rubric' => 'nullable|string',
            'academic_year_id' => 'required|exists:academic_years,id',
            'level_id' => 'required|exists:levels,id'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $data = $validator->validated();
        $data['course_id'] = $courseId;
        $data['created_by'] = auth()->id();

        $assignment = Assignment::create($data);
        $assignment->load('createdBy:id,first_name,last_name');

        return $this->success('Assignment created successfully', $assignment, 201);
    }

    public function show($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->with(['createdBy:id,first_name,last_name', 'course:id,name,code'])
            ->firstOrFail();

        // Add submission info for students
        if (auth()->user()->hasRole('student')) {
            $studentId = auth()->user()->student->id;
            $submission = $assignment->getSubmissionForStudent($studentId);
            $assignment->student_submission = $submission;
            $assignment->can_submit = $assignment->canSubmit($studentId);
        }

        return $this->success('Assignment retrieved successfully', $assignment);
    }

    public function update(Request $request, $courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'description' => 'string',
            'instructions' => 'nullable|string',
            'max_score' => 'integer|min:1|max:1000',
            'weight_percentage' => 'integer|min:1|max:100',
            'start_date' => 'date',
            'due_date' => 'date|after:start_date',
            'late_submission_deadline' => 'nullable|date|after:due_date',
            'allow_late_submission' => 'boolean',
            'late_penalty_percentage' => 'integer|min:0|max:100',
            'is_group_assignment' => 'boolean',
            'max_group_size' => 'nullable|integer|min:2|max:10',
            'allowed_file_types' => 'nullable|array',
            'max_file_size_mb' => 'integer|min:1|max:100',
            'max_files' => 'integer|min:1|max:10',
            'is_published' => 'boolean',
            'grading_rubric' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $assignment->update($validator->validated());
        $assignment->load('createdBy:id,first_name,last_name');

        return $this->success('Assignment updated successfully', $assignment);
    }

    public function destroy($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        // Check if there are submissions
        if ($assignment->submissions()->count() > 0) {
            return $this->error('Cannot delete assignment with existing submissions', 422);
        }

        $assignment->delete();

        return $this->success('Assignment deleted successfully');
    }

    public function publish($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $assignment->update(['is_published' => true]);

        return $this->success('Assignment published successfully', $assignment);
    }

    public function unpublish($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $assignment->update(['is_published' => false]);

        return $this->success('Assignment unpublished successfully', $assignment);
    }

    public function submissions($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $submissions = $assignment->submissions()
            ->with(['student.user:id,first_name,last_name', 'gradedBy:id,first_name,last_name'])
            ->submitted()
            ->latest('submitted_at')
            ->get();

        return $this->success('Assignment submissions retrieved successfully', $submissions);
    }

    public function statistics($courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $totalSubmissions = $assignment->submissions()->where('status', '!=', 'draft')->count();
        $gradedSubmissions = $assignment->submissions()->where('status', 'graded')->count();

        $stats = [
            'total_submissions' => $totalSubmissions,
            'graded_submissions' => $gradedSubmissions,
            'pending_grading' => $totalSubmissions - $gradedSubmissions,
            'average_score' => $assignment->submissions()
                ->where('status', 'graded')
                ->avg('score'),
            'submission_rate' => 0, // Will need to calculate based on enrolled students
        ];

        return $this->success('Assignment statistics retrieved successfully', $stats);
    }
}
