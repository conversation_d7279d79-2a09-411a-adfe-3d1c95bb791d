<?php

namespace App\Http\Controllers\Api\LMS;

use App\Http\Controllers\BaseController;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AssignmentSubmissionController extends BaseController
{
    public function store(Request $request, $courseId, $assignmentId)
    {
        $assignment = Assignment::where('course_id', $courseId)
            ->where('id', $assignmentId)
            ->firstOrFail();

        $studentId = auth()->user()->student->id;

        // Check if student can submit
        if (!$assignment->canSubmit($studentId)) {
            return $this->error('Submission not allowed for this assignment', 422);
        }

        $validator = Validator::make($request->all(), [
            'submission_text' => 'nullable|string',
            'files' => 'nullable|array|max:' . $assignment->max_files,
            'files.*' => 'file|max:' . ($assignment->max_file_size_mb * 1024),
            'is_final' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        // Check file types if specified
        if ($request->hasFile('files') && $assignment->allowed_file_types) {
            foreach ($request->file('files') as $file) {
                $extension = $file->getClientOriginalExtension();
                if (!in_array($extension, $assignment->allowed_file_types)) {
                    return $this->error("File type '{$extension}' is not allowed", 422);
                }
            }
        }

        // Get or create submission
        $submission = AssignmentSubmission::where('assignment_id', $assignmentId)
            ->where('student_id', $studentId)
            ->orderBy('attempt_number', 'desc')
            ->first();

        if (!$submission || $submission->status !== 'draft') {
            // Create new submission
            $attemptNumber = $submission ? $submission->attempt_number + 1 : 1;
            $submission = new AssignmentSubmission([
                'assignment_id' => $assignmentId,
                'student_id' => $studentId,
                'attempt_number' => $attemptNumber,
                'status' => 'draft'
            ]);
        }

        $submission->submission_text = $request->submission_text;

        // Handle file uploads
        if ($request->hasFile('files')) {
            $filePaths = [];
            $fileMetadata = [];

            foreach ($request->file('files') as $index => $file) {
                $filename = time() . '_' . $index . '_' . Str::slug($assignment->title) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs("submissions/{$assignmentId}/{$studentId}", $filename, 'private');
                
                $filePaths[] = $path;
                $fileMetadata[] = [
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                    'path' => $path
                ];
            }

            $submission->file_paths = $filePaths;
            $submission->file_metadata = $fileMetadata;
        }

        if ($request->boolean('is_final')) {
            $submission->markAsSubmitted();
        }

        $submission->save();

        return $this->success('Submission saved successfully', $submission, 201);
    }

    public function show($courseId, $assignmentId, $submissionId)
    {
        $submission = AssignmentSubmission::where('id', $submissionId)
            ->whereHas('assignment', function ($query) use ($courseId, $assignmentId) {
                $query->where('course_id', $courseId)->where('id', $assignmentId);
            })
            ->with(['student.user:id,first_name,last_name', 'gradedBy:id,first_name,last_name'])
            ->firstOrFail();

        // Check permissions
        $user = auth()->user();
        if ($user->hasRole('student') && $submission->student_id !== $user->student->id) {
            return $this->error('Unauthorized', 403);
        }

        return $this->success('Submission retrieved successfully', $submission);
    }

    public function update(Request $request, $courseId, $assignmentId, $submissionId)
    {
        $submission = AssignmentSubmission::where('id', $submissionId)
            ->whereHas('assignment', function ($query) use ($courseId, $assignmentId) {
                $query->where('course_id', $courseId)->where('id', $assignmentId);
            })
            ->firstOrFail();

        // Check permissions
        $user = auth()->user();
        if ($user->hasRole('student')) {
            if ($submission->student_id !== $user->student->id || $submission->status !== 'draft') {
                return $this->error('Cannot modify this submission', 422);
            }
        }

        $validator = Validator::make($request->all(), [
            'submission_text' => 'nullable|string',
            'score' => 'nullable|integer|min:0|max:' . $submission->assignment->max_score,
            'feedback' => 'nullable|string',
            'private_notes' => 'nullable|string',
            'is_final' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        // Handle grading (lecturers/admins only)
        if ($user->hasRole(['admin', 'lecturer']) && $request->has('score')) {
            $submission->grade(
                $request->score,
                $request->feedback,
                $user->id
            );
        } else {
            // Student updating their submission
            $submission->submission_text = $request->submission_text;
            
            if ($request->boolean('is_final')) {
                $submission->markAsSubmitted();
            }
            
            $submission->save();
        }

        return $this->success('Submission updated successfully', $submission);
    }

    public function destroy($courseId, $assignmentId, $submissionId)
    {
        $submission = AssignmentSubmission::where('id', $submissionId)
            ->whereHas('assignment', function ($query) use ($courseId, $assignmentId) {
                $query->where('course_id', $courseId)->where('id', $assignmentId);
            })
            ->firstOrFail();

        // Check permissions
        $user = auth()->user();
        if ($user->hasRole('student')) {
            if ($submission->student_id !== $user->student->id || $submission->status !== 'draft') {
                return $this->error('Cannot delete this submission', 422);
            }
        }

        // Delete files
        if ($submission->file_paths) {
            foreach ($submission->file_paths as $path) {
                Storage::disk('private')->delete($path);
            }
        }

        $submission->delete();

        return $this->success('Submission deleted successfully');
    }

    public function download($courseId, $assignmentId, $submissionId, $fileIndex)
    {
        $submission = AssignmentSubmission::where('id', $submissionId)
            ->whereHas('assignment', function ($query) use ($courseId, $assignmentId) {
                $query->where('course_id', $courseId)->where('id', $assignmentId);
            })
            ->firstOrFail();

        // Check permissions
        $user = auth()->user();
        if ($user->hasRole('student') && $submission->student_id !== $user->student->id) {
            return $this->error('Unauthorized', 403);
        }

        if (!isset($submission->file_paths[$fileIndex])) {
            return $this->error('File not found', 404);
        }

        $filePath = $submission->file_paths[$fileIndex];
        $fileName = $submission->file_metadata[$fileIndex]['name'] ?? 'download';

        if (!Storage::disk('private')->exists($filePath)) {
            return $this->error('File not found', 404);
        }

        return Storage::disk('private')->download($filePath, $fileName);
    }

    public function mySubmissions(Request $request)
    {
        $studentId = auth()->user()->student->id;
        
        $submissions = AssignmentSubmission::where('student_id', $studentId)
            ->with(['assignment.course:id,name,code'])
            ->submitted()
            ->latest('submitted_at')
            ->paginate(10);

        return $this->success('Student submissions retrieved successfully', $submissions);
    }
}
