<?php

namespace App\Http\Controllers\Api\LMS;

use App\Http\Controllers\BaseController;
use App\Models\CourseMaterial;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CourseMaterialController extends BaseController
{
    public function index(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);
        
        $materials = $course->visibleMaterials()
            ->with('uploadedBy:id,first_name,last_name')
            ->get();

        return $this->success('Course materials retrieved successfully', $materials);
    }

    public function store(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:document,video,audio,image,link,other',
            'file' => 'nullable|file|max:51200', // 50MB max
            'external_url' => 'nullable|url',
            'is_downloadable' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $data = $validator->validated();
        $data['course_id'] = $courseId;
        $data['uploaded_by'] = auth()->id();

        // Handle file upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . Str::slug($data['title']) . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('course-materials/' . $courseId, $filename, 'public');
            
            $data['file_path'] = $path;
            $data['file_name'] = $file->getClientOriginalName();
            $data['file_size'] = $file->getSize();
            $data['mime_type'] = $file->getMimeType();
        }

        $material = CourseMaterial::create($data);
        $material->load('uploadedBy:id,first_name,last_name');

        return $this->success('Course material uploaded successfully', $material, 201);
    }

    public function show($courseId, $materialId)
    {
        $material = CourseMaterial::where('course_id', $courseId)
            ->where('id', $materialId)
            ->with('uploadedBy:id,first_name,last_name')
            ->firstOrFail();

        return $this->success('Course material retrieved successfully', $material);
    }

    public function update(Request $request, $courseId, $materialId)
    {
        $material = CourseMaterial::where('course_id', $courseId)
            ->where('id', $materialId)
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'description' => 'nullable|string',
            'external_url' => 'nullable|url',
            'is_downloadable' => 'boolean',
            'is_visible' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        $material->update($validator->validated());
        $material->load('uploadedBy:id,first_name,last_name');

        return $this->success('Course material updated successfully', $material);
    }

    public function destroy($courseId, $materialId)
    {
        $material = CourseMaterial::where('course_id', $courseId)
            ->where('id', $materialId)
            ->firstOrFail();

        // Delete file if exists
        if ($material->file_path && Storage::disk('public')->exists($material->file_path)) {
            Storage::disk('public')->delete($material->file_path);
        }

        $material->delete();

        return $this->success('Course material deleted successfully');
    }

    public function download($courseId, $materialId)
    {
        $material = CourseMaterial::where('course_id', $courseId)
            ->where('id', $materialId)
            ->where('is_downloadable', true)
            ->firstOrFail();

        if (!$material->file_path || !Storage::disk('public')->exists($material->file_path)) {
            return $this->error('File not found', 404);
        }

        return Storage::disk('public')->download($material->file_path, $material->file_name);
    }

    public function reorder(Request $request, $courseId)
    {
        $validator = Validator::make($request->all(), [
            'materials' => 'required|array',
            'materials.*.id' => 'required|exists:course_materials,id',
            'materials.*.sort_order' => 'required|integer|min:0'
        ]);

        if ($validator->fails()) {
            return $this->error('Validation failed', 422, $validator->errors());
        }

        foreach ($request->materials as $materialData) {
            CourseMaterial::where('id', $materialData['id'])
                ->where('course_id', $courseId)
                ->update(['sort_order' => $materialData['sort_order']]);
        }

        return $this->success('Materials reordered successfully');
    }
}
