<?php

namespace App\Http\Controllers\Api\Departments;

use App\Http\Controllers\BaseController;
use App\Http\Resources\DepartmentResource;
use App\Repositories\DepartmentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DepartmentController extends BaseController
{
    private $departmentRepository;
    public function __construct(DepartmentRepository $departmentRepository)
    {
        $this->departmentRepository = $departmentRepository;
    }

    public function index(Request $request)
    {
        $departments = $this->departmentRepository->all();
    return $this->success('All departments', data: DepartmentResource::collection($departments));
    }

    public function withPagination(Request $request)
    {
        $departments = $this->departmentRepository->withPagination($request);
    return $this->success('All departments', DepartmentResource::collection($departments)->response()->getData(true));
    }

    public function show($id)
    {
        $department = $this->departmentRepository->findById($id);
        return $this->success('Department details', $department ? DepartmentResource::make($department) : null);
    }

    public function store(Request $request)
    {
        $request->merge(['admin_id' => Auth::id()]);
        try {
            $department = $this->departmentRepository->create($request->all());
            return $this->success('Department created successfully', DepartmentResource::make($department));
        } catch (\Exception $e) {
            Log::error('Error creating department: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function update(Request $request, $id)
    {
        $request->merge(['admin_id' => Auth::id()]);
        try {
            $this->departmentRepository->update($request->all(), $id);
            return $this->success('Department updated successfully', DepartmentResource::make($this->departmentRepository->findById($id)));
        } catch (\Exception $e) {
            Log::error('Error updating department: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function destroy($id)
    {
        $department = $this->departmentRepository->findById($id);
        if (!$department) {
            return $this->notFound('Department');
        }

        try {
            $this->departmentRepository->delete($id);
            return $this->success('Department deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting department: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function search(Request $request)
    {
        return $this->success('Search results', DepartmentResource::collection($this->departmentRepository->search($request->query('query'))));
    }
}
