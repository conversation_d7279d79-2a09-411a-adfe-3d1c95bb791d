<?php

namespace App\Http\Controllers\Api\Departments;

use App\Http\Controllers\BaseController;
use App\Imports\DepartmentsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;


class ImportDepartmentsController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
            'school_id' => 'required|exists:schools,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $school_id = $request->input('school_id');
            Excel::import(new DepartmentsImport($school_id), $request->file('file'));
            return $this->success('Departments imported successfully', null, 200);
        } catch (\Exception $e) {
            Log::error('Error importing departments: ' . $e->getMessage());
            return $this->internalServerError("Could not import departments", 500);
        }
    }
}
