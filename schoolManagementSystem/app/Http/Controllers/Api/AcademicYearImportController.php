<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Imports\AcademicYearImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class AcademicYearImportController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new AcademicYearImport, request()->file('file'));
            return $this->success('Academic years imported successfully', null, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to import academic Years', 'details' => $e->getMessage()], 500);
        }
    }
}
