<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Repositories\Interfaces\SystemSettingRepositoryInterface;
use Illuminate\Http\Request;

class SystemSettingController extends BaseController
{

    private $systemSettingRepository;
    public function __construct(SystemSettingRepositoryInterface $systemSettingRepository)
    {
        $this->systemSettingRepository = $systemSettingRepository;
    }


    public function index()
    {
        return $this->success(data: $this->systemSettingRepository->all());
    }


    public function update(Request $request)
    {
        
        try {
            $settings = $this->systemSettingRepository->update($request->key, $request->value);
        return $this->success("settings updated", $settings);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

    }


    public function clear()
    {
        $this->systemSettingRepository->clear();
        return $this->success("settings cleared");
    }


    public function get($key)
    {
        return $this->success(data: $this->systemSettingRepository->get($key));
    }



}
