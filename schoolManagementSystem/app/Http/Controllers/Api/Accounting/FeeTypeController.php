<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Requests\FeeTypeRequest;
use App\Repositories\Interfaces\FeeTypeRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FeeTypeController extends BaseController
{
    protected FeeTypeRepositoryInterface $feeTypeRepository;

    public function __construct(FeeTypeRepositoryInterface $feeTypeRepository)
    {
        $this->feeTypeRepository = $feeTypeRepository;
    }


    public function index(): JsonResponse
    {
        return $this->success("Fee types fetched", $this->feeTypeRepository->all(), 200);
    }

    public function search(Request $request): JsonResponse
    {
        $query = $request->input("query");
        $results = $this->feeTypeRepository->search($request->query);
        return $this->success("Results fetched", $results, 200);

    }

    public function store(FeeTypeRequest $request): JsonResponse
    {
        $feeType = $this->feeTypeRepository->create($request->validated());
        return $this->success("Fee type created.", $feeType, 201);
    }

    public function show($id): JsonResponse
    {
        $this->checkIfFound($id);
        $feeType = $this->feeTypeRepository->find($id);
        return $this->success("Fee type fetched.", $feeType, 200);
    }

    public function update(FeeTypeRequest $request, $id): JsonResponse
    {
       $this->checkIfFound($id);
        $feeType = $this->feeTypeRepository->update($id, $request->validated());
        return $this->success("Fee type updated.", $feeType, 200);
    }

    public function destroy($id): JsonResponse
    {
       $this->checkIfFound($id);
        $this->feeTypeRepository->delete($id);
        return $this->success("Fee Type deleted", 204);
    }

    /**
     * @param $id
     * @return JsonResponse|bool
     */
    public function checkIfFound($id): JsonResponse|bool
    {
        $feeType = $this->feeTypeRepository->find($id);
        if (empty($feeType)) {
            return $this->notFound("Fee Type");
        }

        return true;
    }
}
