<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\CrudRequest;
use App\Imports\CategoryImport;
use App\Repositories\Interfaces\CategoryCrudInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class CategoryCrudController extends BaseController
{
    private CategoryCrudInterface $crud;

    public function __construct(CategoryCrudInterface $crud)
    {
        $this->crud = $crud;
    }


    public function allIncomeCategories()
    {
        return $this->success("Success", $this->crud->allIncomeCategories());
    }


    public function allExpenseCategories()
    {
        return $this->success("Success", $this->crud->allExpenseCategories());
    }


    public function createIncomeCategory(CrudRequest $request)
    {
        $created = $this->crud->createIncomeCategory($request->all());
        return $this->success("Category created successfully", $created, 201);
    }


    public function createExpenseCategory(CrudRequest $request)
    {
        $created = $this->crud->createExpenseCategory($request->all());
        return $this->success("Category created successfully", $created, 201);
    }


    public function updateIncomeCategory(CrudRequest $request, $id)
    {
        $updated = $this->crud->updateIncomeCategory($request->all(), $id);
        return $this->success("Category updated successfully", $updated, 201);
    }


    public function updateExpenseCategory(CrudRequest $request, $id)
    {
        $updated = $this->crud->updateExpenseCategory($request->all(), $id);
        return $this->success("Category updated successfully", $updated, 201);
    }


    public function deleteIncomeCategory($id)
    {
        $deleted = $this->crud->deleteIncomeCategory($id);
        return $this->success("Category deleted successfully", $deleted, 200);
    }


    public function deleteExpenseCategory($id)
    {
        $deleted = $this->crud->deleteExpenseCategory($id);
        return $this->success("Category deleted successfully", $deleted, 200);
    }

    public function import(Request $request){

        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx,txt',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray(), 400);
        }

        try {
            Excel::import(import: new CategoryImport(), filePath: $request->file('file'));
            return $this->success("Imported successfully", null, 200);
        }

        catch (\Exception $e) {
            return $this->internalServerError(exception: $e);
        }
    }




}
