<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Requests\ExpenseRequest;
use App\Repositories\Interfaces\ExpenseInterface;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ExpenseController extends BaseController
{
    protected $expenseRepository;

    public function __construct(ExpenseInterface $expenseRepository)
    {
        $this->expenseRepository = $expenseRepository;
    }

    /**
     * Get all expenses.
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date|after_or_equal:start_date',
            'reference' => 'sometimes|required|string',
            'account_id' => 'sometimes|required|exists:accounts,id',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        // Initialize query conditions
        $conditions = [];

        // If both start_date and end_date are provided, add them to the conditions
        if ($request->has(['start_date', 'end_date'])) {
            $conditions['start_date'] = $request->start_date;
            $conditions['end_date'] = $request->end_date;
        }

        // If reference is provided, add it to the conditions
        if ($request->has('reference')) {
            $conditions['reference'] = $request->reference;
        }

        // If account_id is provided, add it to the conditions
        if ($request->has('account_id')) {
            $conditions['account_id'] = $request->account_id;
        }

        try {
            // Handle combination logic in the repository
            if (!empty($conditions)) {
                $expenses = $this->expenseRepository->getFilteredExpenses($conditions);
            } else {
                $expenses = $this->expenseRepository->all();
            }

            return $this->success("Expenditures fetched", $expenses, 200);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }


    /**
     * Store a newly created expense.
     */
    public function store(ExpenseRequest $request)
    {
        try {
            $data = $request->validated();
            $expense = $this->expenseRepository->create($data);
            return $this->success("Expenditure created", $expense, 201);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    /**
     * Show a specific expense.
     */
    public function show($id)
    {
        try {
            $expense = $this->expenseRepository->findById($id);

            if (!$expense) {
                return $this->notFound("Expense", 404);
            }

            return $this->success("Expense fetched", $expense, 200);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    /**
     * Update a specific expense.
     */
    public function update(ExpenseRequest $request, $id)
    {
        $data = $request->validated();

        try {
            $expense = $this->expenseRepository->findById($id);

            if (!$expense) {
                return $this->notFound("Expense", 404);
            }

            $updatedExpense = $this->expenseRepository->update($data, $id);

            return $this->success("Expense updated", $updatedExpense, 200);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    /**
     * Remove a specific expense.
     */
    public function destroy($id)
    {
        try {
            $expense = $this->expenseRepository->findById($id);

            if (!$expense) {
                return $this->notFound("Expense", 404);
            }

            $this->expenseRepository->destroy($id);

            return $this->success("Expense deleted", null, 204);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    /**
     * Get expenses within a specific date range.
     */
    public function getExpensesByDate(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $expenses = $this->expenseRepository->getExpensesByDate($request->start_date, $request->end_date);
            return $this->success("Expenses fetched by date range", $expenses, 200);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }

    /**
     * Pay a lecturer and create associated expense and payroll entries.
     */
    public function payLecturer(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'lecturer_id' => 'required|exists:lecturers,id',
            'account_id' => 'required|exists:accounts,id',
            'payment_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        try {
            // Extract data
            $amount = $request->input('amount');
            $lecturer = $request->input('lecturer_id');
            $account = $request->input('account_id');
            $paymentDate = $request->input('payment_date');

            // Process payment
            $expense = $this->expenseRepository->payLecturer($amount, $lecturer, $account, $paymentDate);

            return $this->success("Lecturer paid successfully", $expense, 201);
        } catch (Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
