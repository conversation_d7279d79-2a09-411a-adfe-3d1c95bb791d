<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Repositories\Interfaces\TransactionInterface;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TransactionsController extends BaseController
{
    protected TransactionInterface $transactionInterface;

    public function __construct(TransactionInterface $transactionInterface)
    {
        $this->transactionInterface = $transactionInterface;
    }

    public function index(Request $request): JsonResponse
    {
        return $this->success(
            'Transactions',
            $this->transactionInterface->getAllTransactions($request->all())
        );
    }

    public function show($id): JsonResponse
    {
        return $this->success(
            'Transaction',
            $this->transactionInterface->getTransactionById($id)
        );
    }

    public function getUserTransactions(Request $request)
    {
        return $this->success(
            'User transactions',
            $this->transactionInterface->getUserTransactions($request->user_id)
        );
    }


    public function getTransactionByReference(Request $request): JsonResponse
    {
        return $this->success(
            'Transaction by reference',
            $this->transactionInterface->getTransactionByReference($request->input('reference'))
        );
    }

    public function downloadPdf($id)
    {
        // Fetch the transaction
        $transaction = $this->transactionInterface->getTransactionById($id);

        if (!$transaction) {
            return $this->notFound('Transaction not found');
        }

        // Conditionally load the relationships based on transaction type
        if ($transaction->transactionable_type === \App\Models\Income::class) {
            // Load only the student for Income transactions
            $transaction->load('transactionable.student');
        } elseif ($transaction->transactionable_type === \App\Models\Expense::class) {
            // Load both student and lecturer for Expense transactions
            $transaction->load('transactionable.student', 'transactionable.lecturer');
        }

        // Load the view and pass the transaction data to it
        $pdf = Pdf::loadView('receipts.transaction', compact('transaction'));

        // Stream the PDF to the browser
        return $pdf->stream('transaction_receipt.pdf');
    }


}
