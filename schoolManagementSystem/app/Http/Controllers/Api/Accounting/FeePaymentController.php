<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Requests\FeeRequest;
use App\Models\Account;
use App\Models\Installment;
use App\Models\Student;
use App\Repositories\Interfaces\FeeRepositoryInterface;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class FeePaymentController extends BaseController
{
    protected FeeRepositoryInterface $feeRepository;

    public function __construct(FeeRepositoryInterface $feeRepository)
    {
        $this->feeRepository = $feeRepository;
    }

    public function store(FeeRequest $request): JsonResponse
{
    try {
        $data = $request->validated();

        // Handle registration fee
        if ($data['fee_type_id'] == 1) {
            return $this->handleRegistrationFee($data);
        }

        // Check if the fee exists by student, academic year, fee type, and bank reference
        $existingFee = $this->feeRepository->getFeeByStudentAndYear(
            $data['student_id'],
            $data['academic_year_id'],
            $data['fee_type_id']
        );

        // If installment payment is requested
        if ($data['is_installment']) {
            // If no fee exists, create a new fee entry
            if (!$existingFee) {
                $feeData = [
                    'student_id' => $data['student_id'],
                    'academic_year_id' => $data['academic_year_id'],
                    'fee_type_id' => $data['fee_type_id'],
                    'amount' => $data['amount'], // Total amount for the fee
                    'status' => $data['status'], // Default to pending for installments
                    'bank_ref' => $data['bank_ref'] ?? null,
                    'account_id' => $data['account_id'] ?? null,
                    'payment_date' => $data['payment_date'],
                    'payment_channel' => $data['payment_channel'],
                ];
                $existingFee = $this->feeRepository->createFee($feeData);
            }

            // Validate fee amount against the program cost
            $programCost = $this->getProgramCost($data['student_id']);
            $totalInstallmentAmount = Installment::where('fee_id', $existingFee->id)->sum('amount');

            // Check if fee is already fully paid
            if ($totalInstallmentAmount >= $programCost) {
                $existingFee->update([
                    'status' => 'complete',
                    'amount' => $programCost
                ]);
                return $this->conflict("Fee payment already completed for this student, academic year, and fee type.");
            }

            // Check for duplicate installment based on bank reference
            $installment = Installment::where('fee_id', $existingFee->id)
                ->where('reference', $data['bank_ref'])
                ->first();


            if ($installment) {
                return $this->conflict("Installment with this bank reference already exists.");
            }

            // Add new installment
            $installment = $this->feeRepository->addInstallment(
                $existingFee->id,
                $data['amount'],
                $data['bank_ref'],
                $data['account_id'],
            );

            // Update account balance
            $this->updateAccountBalance($data['account_id'], $data['amount']);

            // Recalculate fee status based on total paid amount
            $totalInstallmentAmount += $data['amount'];
            if ($totalInstallmentAmount >= $programCost) {
                $existingFee->update(['status' => 'complete']);
            } else {
                $existingFee->update(['status' => 'incomplete']);
            }

            return $this->success("Installment added successfully.", $installment, Response::HTTP_CREATED);

        } else {
            // Non-installment fee payment (one-time payment)
            if ($existingFee) {
                $programCost = $this->getProgramCost($data['student_id']);
                $totalInstallmentAmount = Installment::where('fee_id', $existingFee->id)->sum('amount');

                // If the fee is already fully paid
                if ($totalInstallmentAmount >= $programCost) {
                    $existingFee->update(['status' => 'complete']);
                    return $this->conflict("Fee payment already completed for this student, academic year, and fee type.");
                }

                return $this->conflict("Fee payment already recorded for this student, academic year, and fee type.");
            }

            // Create new fee entry
            $fee = $this->feeRepository->createFee($data);

            // create and installment for it
            $installment = $this->feeRepository->addInstallment(
                $fee->id,
                $data['amount'],
                $data['bank_ref'],
                $data['account_id'],
            );

            // Check if the fee amount covers the entire program cost
            $programCost = $this->getProgramCost($data['student_id']);
            if ($data['amount'] >= $programCost) {
                $fee->update(['status' => 'complete']);
            }

            // Update account balance
            $this->updateAccountBalance($data['account_id'], $data['amount']);

            return $this->success("Fee payment recorded successfully.", $fee, Response::HTTP_CREATED);
        }
    } catch (\Exception $e) {
        return $this->error($e->getMessage());
    }
}



    public function update(Request $request, $id): JsonResponse
    {
        try {
            $data = $request->all();
            $fee = $this->feeRepository->updateFee($id, $data);
            return $this->success("Fee payment updated successfully.", $fee, Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function show($id): JsonResponse
    {
        try {
            $fee = $this->feeRepository->getFeeById($id);
            if (!$fee) {
                return $this->notFound('Fee');
            }
            return $this->success("Fee retrieved successfully.", $fee, Response::HTTP_OK);
        } catch (\Exception $e) {
            Log::error('Error retrieving fee: ' . $e->getMessage());
            return $this->internalServerError($e);
        }
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $results = $this->feeRepository->getAll($request->input('query'));
            return $this->success("Fee payments fetched successfully.", $results, Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    public function updateAccountBalance($account_id, $amount)
    {

        $account = Account::find($account_id);
        $account->update([
            'balance' => $account->balance + $amount
        ]);

        return $account;

    }

    public function getProgramCost($student_id)
    {

        $student = Student::with('program')->find($student_id);
        return $student->program->cost;
    }

    protected function handleRegistrationFee(array $data): JsonResponse
    {
        try {
            $existingFee = $this->feeRepository->getRegistrationFeeByStudentAndYear(
                $data['student_id'],
                $data['academic_year_id']
            );

            if ($existingFee) {
                return $this->conflict("Registration fee payment already recorded.");
            }

            // Create registration fee entry
            $fee = $this->feeRepository->createFee($data);

            // Update account balance
            $this->updateAccountBalance($data['account_id'], $data['amount']);

            return $this->success("Registration fee recorded successfully.", $fee, Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function getStudentFeeStatus(Request $request): JsonResponse
{
    try {

        $validator = Validator::make($request->all(), [
            'student_id' => 'required|integer',
            'academic_year_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        $data = $request->all();
        // Call the service method to get fee status
        $feeStatus = $this->feeRepository->feeStatus($data['student_id'], $data['academic_year_id']);

        return $this->success("fee status retrieved", $feeStatus, Response::HTTP_OK);
    } catch (\Exception $e) {
        return $this->internalServerError($e);
    }
}

public function getFeesByStudent($studenId)
{
    return $this->success(data: $this->feeRepository->getAllFeeByStudent($studenId));
}

public function getReceipt($feeId)
{
    $fee = $this->feeRepository->getFeeById($feeId);

    if (!$fee) {
        return $this->notFound('Fee not found.');
    }

    $fee->load('student.department.school','student.program', 'academicYear', 'installments');

    $cleanAcademicYear = str_replace('_', '/', $fee->academicYear->name);

    $receiptData = [
        'fee' => $fee,
        'student' => $fee->student,
        'academicYear' => $cleanAcademicYear,
        'transaction' => $fee->transactions
    ];

    $pdf = Pdf::loadView('receipts.fee', ['receipt' => $receiptData]);
    return $pdf->stream($receiptData['student']->matricule .'receipt.pdf');
}

public function getInstallmentReceipt($installmentId){

    $installment = $this->feeRepository->getInstallmentById($installmentId);

    if (!$installment) {
        return $this->notFound('Installment not found.');
    }

    $fee = $installment->fee;
    $student = $fee->student;
    $academicYear = $fee->academicYear;

    $cleanAcademicYear = str_replace('_', '/', $academicYear->name);


    $receiptData = [
        'installment' => $installment,
        'fee' => $fee,
        'student' => $student,
        'academicYear' => $cleanAcademicYear,
        'transaction' => $installment->transactions
    ];

    $pdf = Pdf::loadView('receipts.installment', ['receipt' => $receiptData]);

    return $pdf->stream($receiptData['student']->matricule .'installment-receipt.pdf');

}

public function installments(Request $request)
{
    $validator = Validator::make($request->all(), [
        'student_id' => 'required|integer',
        'academic_year_id' => 'required|integer',
    ]);

    if ($validator->fails()) {
        return $this->validationError($validator->errors());
    }

    $data = $request->all();
    $installments = $this->feeRepository->getAllIinstallments($data['student_id'], $data['academic_year_id']);
    return $this->success(data: $installments);
}

}
