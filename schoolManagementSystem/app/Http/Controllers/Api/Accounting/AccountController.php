<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Requests\AccountRequest;
use App\Repositories\Interfaces\AccountRepositoryInterface;

class AccountController extends BaseController
{

    protected AccountRepositoryInterface $accountRepository;

    public function __construct(AccountRepositoryInterface $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $accounts = $this->accountRepository->all();
        return $this->success('Accounts',$accounts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AccountRequest $request)
    {
        $account = $this->accountRepository->create($request->validated());
        return $this->success('Account Created',$account);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $account = $this->accountRepository->findById($id);
        return $this->success('Account Retrieved',$account);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AccountRequest $request, string $id)
    {
        $account = $this->accountRepository->findById($id);
        $this->accountRepository->update($request->all(), $id);
        $account->refresh();
        return $this->success('Account Updated',$account);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $account = $this->accountRepository->findById($id);
        return $this->success('Account Deleted',$account);
    }
}
