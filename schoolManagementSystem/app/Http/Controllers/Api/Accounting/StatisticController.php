<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Models\Account;
use App\Models\Course;
use App\Models\Department;
use App\Models\School;
use App\Models\Student;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class StatisticController extends BaseController
{
    /**
     * Get accounting statistics for the given year.
     *
     * @queryParam year The year to get statistics for. Defaults to the current year.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        // Determine the year to use
        $year = $request->query('year', Carbon::now()->year);

        // Validate that the year is a positive integer
        if (!is_numeric($year) || $year <= 0) {
            return $this->error('Invalid year provided', 422);
        }

        // Total income and expenditure for the specified year from transactions
        $totalIncome = Transaction::whereYear('transaction_date', $year)
            ->where('transaction_type', 'Income')
            ->sum('amount');

        $totalExpenditure = Transaction::whereYear('transaction_date', $year)
            ->where('transaction_type', 'Expense')
            ->sum('amount');

        // Balance calculation
        $balance = $totalIncome - $totalExpenditure;

        // Account balances
        $accounts = Account::all()->map(function ($account) {
            return [
                'name' => $account->name,
                'balance' => $account->balance,
            ];
        });

        // Get income and expenditure data for plotting
        $incomeData = Transaction::select(DB::raw('DATE(transaction_date) as date'), DB::raw('SUM(amount) as total'))
            ->whereYear('transaction_date', $year)
            ->where('transaction_type', 'Income')
            ->groupBy(DB::raw('DATE(transaction_date)'))
            ->orderBy(DB::raw('DATE(transaction_date)'))
            ->get();

        $expenseData = Transaction::select(DB::raw('DATE(transaction_date) as date'), DB::raw('SUM(amount) as total'))
            ->whereYear('transaction_date', $year)
            ->where('transaction_type', 'Expense')
            ->groupBy(DB::raw('DATE(transaction_date)'))
            ->orderBy(DB::raw('DATE(transaction_date)'))
            ->get();

        // Get the 5 most recent transactions
        $recentTransactions = Transaction::orderBy('transaction_date', 'desc')
            ->take(5)
            ->get();

        // students count
        $studentsCount = Student::count();
        // lecturer count (user with role lecturer, using laravel spatie)
        $lecturerCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'lecturer');
        })->count();

        $schools = School::count();
        $departments = Department::count();
        $courses = Course::count();

        return $this->success("Statistics fetched successfully", [
            'total_income' => $totalIncome,
            'total_expenditure' => $totalExpenditure,
            'balance' => $balance,
            'accounts' => $accounts,
            'income_data' => $incomeData,
            'expense_data' => $expenseData,
            'total_students' => $studentsCount,
            'total_lecturers' => $lecturerCount,
            'total_schools' => $schools,
            'total_departments' => $departments,
            'total_courses' => $courses,
            'recent_transactions' => $recentTransactions,  // Include recent transactions
        ]);
    }
}
