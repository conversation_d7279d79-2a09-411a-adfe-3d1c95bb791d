<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Imports\FeeTypeImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class FeeTypeImportController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new FeeTypeImport, request()->file('file'));
            return $this->success('Fee Types imported successfully', null, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to import Fee Types', 'details' => $e->getMessage()], 500);
        }
    }
}
