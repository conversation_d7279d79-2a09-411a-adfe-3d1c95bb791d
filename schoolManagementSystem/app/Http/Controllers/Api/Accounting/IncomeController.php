<?php

namespace App\Http\Controllers\Api\Accounting;

use App\Http\Controllers\BaseController;
use App\Http\Requests\IncomeRequest;
use App\Repositories\Interfaces\IncomeInterface;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class IncomeController extends BaseController
{
    private $incomeRepository;

    public function __construct(IncomeInterface $incomeRepository)
    {
        $this->incomeRepository = $incomeRepository;
    }


    public function index()
    {
        $incomes = $this->incomeRepository->all();
        return $this->success("Incomes fetched", $incomes, 200);
    }


    public function store(IncomeRequest $request)
    {
        $data = $request->validated();

        $income = $this->incomeRepository->create($data);

        return $this->success("Income created", $income, 201);

    }


    public function show($id)
    {
        $this->checkIfFound($id);
        $income = $this->incomeRepository->findById($id);
        return $this->success("Income fetched", $income, 200);
    }


    public function update(IncomeRequest $request, $id)
    {
        $this->checkIfFound($id);
        $income = $this->incomeRepository->update($request->validated(), $id);
        return $this->success("Income updated", $income, 200);
    }


    public function destroy($id)
    {
        $this->checkIfFound($id);
        $this->incomeRepository->delete($id);
        return $this->success("Income deleted", 204);

    }


    public function search(Request $request)
    {
        $query = $request->input("query");
        $results = $this->incomeRepository->search($query);
        return $this->success("Results fetched", $results, 200);
    }


    public function checkIfFound($id)
    {
        $income = $this->incomeRepository->findById($id);
        if (empty($income)) {
            return $this->notFound("Income");
        }
    }

    



}
