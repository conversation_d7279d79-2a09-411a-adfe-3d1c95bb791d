<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Repositories\Interfaces\StatisticsInterface;
use Illuminate\Http\Request;

class AdminStatisticsController extends BaseController
{
    private $statisticsRepository;

    public function __construct(StatisticsInterface $statisticsRepository)
    {
        $this->statisticsRepository = $statisticsRepository;
    }

    public function getStatistics(Request $request)
    {
        $filters = $request->only(['academic_year_id', 'level_id', 'department_id']);
        $statistics = $this->statisticsRepository->getGeneralStatistics($filters);

        return $this->success('Statistics retrieved successfully', $statistics);
    }

    public function exportStatisticsToCSV(Request $request)
    {
        $filters = $request->only(['academic_year_id', 'level_id', 'department_id']);
        $filePath = $this->statisticsRepository->exportStatisticsToCSV($filters);

        return $this->success('Statistics exported successfully', $filePath);
    }
}
