<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\BaseController;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminLoginController extends BaseController
{
    private $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function __invoke(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'email' => 'required',
                'phone_number' => 'required',
                'password' => 'required',
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors());
            }
            $user = $this->userRepository->findByUsernameAndPhoneNumber($request->input('email'), $request->input('phone_number'));

            if ($user && Hash::check($request->input('password'), $user->password)) {
                $token = $this->userRepository->createToken($user, 'AdminToken');
                $role = $user->getRoleNames()->first();

                $data = [
                        'user' => new UserResource($user),
                        'role' => $role,
                        'token' => $token
                ];
                return $this->success("Admin successful login", $data);

            }

            return $this->unauth();
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            return $this->internalServerError();
        }
    }
}
