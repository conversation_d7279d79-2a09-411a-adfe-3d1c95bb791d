<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\BaseController;
use App\Http\Resources\StudentResource;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class StudentLoginController extends BaseController
{
    private $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function __invoke(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'matricule' => 'required',
                'password' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors());
            }
            $student = $this->userRepository->findbyMatricule($request->input('matricule'));

            if(!$student) {
                return $this->notFound('Student Account');
            }
            if ($student && Hash::check($request->input('password'), $student->user->password)) {
                $token = $this->userRepository->createToken($student->user, 'StudentToken');
                $role = $student->user->getRoleNames()->first();


                $data = [
                    'student' => StudentResource::make($student),
                    'role' => $role,
                    'token' => $token
                ];
                return $this->success('Login successful', $data);
            }

            return $this->unauth('Invalid Credentials');
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            return $this->internalServerError();
        }
    }
}
