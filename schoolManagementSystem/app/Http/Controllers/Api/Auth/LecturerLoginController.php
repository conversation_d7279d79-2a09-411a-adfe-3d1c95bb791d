<?php

namespace App\Http\Controllers\Api\Auth;
use App\Http\Controllers\BaseController;
use App\Http\Resources\UserResource;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class LecturerLoginController extends BaseController
{
    private $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Handle lecturer login request
     */
    public function __invoke(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required',
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors());
            }

            // Find user by email
            $user = $this->userRepository->findByEmail($request->input('email'));

            if (!$user) {
                return $this->notFound('Lecturer Account');
            }

            // Check if user has lecturer role
            if (!$user->hasRole('lecturer')) {
                return $this->forbidden('Access denied. This portal is for lecturers only.');
            }

            // Verify password and create token
            if ($user && Hash::check($request->input('password'), $user->password)) {

                $token = $this->userRepository->createToken($user, 'LecturerToken');
                $role = $user->getRoleNames()->first();

                $data = [
                    'user' => new UserResource($user),
                    'lecturer' => new UserResource($user), // For frontend compatibility
                    'role' => $role,
                    'token' => $token
                ];

                return $this->success('Lecturer login successful', $data);
            }

            return $this->unauth('Invalid Credentials');

        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            return $this->internalServerError();
        }
    }
}
