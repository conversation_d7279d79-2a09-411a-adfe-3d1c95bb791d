<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\Lecturer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class LecturerLoginController extends Controller
{
    /**
     * <PERSON>le lecturer login request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $credentials = $request->only('email', 'password');

            // Find lecturer by email
            $lecturer = Lecturer::where('email', $credentials['email'])->first();

            if (!$lecturer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials. Lecturer not found.'
                ], 401);
            }

            // Check if lecturer account is active
            if (!$lecturer->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Your account has been deactivated. Please contact the administrator.'
                ], 403);
            }

            // Verify password
            if (!Hash::check($credentials['password'], $lecturer->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials. Incorrect password.'
                ], 401);
            }

            // Create token for lecturer
            $token = $lecturer->createToken('lecturer-token', ['lecturer'])->plainTextToken;

            // Update last login timestamp
            $lecturer->update([
                'last_login_at' => now(),
                'login_count' => $lecturer->login_count + 1
            ]);

            // Prepare lecturer data
            $lecturerData = [
                'id' => $lecturer->id,
                'first_name' => $lecturer->first_name,
                'last_name' => $lecturer->last_name,
                'email' => $lecturer->email,
                'phone' => $lecturer->phone,
                'employee_id' => $lecturer->employee_id,
                'department_id' => $lecturer->department_id,
                'department' => $lecturer->department ? [
                    'id' => $lecturer->department->id,
                    'name' => $lecturer->department->name,
                ] : null,
                'profile_image' => $lecturer->profile_image,
                'is_active' => $lecturer->is_active,
                'role' => 'lecturer',
                'last_login_at' => $lecturer->last_login_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $lecturerData,
                    'lecturer' => $lecturerData,
                    'token' => $token,
                    'role' => 'lecturer',
                    'expires_at' => now()->addDays(30)->toISOString(),
                ]
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Lecturer login error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred during login. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
