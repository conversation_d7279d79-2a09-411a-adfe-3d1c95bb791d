<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class LecturerLoginController extends Controller
{
    protected $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Handle lecturer login request
     */
    public function __invoke(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $credentials = $request->only('email', 'password');

            // Find user by email
            $user = $this->userRepository->findByEmail($credentials['email']);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials. User not found.'
                ], 401);
            }

            // Check if user has lecturer role
            if (!$user->hasRole('lecturer')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. This portal is for lecturers only.'
                ], 403);
            }

            // Verify password
            if (!Hash::check($credentials['password'], $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials. Incorrect password.'
                ], 401);
            }

            // Create token for lecturer
            $token = $this->userRepository->createToken($user, 'lecturer-token');

            // Prepare user data
            $userData = [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'gender' => $user->gender,
                'dob' => $user->dob,
                'profile_image' => $user->profile_image,
                'role' => 'lecturer',
                'roles' => $user->roles->pluck('name'),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $userData,
                    'lecturer' => $userData,
                    'token' => $token,
                    'role' => 'lecturer',
                    'expires_at' => now()->addDays(30)->toISOString(),
                ]
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Lecturer login error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during login. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
