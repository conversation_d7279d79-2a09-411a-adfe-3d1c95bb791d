<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\Lecturer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LecturerForgotPasswordController extends Controller
{
    /**
     * <PERSON><PERSON> lecturer forgot password request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:lecturers,email',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $email = $request->email;

            // Find lecturer by email
            $lecturer = Lecturer::where('email', $email)->first();

            if (!$lecturer) {
                return response()->json([
                    'success' => false,
                    'message' => 'No lecturer found with this email address.'
                ], 404);
            }

            // Check if lecturer account is active
            if (!$lecturer->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Your account has been deactivated. Please contact the administrator.'
                ], 403);
            }

            // Generate reset token
            $token = Str::random(64);

            // Store reset token in database
            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $email],
                [
                    'email' => $email,
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );

            // Create reset URL
            $resetUrl = config('app.frontend_url') . '/lecturer/reset-password?token=' . $token . '&email=' . urlencode($email);

            // Send email (you can customize this based on your mail setup)
            try {
                // For now, we'll just log the reset URL
                // In production, you should send an actual email
                \Log::info('Lecturer Password Reset URL: ' . $resetUrl);
                
                // You can implement actual email sending here
                // Mail::send('emails.lecturer-password-reset', ['resetUrl' => $resetUrl, 'lecturer' => $lecturer], function($message) use ($email) {
                //     $message->to($email);
                //     $message->subject('Reset Your Lecturer Account Password');
                // });

            } catch (\Exception $e) {
                \Log::error('Failed to send password reset email: ' . $e->getMessage());
                
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send password reset email. Please try again later.'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Password reset link has been sent to your email address.',
                'data' => [
                    'email' => $email,
                    'reset_url' => config('app.debug') ? $resetUrl : null // Only show in debug mode
                ]
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Lecturer forgot password error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}

class LecturerResetPasswordController extends Controller
{
    /**
     * Handle lecturer password reset
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:lecturers,email',
                'token' => 'required|string',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $email = $request->email;
            $token = $request->token;
            $password = $request->password;

            // Find the reset token
            $resetRecord = DB::table('password_reset_tokens')
                ->where('email', $email)
                ->first();

            if (!$resetRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired reset token.'
                ], 400);
            }

            // Check if token is valid (not older than 1 hour)
            if (now()->diffInMinutes($resetRecord->created_at) > 60) {
                DB::table('password_reset_tokens')->where('email', $email)->delete();
                
                return response()->json([
                    'success' => false,
                    'message' => 'Reset token has expired. Please request a new one.'
                ], 400);
            }

            // Verify token
            if (!Hash::check($token, $resetRecord->token)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid reset token.'
                ], 400);
            }

            // Find lecturer
            $lecturer = Lecturer::where('email', $email)->first();

            if (!$lecturer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lecturer not found.'
                ], 404);
            }

            // Update password
            $lecturer->update([
                'password' => Hash::make($password),
                'password_changed_at' => now()
            ]);

            // Delete the reset token
            DB::table('password_reset_tokens')->where('email', $email)->delete();

            // Revoke all existing tokens for security
            $lecturer->tokens()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Password has been reset successfully. Please login with your new password.'
            ], 200);

        } catch (\Exception $e) {
            \Log::error('Lecturer reset password error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while resetting your password. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
