<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class AuthController extends BaseController
{

    protected $userRepository;
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function logout(Request $request)
    {
        $user = $request->user();
        $this->userRepository->deleteToken($user, $request->tokenName);
        return $this->success('Logout successful');
    }
}
