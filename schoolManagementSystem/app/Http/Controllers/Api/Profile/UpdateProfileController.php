<?php

namespace App\Http\Controllers\Api\Profile;

use App\Http\Controllers\BaseController;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Requests\UpdateProfileImageRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class UpdateProfileController extends BaseController
{
    /**
     * Update the user's profile information.
     *
     * @param UpdateProfileRequest $request
     * @return JsonResponse
     */
    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $user = $request->user();
        $user->update($request->validated());

        return $this->success("User Successfully updated", $user);

    }

    /**
     * Update the user's profile image.
     *
     * @param UpdateProfileImageRequest $request
     * @return JsonResponse
     */
    public function updateProfileImage(UpdateProfileImageRequest $request): JsonResponse
    {
        $user = $request->user();
        $image = $request->file('profile_image');

        // Store the new profile image and get the path
        $filePath = $image->store('public/profile_images');

        // Delete old profile image if exists
        if ($user->profile_image && Storage::exists($user->profile_image)) {
            Storage::delete($user->profile_image);
        }

        // Update the user's profile image URL
        $user->profile_image = Storage::url($filePath);
        $user->save();

        return $this->success("User Successfully updated", $user);
    }
}
