<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Http\Requests\AcademicYearRequest;
use App\Repositories\Interfaces\AcademicYearRepositoryInterface;
use Illuminate\Http\Request;

class AcademicYearController extends BaseController
{
    protected AcademicYearRepositoryInterface $academicYearRepository;

    public function __construct(AcademicYearRepositoryInterface $academicYearRepository)
    {
        $this->academicYearRepository = $academicYearRepository;
    }

    public function index()
    {
        $academicYears = $this->academicYearRepository->all();
        return $this->success("Academic Years retrieved successfully.", $academicYears);
    }

    public function search(Request $request)
    {
        $query = $request->get('query');
        $results = $this->academicYearRepository->search($query);
        return $this->success("Academic Years retrieved successfully.", $results);
    }

    public function store(AcademicYearRequest $request): \Illuminate\Http\JsonResponse
    {
        try {
            $academicYear = $this->academicYearRepository->create($request->validated());
            return $this->success("Academic Year created successfully.", $academicYear);
        }catch (\Exception $exception){
            return $this->internalServerError($exception);
        }

    }

    public function show($id): \Illuminate\Http\JsonResponse
    {
        $academicYear = $this->academicYearRepository->find($id);
        return $this->success("Academic Year retrieved successfully.", $academicYear);
    }

    public function update(AcademicYearRequest $request, $id): \Illuminate\Http\JsonResponse
    {
        try {
            $academicYear = $this->academicYearRepository->update($id, $request->validated());
            return $this->success("Academic Year updated successfully.", $academicYear);
        }catch (\Exception $exception){
            return $this->internalServerError($exception);
        }
    }

    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        $academicYear = $this->academicYearRepository->find($id);
        if (empty($academicYear))
            {
               return $this->notFound("Academic");
            }
        try
            {
                $this->academicYearRepository->delete($id);
                return $this->success("Academic Year deleted successfully.");
            }
        catch
            ( \Exception $exception){
               return $this->internalServerError(exception: $exception);
            }
    }
}
