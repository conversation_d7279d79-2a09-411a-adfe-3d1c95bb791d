<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Models\Level;
use Illuminate\Http\Request;

class LevelController extends BaseController
{
    public function index()
    {
        $levels = Level::all();
        return $this->success('Levels', $levels);
    }

    public function show($id)
    {
        $level = Level::find($id);
        return $this->success('Level', $level);
    }

    public function store(Request $request)
    {
        if(!auth()->user()->hasRole('admin')) {
            return $this->forbidden();
        }
        $level = Level::create($request->all());
        return $this->success('Level created successfully', $level);
    }

    public function update(Request $request, $id)
    {
        $level = Level::find($id);
        $level->update($request->all());
        return $this->success('Level updated successfully', $level);
    }
}
