<?php

namespace App\Http\Controllers\Api\Schools;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Repositories\SchoolRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\SchoolRequest;
use App\Http\Resources\DepartmentResource;
use App\Http\Resources\SchoolResource;
use App\Http\Resources\SchooResource;
use App\Models\Program;
use App\Repositories\Interfaces\ProgramRepositoryInterface;
use App\Repositories\Interfaces\SchoolRepositoryInterface;
use Illuminate\Support\Facades\Auth;

class SchoolController extends BaseController
{
    protected $schoolRepository;
    protected $programRepository;

    public function __construct(SchoolRepositoryInterface $schoolRepository, ProgramRepositoryInterface $programRepository)
    {
        $this->schoolRepository = $schoolRepository;
        $this->programRepository = $programRepository;
    }

    public function index(Request $request)
    {
        try {
            $schools = $this->schoolRepository->all();
            return $this->success('All schools', SchoolResource::collection($schools));
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }


    public function show($id)
    {
        try {
            $school = $this->schoolRepository->findById($id);

            if ($school) {
                $abbreviations = json_decode($school->programs, true);

                if (is_array($abbreviations)) {
                    $programs = $this->programRepository->findByNameOrAbbreviation($abbreviations);
                    $school->programs = $programs;
                } else {
                    $school->programs = [];
                }
            }

            return $this->success('School details', $school ? SchoolResource::make($school) : null);
        } catch (\Exception $e) {
            Log::error('Error fetching school: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function store(SchoolRequest $request)
{
    $request->merge(['admin_id' => Auth::id()]);

    try {
        $school = $this->schoolRepository->create($request->all());

        // Attach the program IDs directly from the request
        $programs = $request->input('programs');

        // check if the programs are present
        $programIds = Program::whereIn('name', array_map('trim',  $programs))
            ->orWhereIn('abbreviation', array_map('trim', $programs))
            ->pluck('id');

        $school->programRelation()->attach($programIds);
        $school->load('admin');

        return $this->success('School created successfully', SchoolResource::make($school));
    } catch (\Exception $e) {
        Log::error('Error creating school: ' . $e->getMessage());
        return $this->internalServerError();
    }
}


    public function update(SchoolRequest $request, $id)
    {
        $request->merge(['admin_id' => Auth::id()]);
        $request->merge(['programs' => json_encode($request->input('programs'))]);

        try {
            $school = $this->schoolRepository->findById($id);
            // attach program ids
            $programs = $request->input('programs');

            $programIds = Program::whereIn('name', array_map('trim', $programs))
                ->orWhereIn('abbreviation', array_map('trim', $programs))
                ->pluck('id');

            $school->programRelation()->sync($programIds);

            if ($this->schoolRepository->update($request->all(), $id)) {
                $school->load('admin');
            }
            return $this->success('School updated successfully', SchoolResource::make($school));
        } catch (\Exception $e) {
            Log::error('Error updating school: ' . $e->getMessage());
            return $this->internalServerError();
        }
    }

    public function destroy($id)
{
    try {
        $school = $this->schoolRepository->findById($id);

        if (!$school) {
            return $this->notFound('School');
        }

        $school->programRelation()->detach();

        $this->schoolRepository->delete($id);
        return $this->success('School deleted successfully');
    } catch (\Exception $e) {
        Log::error('Error deleting school: ' . $e->getMessage());
        return $this->internalServerError();
    }
}


    public function getDepartmentsBySchoolId($id){
        $departments = $this->schoolRepository->getDepartmentsBySchoolId($id);
        return $this->success('Departments retrieved successfully', DepartmentResource::collection($departments));
    }

}
