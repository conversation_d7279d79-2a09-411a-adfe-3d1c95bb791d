<?php

namespace App\Http\Controllers\Api\Schools;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Imports\SchoolsImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;

class ImportSchoolsController extends BaseController
{
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            Excel::import(new SchoolsImport, $request->file('file'));
            return $this->success('Schools imported successfully', null, 200);
        } catch (\Exception $e) {
            return $this->internalServerError(exception: $e->getMessage());
        }
    }
}
