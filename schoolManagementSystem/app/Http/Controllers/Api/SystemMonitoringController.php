<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseController;
use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use App\Services\SecurityAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SystemMonitoringController extends BaseController
{
    protected $cacheService;
    protected $dbOptimizationService;
    protected $securityAuditService;

    public function __construct(
        CacheService $cacheService,
        DatabaseOptimizationService $dbOptimizationService,
        SecurityAuditService $securityAuditService
    ) {
        $this->cacheService = $cacheService;
        $this->dbOptimizationService = $dbOptimizationService;
        $this->securityAuditService = $securityAuditService;
        
        // Only allow admin access
        $this->middleware('role:admin');
    }

    public function getSystemHealth()
    {
        $health = [
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'checks' => []
        ];

        // Database connectivity
        try {
            DB::connection()->getPdo();
            $health['checks']['database'] = ['status' => 'healthy', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            $health['checks']['database'] = ['status' => 'unhealthy', 'message' => 'Database connection failed: ' . $e->getMessage()];
            $health['status'] = 'unhealthy';
        }

        // Cache connectivity
        try {
            Cache::put('health_check', 'test', 10);
            $value = Cache::get('health_check');
            if ($value === 'test') {
                $health['checks']['cache'] = ['status' => 'healthy', 'message' => 'Cache is working'];
            } else {
                $health['checks']['cache'] = ['status' => 'unhealthy', 'message' => 'Cache read/write failed'];
                $health['status'] = 'degraded';
            }
        } catch (\Exception $e) {
            $health['checks']['cache'] = ['status' => 'unhealthy', 'message' => 'Cache connection failed: ' . $e->getMessage()];
            $health['status'] = 'degraded';
        }

        // Storage accessibility
        try {
            Storage::disk('public')->put('health_check.txt', 'test');
            $content = Storage::disk('public')->get('health_check.txt');
            Storage::disk('public')->delete('health_check.txt');
            
            if ($content === 'test') {
                $health['checks']['storage'] = ['status' => 'healthy', 'message' => 'Storage is accessible'];
            } else {
                $health['checks']['storage'] = ['status' => 'unhealthy', 'message' => 'Storage read/write failed'];
                $health['status'] = 'degraded';
            }
        } catch (\Exception $e) {
            $health['checks']['storage'] = ['status' => 'unhealthy', 'message' => 'Storage access failed: ' . $e->getMessage()];
            $health['status'] = 'degraded';
        }

        // Memory usage
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->convertToBytes($memoryLimit);
        $memoryPercentage = ($memoryUsage / $memoryLimitBytes) * 100;

        if ($memoryPercentage > 90) {
            $health['checks']['memory'] = ['status' => 'unhealthy', 'message' => "Memory usage critical: {$memoryPercentage}%"];
            $health['status'] = 'unhealthy';
        } elseif ($memoryPercentage > 75) {
            $health['checks']['memory'] = ['status' => 'warning', 'message' => "Memory usage high: {$memoryPercentage}%"];
            if ($health['status'] === 'healthy') $health['status'] = 'degraded';
        } else {
            $health['checks']['memory'] = ['status' => 'healthy', 'message' => "Memory usage normal: {$memoryPercentage}%"];
        }

        return $this->success('System health check completed', $health);
    }

    public function getSystemMetrics()
    {
        $metrics = [
            'timestamp' => now()->toISOString(),
            'application' => $this->getApplicationMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'storage' => $this->getStorageMetrics(),
            'performance' => $this->getPerformanceMetrics(),
        ];

        return $this->success('System metrics retrieved successfully', $metrics);
    }

    public function performSecurityAudit()
    {
        $audit = $this->securityAuditService->performSecurityAudit();
        return $this->success('Security audit completed', $audit);
    }

    public function optimizeDatabase()
    {
        try {
            $this->dbOptimizationService->addPerformanceIndexes();
            $this->dbOptimizationService->optimizeTables();
            $cleanup = $this->dbOptimizationService->cleanupOldData();
            
            return $this->success('Database optimization completed', [
                'indexes_added' => true,
                'tables_optimized' => true,
                'cleanup_results' => $cleanup
            ]);
        } catch (\Exception $e) {
            return $this->error('Database optimization failed: ' . $e->getMessage(), 500);
        }
    }

    public function clearCache(Request $request)
    {
        $type = $request->input('type', 'all');
        
        try {
            switch ($type) {
                case 'application':
                    Cache::flush();
                    break;
                case 'config':
                    \Artisan::call('config:clear');
                    break;
                case 'route':
                    \Artisan::call('route:clear');
                    break;
                case 'view':
                    \Artisan::call('view:clear');
                    break;
                case 'all':
                default:
                    Cache::flush();
                    \Artisan::call('config:clear');
                    \Artisan::call('route:clear');
                    \Artisan::call('view:clear');
                    break;
            }
            
            return $this->success("Cache cleared successfully: {$type}");
        } catch (\Exception $e) {
            return $this->error('Cache clear failed: ' . $e->getMessage(), 500);
        }
    }

    public function warmUpCache()
    {
        try {
            $this->cacheService->warmUpCache();
            return $this->success('Cache warmed up successfully');
        } catch (\Exception $e) {
            return $this->error('Cache warm up failed: ' . $e->getMessage(), 500);
        }
    }

    public function getSystemLogs(Request $request)
    {
        $type = $request->input('type', 'laravel');
        $lines = $request->input('lines', 100);
        
        try {
            $logPath = storage_path("logs/{$type}.log");
            
            if (!file_exists($logPath)) {
                return $this->error('Log file not found', 404);
            }
            
            $logs = $this->readLastLines($logPath, $lines);
            
            return $this->success('System logs retrieved successfully', [
                'type' => $type,
                'lines' => count($logs),
                'logs' => $logs
            ]);
        } catch (\Exception $e) {
            return $this->error('Failed to read logs: ' . $e->getMessage(), 500);
        }
    }

    protected function getApplicationMetrics()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'environment' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'timezone' => config('app.timezone'),
            'memory_usage' => [
                'current' => $this->formatBytes(memory_get_usage(true)),
                'peak' => $this->formatBytes(memory_get_peak_usage(true)),
                'limit' => ini_get('memory_limit')
            ],
            'uptime' => $this->getUptime(),
        ];
    }

    protected function getDatabaseMetrics()
    {
        try {
            $stats = $this->dbOptimizationService->analyzeTableStatistics();
            $performance = $this->dbOptimizationService->getPerformanceMetrics();
            
            return [
                'connection_status' => 'connected',
                'table_statistics' => $stats,
                'performance_metrics' => $performance,
            ];
        } catch (\Exception $e) {
            return [
                'connection_status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    protected function getCacheMetrics()
    {
        try {
            $driver = config('cache.default');
            
            $metrics = [
                'driver' => $driver,
                'status' => 'connected'
            ];
            
            if ($driver === 'redis') {
                // Add Redis-specific metrics if available
                $metrics['redis_info'] = 'Available';
            }
            
            return $metrics;
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    protected function getStorageMetrics()
    {
        $storagePath = storage_path();
        $publicPath = public_path('storage');
        
        return [
            'storage_path' => $storagePath,
            'storage_size' => $this->getDirectorySize($storagePath),
            'public_storage_size' => file_exists($publicPath) ? $this->getDirectorySize($publicPath) : 0,
            'disk_usage' => $this->getDiskUsage(),
        ];
    }

    protected function getPerformanceMetrics()
    {
        return [
            'response_time' => round(microtime(true) - LARAVEL_START, 3),
            'queries_count' => count(DB::getQueryLog()),
            'included_files' => count(get_included_files()),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
        ];
    }

    protected function convertToBytes($value)
    {
        $unit = strtolower(substr($value, -1));
        $value = (int) $value;
        
        switch ($unit) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }
        
        return $value;
    }

    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    protected function getDirectorySize($directory)
    {
        $size = 0;
        
        if (is_dir($directory)) {
            foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory)) as $file) {
                $size += $file->getSize();
            }
        }
        
        return $this->formatBytes($size);
    }

    protected function getDiskUsage()
    {
        $total = disk_total_space('/');
        $free = disk_free_space('/');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'percentage' => round(($used / $total) * 100, 2)
        ];
    }

    protected function getUptime()
    {
        if (function_exists('sys_getloadavg')) {
            $uptime = shell_exec('uptime');
            return trim($uptime);
        }
        
        return 'Uptime information not available';
    }

    protected function readLastLines($file, $lines)
    {
        $handle = fopen($file, 'r');
        $linecounter = $lines;
        $pos = -2;
        $beginning = false;
        $text = [];
        
        while ($linecounter > 0) {
            $t = ' ';
            while ($t != "\n") {
                if (fseek($handle, $pos, SEEK_END) == -1) {
                    $beginning = true;
                    break;
                }
                $t = fgetc($handle);
                $pos--;
            }
            $linecounter--;
            if ($beginning) {
                rewind($handle);
            }
            $text[$lines - $linecounter - 1] = fgets($handle);
            if ($beginning) break;
        }
        
        fclose($handle);
        return array_reverse($text);
    }
}
