<?php

namespace App\Http\Controllers\Api\Courses;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\CourseRequest;
use App\Http\Resources\CourseResource;
use App\Repositories\CourseRepository;
use Illuminate\Http\Request;

class CoursesController extends BaseController
{
    private $courseRepository;
    public function __construct(CourseRepository $courseRepository)
    {
        $this->courseRepository = $courseRepository;
    }

    public function index(Request $request)
    {
        $courses = CourseResource::collection($this->courseRepository->all($request));
        return $this->success('Courses fetched successfully', $courses);
    }

    public function getCourseByDepartment(Request $request){
        $courses = CourseResource::collection($this->courseRepository->getCoursesByDepartment($request->department));
        return $this->success('Courses fetched successfully', $courses);
    }

    public function getCourseByStudentDepartment(Request $request){
        $courses = CourseResource::collection($this->courseRepository->getCoursesByStudentDepartment());
        return $this->success('Courses fetched successfully', $courses);
    }
    public function show($id)
    {
        $course = $this->courseRepository->findById($id);
        if(!$course){
            return $this->notFound('Course');
        }
        return $this->success('Course fetched successfully', CourseResource::make($course));
    }

    public function store(CourseRequest $request)
    {
        $data = $request->all();
        $course = $this->courseRepository->create($data);
        return $this->success('Course created successfully', CourseResource::make($course));
    }

    public function update(Request $request, $id)
    {
        $data = $request->all();
        $course = $this->courseRepository->update($data, $id);
        return $this->success('Course updated successfully', CourseResource::make($course));
    }

    public function destroy($id)
    {
        $course = $this->courseRepository->findById($id);
        if (!$course) {
            return $this->notFound('Course');
        }
        $this->courseRepository->delete($id);
        return $this->success('Course deleted successfully');
    }
}
