<?php

namespace App\Http\Controllers\Api\Courses;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Imports\CourseImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportCoursesController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xlsx',
            'school_id' => 'required|exists:schools,id',
            'department_id' => 'required|exists:departments,id',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $school_id = $request->input('school_id');
            $department_id = $request->input('department_id');
            Excel::import(new CourseImport($school_id, $department_id), $request->file('file'));
            return $this->success('Courses imported successfully', null, 200);
        } catch (\Exception $e) {
            Log::error('Error importing departments: ' . $e->getMessage());
            return $this->internalServerError($e, 500);
        }
    }
}
