<?php

namespace App\Http\Controllers;

use GuzzleHttp\Psr7\UploadedFile;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile as HttpUploadedFile;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

abstract class BaseController extends Controller
{
    protected function success($message = 'Success', $data = null, $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $status);
    }

    protected function error($message = 'Error', $status = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message
        ], $status);
    }

    protected function unauth($message = 'Failed Login', $status = 401): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function forbidden($message = 'Forbidden', $status = 403): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function notFound($model, $status = 404): JsonResponse
    {
        return $this->error($model. ' Not Found', $status);
    }

    protected function conflict($message = 'Conflict', $status = 409): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function validationError($errors, $message = 'Validation Error', $status = 422): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $status);
    }

    protected function internalServerError($exception=null, $message = 'Internal Server Error', $stat = 500,): JsonResponse
    {
        Log::error($exception ? $exception->getMessage(): $message);
        return $this->error(message: $message, status: $stat);
    }

    protected function notImplemented($message = 'Not Implemented', $status = 501): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function serviceUnavailable($message = 'Service Unavailable', $status = 503): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function gatewayTimeout($message = 'Gateway Timeout', $status = 504): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function badGateway($message = 'Bad Gateway', $status = 502): JsonResponse
    {
        return $this->error($message, $status);
    }

    protected function uploadFile(HttpUploadedFile $file, $path)
    {
        $path = $file->store($path, 'public');
        return Storage::url($path);
    }


}
