<?php

namespace App\Http\Controllers;

use App\Enums\EventType;
use App\Models\CalendarEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CalendarEventController extends BaseController
{
    /**
     * Store multiple calendar events at once.
     */
    public function store(Request $request)
    {
        $data = $request->all();

        if (!isset($data['events']) || !is_array($data['events']) || empty($data['events'])) {
            return $this->validationError(['events' => 'Input must contain a non-empty array of events']);
        }

        $errors = [];
        $created = [];

        foreach ($data['events'] as $index => $event) {
            $validator = Validator::make($event, [
                'title' => 'required|string|max:255',
                'type' => ['required', Rule::in(EventType::values())],
                'description' => 'nullable|string',
                'event_date' => 'required|date',
                'academic_year' => 'required|regex:/^\d{4}\/\d{4}$/',
                'semester' => ['required', Rule::in(['First', 'Second', 'Third'])],
            ]);

            if ($validator->fails()) {
                $errors[$index] = $validator->errors();
                continue;
            }

            try {
                $created[] = CalendarEvent::create($validator->validated());
            } catch (\Exception $e) {
                $errors[$index] = ['exception' => $e->getMessage()];
            }
        }

        if (!empty($errors)) {
            return $this->validationError($errors, 'Some events could not be created');
        }

        return $this->success('Events created successfully', $created);
    }

    /**
     * Retrieve all calendar events (optionally filtered).
     */
    public function index(Request $request)
    {
        $query = CalendarEvent::query();

        if ($request->has('academic_year')) {
            $query->where('academic_year', $request->academic_year);
        }

        if ($request->has('semester')) {
            $query->where('semester', $request->semester);
        }

        return $this->success('Calendar events retrieved successfully', $query->orderBy('event_date')->get());
    }

    /**
     * Update a single calendar event.
     */
    public function update(Request $request, $id)
    {
        $event = CalendarEvent::find($id);

        if (!$event) {
            return $this->notFound('Calendar Event');
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'type' => ['sometimes', 'required', Rule::in(EventType::values())],
            'description' => 'nullable|string',
            'event_date' => 'sometimes|required|date',
            'academic_year' => 'sometimes|required|regex:/^\d{4}\/\d{4}$/',
            'semester' => ['sometimes', 'required', Rule::in(['First', 'Second', 'Third'])],
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors());
        }

        try {
            $event->update($validator->validated());
            return $this->success('Event updated successfully', $event);
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
