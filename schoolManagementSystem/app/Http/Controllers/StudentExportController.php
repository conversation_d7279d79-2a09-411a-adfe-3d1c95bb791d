<?php

namespace App\Http\Controllers;

use App\Models\Student;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StudentsExport;

class StudentExportController extends Controller
{
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'sometimes|in:pdf,excel',
            'academic_year_id' => 'nullable|exists:academic_years,id'
        ]);

        // Get students with their user and level relationships
        $students = Student::with(['user', 'level', 'program', 'department'])
            ->when($request->academic_year_id, function($query) use ($request) {
                $query->where('academic_year_id', $request->academic_year_id);
            })
            ->get()
            ->groupBy(function($student) {
                return optional($student->level)->name ?? 'Unknown Level';
            });

        $format = $request->format ?? 'excel';

        if ($format === 'pdf') {
            return $this->exportPdf($students);
        }

        return $this->exportExcel($students);
    }

    protected function exportPdf($groupedStudents)
    {
        $pdf = Pdf::loadView('students.students_by_level', [
            'groupedStudents' => $groupedStudents
        ]);


        // set paper to A4 and landscape
        $pdf->setPaper('a4', 'landscape');

        return $pdf->download('students_by_level.pdf');
    }

    protected function exportExcel($groupedStudents)
    {
        return Excel::download(new StudentsExport($groupedStudents), 'students_by_level.xlsx');
    }
}
