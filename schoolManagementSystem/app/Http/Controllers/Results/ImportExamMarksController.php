<?php

namespace App\Http\Controllers\Results;

use App\Enums\AcademicYear;
use App\Enums\Semester;
use App\Http\Controllers\BaseController;
use App\Imports\ExamMarksImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportExamMarksController extends BaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,xlsx,application/csv,text/csv,application/vnd.ms-excel',
            'academic_year_id' => 'required|exists:academic_years,id',
            'level' => 'required|exists:levels,id',
            'semester' => 'required|in:' . implode(',', Semester::values())
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $academic_year = $request->input('academic_year_id');
            $level = $request->input('level');
            $semester = $request->input('semester');

            Excel::import(new ExamMarksImport($academic_year, $level, $semester), $request->file('file'));

            return $this->success('Exam Marks imported successfully', null, 200);
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
