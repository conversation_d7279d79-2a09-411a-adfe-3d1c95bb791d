<?php

namespace App\Http\Controllers\Results;

use App\Enums\Semester;
use App\Http\Controllers\BaseController;
use App\Http\Resources\ResultResource;
use App\Models\AcademicYear;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use App\Repositories\ResultRepository;
use App\Repositories\StudentRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ResultsController extends BaseController
{
    private $resultRepository;
    private $studentRepository;

    public function __construct(ResultRepository $resultRepository, StudentRepository $studentRepository)
    {
        $this->resultRepository = $resultRepository;
        $this->studentRepository = $studentRepository;
    }

    public function getResults(Request $request)
    {
        $student = $this->studentRepository->findById($request->input('student_id'));

        $results = $this->resultRepository->getResults(
            $request->input('student_id'),
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id')
        );

        if ($results->isEmpty()) {
            return $this->success('No results found, please make sure you have registered your courses or have paid fee', []);
        }

        // Calculate gotten credits (sum of credits for courses with grade C or above)
        $gottenCredits = $results->sum(function ($result) {
            return $result->grade_point >= 2.0 ? $result->course->credit_value : 0;
        });

        return $this->success('Results retrieved successfully', [
            'results' => ResultResource::collection($results),
            'total' => $results->count(),
            'gotten_points' => $results->sum('grade_point'),
            'gotten_credits' => $gottenCredits,
            'total_credits' => $results->sum(function ($result) {
                return $result->course->credit_value;
            }),
            'total_weighted_points' => $results->sum('weighted_point'),
            'gpa' => $results->sum('weighted_point') / max($results->sum(function ($result) {
                return $result->load('course')->course->credit_value;
            }), 1)
        ]);
    }

    public function exportResultsToPDF(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'academic_year_id' => 'required|exists:academic_years,id',
                'semester' => 'required|in:' . implode(',', Semester::values()),
                'level_id' => 'required|exists:levels,id'
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors()->toArray());
            }
            $results = $this->resultRepository->getResults($request->input('student_id'), $request->input('academic_year_id'), $request->input('semester'), $request->input('level_id'));

            $academicYear = AcademicYear::find($request->academic_year_id);

            $totalCredits = $results->sum(function ($result) {
                return $result->course->credit_value;
            });

            // Calculate gotten credits (courses with grade C or above)
            $gottenCredits = $results->sum(function ($result) {
                return $result->grade_point >= 2.0 ? $result->course->credit_value : 0;
            });

            $totalWeightedPoints = $results->sum('weighted_point');

            $data = [
                'results' => ResultResource::collection($results),
                'total' => $results->count(),
                'gotten_points' => $results->sum('grade_point'),
                'gotten_credits' => $gottenCredits,
                'academicYear' => $academicYear,
                'total_credits' => $totalCredits,
                'total_weighted_points' => $totalWeightedPoints,
                'gpa' => $totalCredits > 0 ? $totalWeightedPoints / $totalCredits : 0,
                'semester' => Semester::getLabel($request->semester)
            ];

            $pdf = Pdf::loadView('results.pdf', compact('data'));

            return $pdf->stream('results.pdf');
        } catch (\Exception $e) {
            return $this->internalServerError($e, $e->getMessage());
        }
    }
    public function updateCAMark(Request $request)
    {
        $this->validateRequest($request);
        try {
            $results = $this->resultRepository->updateCAMark($request->input('student_id'), $request->input('course_id'), $request->input('academic_year_id'), $request->input('semester'), $request->input('level_id'), $request->input('mark'));
            return $this->success('CA mark updated successfully', $results);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    public function updateExamMark(Request $request)
    {
        $this->validateRequest($request);
        $updated = $this->resultRepository->updateExamMark($request->input('student_id'), $request->input('course_id'), $request->input('academic_year_id'), $request->input('semester'), $request->input('level_id'), $request->input('mark'));
        return $this->success('Exam mark updated successfully', $updated);
    }

    private function validateRequest(Request $request)
    {
        $validator = Validator::make([
            'student_id' => $request->input('student_id'),
            'academic_year_id' => $request->input('academic_year_id'),
            'semester' => $request->input('semester'),
            'level_id' => $request->input('level_id'),
            'mark' => $request->input('mark')
        ], [
            'student_id' => 'required|integer',
            'academic_year_id' => 'required|exists,academic_years,id',
            'semester' => 'required|string',
            'level_id' => 'required|integer',
            'mark' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }
    }

    public function getAllCAMarks(Request $request)
    {
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $results = $this->resultRepository->getAllCAMarks(
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id'),
            $perPage
        );
        return $this->success('CA marks retrieved successfully', $results);
    }

    // Method to get CA marks for a specific student with optional filters
    public function getCAMarksForStudent(Request $request)
    {
        $results = $this->resultRepository->getCAMarksForStudent(
            $request->input('student_id'),
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id'),
        );
        return $this->success('CA marks for the student retrieved successfully', $results);
    }

    // Method to get all exam marks with optional filters
    public function getAllExamMarks(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $results = $this->resultRepository->getAllExamMarks(
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id'),
            $perPage
        );
        return $this->success('Exam marks retrieved successfully', $results);
    }

    // Method to get exam marks for a specific student with optional filters
    public function getExamMarksForStudent(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $results = $this->resultRepository->getExamMarksForStudent(
            $request->input('student_id'),
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id'),
        );
        return $this->success('Exam marks for the student retrieved successfully', $results);
    }

    // Method to get all results by semester, academic year, and level, grouped by student
    public function getAllResultsBySemesterAndYearAndLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'academic_year_id' => 'sometimes|exists:academic_years,id',
            'semester' => 'sometimes|in:' . implode(',', Semester::values()),
            'level_id' => 'sometimes|exists:levels,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $perPage = $request->input('per_page', 10);
        $results = $this->resultRepository->getAllResultsBySemesterAndYearAndLevelUnpaginated(
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id'),
            $perPage
        );
        return $this->success('Results retrieved successfully', $results);
    }

    public function getAllResultsGrouped(Request $request)
    {
        // Get the results without pagination
        $results = $this->resultRepository->getAllResultsBySemesterAndYearAndLevelUnpaginated(
            $request->input('academic_year_id'),
            $request->input('semester'),
            $request->input('level_id')
        );

        // Group the results by level and then by course
        $resultsByLevel = $results->groupBy(function ($result) {
            return $result->course->level->name;
        })->map(function ($levelResults) {
            return $levelResults->groupBy('course.name');
        });

        // Generate the PDF
        $pdf = Pdf::loadView('results.general', compact('resultsByLevel'))
            ->setPaper('a4', 'landscape');


        // Stream the PDF back to the user
        return $pdf->stream('grouped_results.pdf');
    }

}
