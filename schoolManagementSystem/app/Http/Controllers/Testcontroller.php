<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class Testcontroller extends BaseController
{
    function allstudents(){
        $students =  User::with('student')->whereHas('student')->get();

        return $this->success("All students", $students);

    }

    function allStaffs(){
        $staffs =  User::whereDoesntHave("student")->get();
        return $this->success("All staffs", $staffs);

    }
}
