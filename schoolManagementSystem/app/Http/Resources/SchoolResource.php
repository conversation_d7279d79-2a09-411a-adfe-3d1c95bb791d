<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class SchoolResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'admin_id' => $this->admin_id,
            'school_email' => $this->school_email,
            'address' => $this->address,
            'logo' => $this->logo,
            'director_name' => $this->director_name,
            'programs' => $this->whenLoaded('programRelation', function () {
                return ProgramResource::collection($this->programRelation);
            }),


            'admin' => [
                'id' => $this->whenLoaded('admin', function () {
                    return $this->admin->id;
                }),
                'name' => $this->whenLoaded('admin', function () {
                    return $this->admin->first_name . ' ' . $this->admin->last_name;
                }),
                'email' => $this->whenLoaded('admin', function () {
                    return $this->admin->email;
                }),

            ],
            'created_at' => Carbon::parse($this->created_at)->format('d/m/Y'),
            'updated_at' => Carbon::parse($this->updated_at)->format('d/m/Y'),
        ];
    }
}
