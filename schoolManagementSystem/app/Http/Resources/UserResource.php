<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'gender' => $this->gender,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'dob' => $this->dob,
            'profile_image' => $this->profile_image,
            'remember_token' => $this->remember_token,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'role' => $this->whenLoaded('role', function () {
                return $this->role->name;
            }),
            'department' => $this->whenLoaded('department', function () {
                return DepartmentResource::make($this->department);
            }),
            'student' => $this->whenLoaded('student', function () {
                return StudentResource::make($this->student);
            }),
        ];
    }
}
