<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\StudentResource;
use App\Http\Resources\CourseResource;

class ResultResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'student' => new StudentResource($this->student),
            'course' => new CourseResource($this->course),
            'ca_mark' => $this->ca_mark,
            'exam_mark' => $this->exam_mark,
            'total_mark' => $this->total_mark,
            'grade' => $this->grade,
            'grade_point' => $this->grade_point
        ];
    }
}
