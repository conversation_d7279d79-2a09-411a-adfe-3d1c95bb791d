<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'student_id' => $this->id,
            'matricule' => $this->matricule,
            'place_of_birth' => $this->place_of_birth,
            'region_of_origin' => $this->region_of_origin,
            'marital_status' => $this->marital_status,
            'date_of_admission' => $this->date_of_admission,
            'department_id' => $this->department_id,
            'nationality' => $this->nationality,

            'userInfo' => [
                'id' => $this->user->id,
                'first_name' => $this->user->first_name,
                'last_name' => $this->user->last_name,
                'gender' => $this->user->gender,
                'email' => $this->user->email,
                'phone_number' => $this->user->phone_number,
                'dob' => $this->user->dob,
                'profile_image' => $this->user->profile_image,
                'remember_token' => $this->user->remember_token,
                'created_at' => $this->user->created_at,
                'updated_at' => $this->user->updated_at,
            ],

            'department' => $this->whenLoaded('department', function () {
                return DepartmentResource::make($this->department);
            }),
            'level' => $this->whenLoaded('currentLevel', function () {
                return LevelResource::make($this->currentLevel);
            }),
            'program' => ProgramResource::make($this->program),
            'academic_year' => $this->academicYear,
        ];
    }
}
