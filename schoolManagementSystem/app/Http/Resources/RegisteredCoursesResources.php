<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RegisteredCoursesResources extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'course_id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'credit_value' => $this->credit_value,
            'credit_hours' => $this->credit_hours,
            'level' => $this->whenLoaded('level', function() {
                return $this->level->name;
            }),
            'semester' => $this->pivot->semester,
            'academic_year' => $this->pivot->academic_year,
        ];
    }
}
