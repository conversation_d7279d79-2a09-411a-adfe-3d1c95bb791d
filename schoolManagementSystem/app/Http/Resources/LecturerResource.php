<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class LecturerResource extends JsonResource
{
    public function toArray($request)
    {

        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'gender' => $this->gender,
            'dob' => $this->dob,
            'profile_image' => $this->profile_image,
            'created_at' => Carbon::parse($this->created_at)->format('d/m/Y'),
            'updated_at' => Carbon::parse($this->updated_at)->format('d/m/Y'),
            'schools' => $this->whenLoaded('schools', function () {
                return SchoolResource::collection($this->schools) ?? null;
            }),
            'departments' => $this->whenLoaded('departments', function () {
                return DepartmentResource::collection($this->departments) ?? null;
            }),
            'courses'=> $this->whenLoaded('lectureCourses', function () {
                return $this->lectureCourses;
            }),
        ];
    }
}
