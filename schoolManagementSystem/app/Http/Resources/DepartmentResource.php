<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'school_id' => $this->school_id,
            'admin_id' => $this->admin_id,
            'admin' => [
                'id' => $this->whenLoaded('admin', function () {
                    return $this->admin->id;
                }),
                'name' => $this->whenLoaded('admin', function () {
                    return $this->admin->first_name . ' ' . $this->admin->last_name;
                }),
                'email' => $this->whenLoaded('admin', function () {
                    return $this->admin->email;
                }),

            ],
            'school' => $this->whenLoaded('school', function () {
                return $this->school;
            }),
            'created_at' => Carbon::parse($this->created_at)->format('d/m/Y'),
            'updated_at' => Carbon::parse($this->updated_at)->format('d/m/Y'),
        ];
    }
}
