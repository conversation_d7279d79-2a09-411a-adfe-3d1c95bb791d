<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'semester' => $this->semester,
            'credit_value' => $this->credit_value,
            'credit_hours' => $this->credit_hours,
            'level_id' => $this->level_id,
            'department_id' => $this->department_id,
            'lecturer_id' => $this->lecturer_id,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
            'updated_at' => Carbon::parse($this->updated_at)->format('Y-m-d'),
            'level' => new LevelResource($this->whenLoaded('level')),
            'lecturer' => new UserResource($this->whenLoaded('lecturer')),
            'department' => new DepartmentResource($this->whenLoaded('department')),
        ];
    }
}
