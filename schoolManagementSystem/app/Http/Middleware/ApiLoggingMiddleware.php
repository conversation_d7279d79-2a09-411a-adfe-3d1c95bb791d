<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiLoggingMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        
        // Log request
        $this->logRequest($request);
        
        $response = $next($request);
        
        // Log response
        $this->logResponse($request, $response, $startTime);
        
        return $response;
    }
    
    /**
     * Log incoming request
     */
    protected function logRequest(Request $request): void
    {
        $data = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id,
            'timestamp' => now()->toISOString(),
        ];
        
        // Don't log sensitive data
        if (!$this->shouldLogBody($request)) {
            $data['body'] = '[FILTERED]';
        } else {
            $data['body'] = $request->except(['password', 'password_confirmation', 'token']);
        }
        
        Log::channel('api')->info('API Request', $data);
    }
    
    /**
     * Log response
     */
    protected function logResponse(Request $request, Response $response, float $startTime): void
    {
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        
        $data = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'status' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'user_id' => $request->user()?->id,
            'timestamp' => now()->toISOString(),
        ];
        
        // Log errors with more detail
        if ($response->getStatusCode() >= 400) {
            $data['response_body'] = $response->getContent();
            Log::channel('api')->error('API Error Response', $data);
        } else {
            Log::channel('api')->info('API Response', $data);
        }
        
        // Log slow requests
        if ($duration > 1000) { // More than 1 second
            Log::channel('performance')->warning('Slow API Request', $data);
        }
    }
    
    /**
     * Determine if request body should be logged
     */
    protected function shouldLogBody(Request $request): bool
    {
        // Don't log file uploads or sensitive endpoints
        $sensitiveEndpoints = [
            'login',
            'register',
            'password',
            'upload',
        ];
        
        foreach ($sensitiveEndpoints as $endpoint) {
            if (str_contains($request->path(), $endpoint)) {
                return false;
            }
        }
        
        return !$request->hasFile('file') && !$request->hasFile('files');
    }
}
