<?php

namespace App\Http\Requests;

use App\Enums\FeeStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'student_id' => 'required|exists:students,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'fee_type_id' => 'required|exists:fee_types,id',
            'account_id' => 'required|exists:accounts,id',
            'amount' => 'required|numeric',
            'status' => ['required', Rule::in(FeeStatus::all())],
            'reference' => 'nullable|string',
            'is_installment' => 'required|boolean',
            'bank_ref' => 'required|string',
            'payment_date' => 'required|date',
            'payment_channel' => 'required|string',
        ];
    }
}
