<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDepartmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // You can add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'school_id' => 'required|exists:schools,id',
            'code' => 'required|string|max:255|unique:departments,code,' . $this->route('department'),
            'description' => 'nullable|string',
            'admin_id' => 'nullable|exists:users,id'
        ];
    }
}
