<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'gender' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:255',
            'dob' => 'nullable|date',
            'profile_image' => 'nullable|string|max:255',
            'password' => 'required|string|min:8',
            'matricule' => 'required|unique:students,matricule|string|max:255',
            'place_of_birth' => 'nullable|string|max:255',
            'region_of_origin' => 'nullable|string|max:255',
            'marital_status' => 'nullable|string|max:255',
            'program_id' => 'required|max:255',
            'date_of_admission' => 'nullable|date',
            'department_id' => 'required|exists:departments,id',
            'nationality' => 'nullable|string|max:255',
            'level_id' => 'required|exists:levels,id',
        ];
    }
}
