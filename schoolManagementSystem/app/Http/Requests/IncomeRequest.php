<?php

namespace App\Http\Requests;

use App\Enums\IncomeCategory;
use Illuminate\Foundation\Http\FormRequest;

class IncomeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric',
            'account_id' => 'required|exists:accounts,id',
            'income_date' => 'required|date',
            'description' => 'nullable',
            'reference' => 'nullable',
            'income_category_id' => 'required|exists:income_categories,id',
            'from' => 'required|string'
        ];
    }
}
