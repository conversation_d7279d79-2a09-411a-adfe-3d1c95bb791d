<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'code' => 'required|unique:courses,code|string|max:255',
            'department_id' => 'required|exists:departments,id',
            'lecturer_id' => 'required|exists:users,id',
            'credit_value' => 'required|numeric',
            'credit_hours' => 'nullable|numeric',
            'level_id' => 'required|exists:levels,id',
            'is_general' => 'required|boolean',
        ];
    }

}
