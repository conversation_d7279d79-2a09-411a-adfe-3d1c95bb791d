<?php

namespace App\Http\Requests;

use App\Enums\ExpenseCategory;
use Illuminate\Foundation\Http\FormRequest;

class ExpenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric',
            'account_id' => 'required|exists:accounts,id',
            'expense_date' => 'required|date',
            'description' => 'nullable',
            'reference' => 'nullable',
            'expense_category_id' => 'required|exists:expense_categories,id',
            'to' => 'required|string',
            'lecturer_id' => 'nullable|exists:users,id',
            'student_id' => 'nullable|exists:students,id'
        ];
    }
}
