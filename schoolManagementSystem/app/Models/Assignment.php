<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Assignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'created_by',
        'academic_year_id',
        'level_id',
        'title',
        'description',
        'instructions',
        'type',
        'max_score',
        'weight_percentage',
        'start_date',
        'due_date',
        'late_submission_deadline',
        'allow_late_submission',
        'late_penalty_percentage',
        'is_group_assignment',
        'max_group_size',
        'allowed_file_types',
        'max_file_size_mb',
        'max_files',
        'is_published',
        'grading_rubric',
        'metadata'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'late_submission_deadline' => 'datetime',
        'allow_late_submission' => 'boolean',
        'is_group_assignment' => 'boolean',
        'is_published' => 'boolean',
        'allowed_file_types' => 'array',
        'metadata' => 'array',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function submissions()
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    public function groups()
    {
        return $this->hasMany(AssignmentGroup::class);
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date < now();
    }

    public function getIsActiveAttribute()
    {
        return $this->start_date <= now() && $this->due_date >= now();
    }

    public function getTimeRemainingAttribute()
    {
        if ($this->is_overdue) {
            return null;
        }
        
        return $this->due_date->diffForHumans();
    }

    public function getSubmissionCountAttribute()
    {
        return $this->submissions()->where('status', '!=', 'draft')->count();
    }

    public function getGradedCountAttribute()
    {
        return $this->submissions()->where('status', 'graded')->count();
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeActive($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where('due_date', '>=', now());
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now());
    }

    public function getSubmissionForStudent($studentId)
    {
        return $this->submissions()
                   ->where('student_id', $studentId)
                   ->orderBy('attempt_number', 'desc')
                   ->first();
    }

    public function canSubmit($studentId = null)
    {
        if (!$this->is_published) {
            return false;
        }

        if ($this->start_date > now()) {
            return false;
        }

        if ($this->due_date < now() && !$this->allow_late_submission) {
            return false;
        }

        if ($this->allow_late_submission && $this->late_submission_deadline && $this->late_submission_deadline < now()) {
            return false;
        }

        return true;
    }
}
