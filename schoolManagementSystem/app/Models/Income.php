<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Income extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'student_id',
        'income_date',
        'amount',
        'account_id',
        'description',
        'from',
        'type',
        'income_category_id',
        'reference'
    ];


    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function account(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Account::class);
    }


    public function transactions(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function category(){
        return $this->belongsTo(IncomeCategory::class, 'income_category_id', 'id');
    }

}
