<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssignmentGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'assignment_id',
        'name',
        'description',
        'leader_id',
        'max_members',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    public function leader()
    {
        return $this->belongsTo(Student::class, 'leader_id');
    }

    public function members()
    {
        return $this->belongsToMany(Student::class, 'assignment_group_members', 'group_id', 'student_id')
                   ->withPivot('joined_at', 'is_active')
                   ->withTimestamps();
    }

    public function activeMembers()
    {
        return $this->members()->wherePivot('is_active', true);
    }

    public function submissions()
    {
        return $this->hasMany(AssignmentSubmission::class, 'group_id');
    }

    public function getCurrentMemberCountAttribute()
    {
        return $this->activeMembers()->count();
    }

    public function getIsFullAttribute()
    {
        return $this->current_member_count >= $this->max_members;
    }

    public function getCanJoinAttribute()
    {
        return $this->is_active && !$this->is_full;
    }

    public function addMember($studentId)
    {
        if ($this->is_full) {
            return false;
        }

        $this->members()->attach($studentId, [
            'joined_at' => now(),
            'is_active' => true
        ]);

        return true;
    }

    public function removeMember($studentId)
    {
        $this->members()->updateExistingPivot($studentId, [
            'is_active' => false
        ]);
    }

    public function isMember($studentId)
    {
        return $this->activeMembers()->where('students.id', $studentId)->exists();
    }

    public function isLeader($studentId)
    {
        return $this->leader_id == $studentId;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithSpace($query)
    {
        return $query->whereRaw('(SELECT COUNT(*) FROM assignment_group_members WHERE group_id = assignment_groups.id AND is_active = 1) < max_members');
    }
}
