<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AssessmentAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'assessment_id',
        'student_id',
        'attempt_number',
        'started_at',
        'submitted_at',
        'auto_submitted_at',
        'time_spent_minutes',
        'total_score',
        'percentage',
        'status',
        'question_order',
        'metadata'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'submitted_at' => 'datetime',
        'auto_submitted_at' => 'datetime',
        'question_order' => 'array',
        'metadata' => 'array',
    ];

    public function assessment()
    {
        return $this->belongsTo(OnlineAssessment::class, 'assessment_id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function answers()
    {
        return $this->hasMany(AssessmentAnswer::class, 'attempt_id');
    }

    public function getTimeRemainingAttribute()
    {
        if ($this->status !== 'in_progress' || !$this->assessment->duration_minutes) {
            return null;
        }
        
        $endTime = $this->started_at->addMinutes($this->assessment->duration_minutes);
        
        if ($endTime <= now()) {
            return 0;
        }
        
        return $endTime->diffInMinutes(now());
    }

    public function getIsExpiredAttribute()
    {
        if ($this->status !== 'in_progress') {
            return false;
        }
        
        // Check assessment end time
        if ($this->assessment->end_time <= now()) {
            return true;
        }
        
        // Check duration limit
        if ($this->assessment->duration_minutes) {
            $endTime = $this->started_at->addMinutes($this->assessment->duration_minutes);
            return $endTime <= now();
        }
        
        return false;
    }

    public function getGradeLetterAttribute()
    {
        if (!$this->percentage) {
            return null;
        }
        
        if ($this->percentage >= 90) return 'A';
        if ($this->percentage >= 80) return 'B';
        if ($this->percentage >= 70) return 'C';
        if ($this->percentage >= 60) return 'D';
        return 'F';
    }

    public function getAnsweredQuestionsCountAttribute()
    {
        return $this->answers()->whereNotNull('answer')->count();
    }

    public function getTotalQuestionsCountAttribute()
    {
        return $this->assessment->questions()->count();
    }

    public function getProgressPercentageAttribute()
    {
        $total = $this->total_questions_count;
        
        if ($total === 0) {
            return 0;
        }
        
        return round(($this->answered_questions_count / $total) * 100, 2);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeGraded($query)
    {
        return $query->where('status', 'graded');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function submit($autoSubmit = false)
    {
        $this->calculateScore();
        
        $this->update([
            'status' => 'submitted',
            'submitted_at' => $autoSubmit ? null : now(),
            'auto_submitted_at' => $autoSubmit ? now() : null,
            'time_spent_minutes' => $this->started_at->diffInMinutes(now())
        ]);
        
        return $this;
    }

    public function calculateScore()
    {
        $totalMarks = 0;
        $earnedMarks = 0;
        
        foreach ($this->assessment->questions as $question) {
            $totalMarks += $question->marks;
            
            $answer = $this->answers()->where('question_id', $question->id)->first();
            if ($answer && $answer->marks_awarded !== null) {
                $earnedMarks += $answer->marks_awarded;
            }
        }
        
        $percentage = $totalMarks > 0 ? round(($earnedMarks / $totalMarks) * 100, 2) : 0;
        
        $this->update([
            'total_score' => $earnedMarks,
            'percentage' => $percentage
        ]);
        
        return $this;
    }

    public function autoGrade()
    {
        foreach ($this->answers as $answer) {
            if ($answer->marks_awarded === null) {
                $question = $answer->question;
                $marksAwarded = $question->getMarksAwarded($answer->answer);
                
                if ($marksAwarded !== null) {
                    $answer->update([
                        'marks_awarded' => $marksAwarded,
                        'is_correct' => $marksAwarded > 0
                    ]);
                }
            }
        }
        
        $this->calculateScore();
        
        // Check if all questions are graded
        $ungradedCount = $this->answers()
            ->whereNull('marks_awarded')
            ->count();
        
        if ($ungradedCount === 0) {
            $this->update(['status' => 'graded']);
        }
        
        return $this;
    }

    public function getQuestionOrder()
    {
        if ($this->question_order) {
            return $this->question_order;
        }
        
        $questions = $this->assessment->questions()->ordered()->pluck('id')->toArray();
        
        if ($this->assessment->shuffle_questions) {
            shuffle($questions);
        }
        
        $this->update(['question_order' => $questions]);
        
        return $questions;
    }

    public function getOrderedQuestions()
    {
        $questionOrder = $this->getQuestionOrder();
        
        return $this->assessment->questions()
            ->whereIn('id', $questionOrder)
            ->get()
            ->sortBy(function ($question) use ($questionOrder) {
                return array_search($question->id, $questionOrder);
            });
    }

    public function canReview()
    {
        return $this->status === 'submitted' && 
               $this->assessment->allow_review && 
               ($this->assessment->show_results_immediately || $this->status === 'graded');
    }
}
