<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssessmentAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'attempt_id',
        'question_id',
        'answer',
        'marks_awarded',
        'is_correct',
        'feedback',
        'answered_at',
        'metadata'
    ];

    protected $casts = [
        'answer' => 'array',
        'is_correct' => 'boolean',
        'answered_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function attempt()
    {
        return $this->belongsTo(AssessmentAttempt::class, 'attempt_id');
    }

    public function question()
    {
        return $this->belongsTo(AssessmentQuestion::class, 'question_id');
    }

    public function getFormattedAnswerAttribute()
    {
        if (!$this->answer) {
            return null;
        }

        switch ($this->question->type) {
            case 'multiple_choice':
                if (is_array($this->answer)) {
                    return collect($this->answer)->map(function ($index) {
                        return chr(65 + $index); // Convert to A, B, C, D...
                    })->implode(', ');
                }
                return chr(65 + $this->answer);
                
            case 'true_false':
                return $this->answer ? 'True' : 'False';
                
            case 'short_answer':
            case 'essay':
                return $this->answer;
                
            case 'fill_blank':
                if (is_array($this->answer)) {
                    return implode(', ', $this->answer);
                }
                return $this->answer;
                
            default:
                return $this->answer;
        }
    }

    public function getIsAnsweredAttribute()
    {
        return $this->answer !== null && $this->answer !== '' && $this->answer !== [];
    }

    public function autoGrade()
    {
        if ($this->marks_awarded !== null) {
            return $this; // Already graded
        }

        $marksAwarded = $this->question->getMarksAwarded($this->answer);
        
        if ($marksAwarded !== null) {
            $this->update([
                'marks_awarded' => $marksAwarded,
                'is_correct' => $marksAwarded > 0
            ]);
        }
        
        return $this;
    }

    public function grade($marks, $feedback = null)
    {
        $maxMarks = $this->question->marks;
        $marks = max(0, min($marks, $maxMarks)); // Ensure marks are within valid range
        
        $this->update([
            'marks_awarded' => $marks,
            'is_correct' => $marks > 0,
            'feedback' => $feedback
        ]);
        
        return $this;
    }

    public function scopeCorrect($query)
    {
        return $query->where('is_correct', true);
    }

    public function scopeIncorrect($query)
    {
        return $query->where('is_correct', false);
    }

    public function scopeAnswered($query)
    {
        return $query->whereNotNull('answer')
                    ->where('answer', '!=', '')
                    ->where('answer', '!=', '[]');
    }

    public function scopeUnanswered($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('answer')
              ->orWhere('answer', '')
              ->orWhere('answer', '[]');
        });
    }

    public function scopeGraded($query)
    {
        return $query->whereNotNull('marks_awarded');
    }

    public function scopeUngraded($query)
    {
        return $query->whereNull('marks_awarded');
    }
}
