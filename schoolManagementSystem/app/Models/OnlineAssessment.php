<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class OnlineAssessment extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'created_by',
        'academic_year_id',
        'level_id',
        'title',
        'description',
        'instructions',
        'type',
        'total_marks',
        'duration_minutes',
        'start_time',
        'end_time',
        'shuffle_questions',
        'show_results_immediately',
        'allow_review',
        'max_attempts',
        'is_published',
        'require_password',
        'password',
        'settings'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'shuffle_questions' => 'boolean',
        'show_results_immediately' => 'boolean',
        'allow_review' => 'boolean',
        'is_published' => 'boolean',
        'require_password' => 'boolean',
        'settings' => 'array',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function questions()
    {
        return $this->hasMany(AssessmentQuestion::class, 'assessment_id');
    }

    public function attempts()
    {
        return $this->hasMany(AssessmentAttempt::class, 'assessment_id');
    }

    public function getIsActiveAttribute()
    {
        return $this->start_time <= now() && $this->end_time >= now();
    }

    public function getIsUpcomingAttribute()
    {
        return $this->start_time > now();
    }

    public function getIsExpiredAttribute()
    {
        return $this->end_time < now();
    }

    public function getTimeRemainingAttribute()
    {
        if ($this->is_expired) {
            return null;
        }
        
        return $this->end_time->diffForHumans();
    }

    public function getTotalQuestionsAttribute()
    {
        return $this->questions()->count();
    }

    public function getAttemptCountAttribute()
    {
        return $this->attempts()->count();
    }

    public function getCompletedAttemptsAttribute()
    {
        return $this->attempts()->where('status', 'submitted')->count();
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeActive($query)
    {
        return $query->where('start_time', '<=', now())
                    ->where('end_time', '>=', now());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('end_time', '<', now());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function canTakeAssessment($studentId = null)
    {
        if (!$this->is_published) {
            return false;
        }

        if (!$this->is_active) {
            return false;
        }

        if ($studentId) {
            $attemptCount = $this->attempts()
                ->where('student_id', $studentId)
                ->count();
            
            if ($attemptCount >= $this->max_attempts) {
                return false;
            }
        }

        return true;
    }

    public function getStudentAttempts($studentId)
    {
        return $this->attempts()
            ->where('student_id', $studentId)
            ->orderBy('attempt_number', 'desc')
            ->get();
    }

    public function getLatestAttempt($studentId)
    {
        return $this->attempts()
            ->where('student_id', $studentId)
            ->orderBy('attempt_number', 'desc')
            ->first();
    }

    public function getRemainingAttempts($studentId)
    {
        $usedAttempts = $this->attempts()
            ->where('student_id', $studentId)
            ->count();
        
        return max(0, $this->max_attempts - $usedAttempts);
    }

    public function getAverageScore()
    {
        return $this->attempts()
            ->where('status', 'submitted')
            ->whereNotNull('total_score')
            ->avg('total_score');
    }

    public function getHighestScore()
    {
        return $this->attempts()
            ->where('status', 'submitted')
            ->whereNotNull('total_score')
            ->max('total_score');
    }

    public function getPassRate($passingScore = 60)
    {
        $totalAttempts = $this->attempts()
            ->where('status', 'submitted')
            ->whereNotNull('percentage')
            ->count();
        
        if ($totalAttempts === 0) {
            return 0;
        }
        
        $passedAttempts = $this->attempts()
            ->where('status', 'submitted')
            ->where('percentage', '>=', $passingScore)
            ->count();
        
        return round(($passedAttempts / $totalAttempts) * 100, 2);
    }
}
