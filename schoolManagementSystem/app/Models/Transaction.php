<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'amount',
        'transactionable_id',
        'transactionable_type',
        'reference',
        'created_by_id',
        'status',
        'description',
        'transaction_date',
        'transaction_type'
    ];


    public function transactionable()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function getStudentAttribute()
    {
        // If the transactionable type is Income, return the student relation
        if ($this->transactionable_type === Income::class) {
            return $this->transactionable->student ?? null;
        }

        // If the transactionable type is Expense, return the student relation
        return $this->transactionable->student ?? null;
    }

    public function getLecturerAttribute()
    {
        // If the transactionable type is Expense, return the lecturer relation
        if ($this->transactionable_type === Expense::class) {
            return $this->transactionable->lecturer ?? null;
        }

        return null; // Return null for Income since it has no lecturer
    }
}
