<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContinuousAssessment extends Model
{
    use HasFactory;

    protected $fillable = ['course_id', 'student_id', 'score', 'academic_year_id'];

    public function results()
    {
        return $this->morphMany(Result::class, 'resultable');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function level()
    {
        return $this->hasManyThrough(Level::class, Result::class, 'resultable_id', 'id');
    }
}
