<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Installment extends Model
{
    use HasFactory;

    protected $fillable = [
        'fee_id',
        'amount',
        'academic_year_id',
        'student_id',
        'reference'
    ];


    public function fee()
    {
        return $this->belongsTo(Fee::class);
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }
}
