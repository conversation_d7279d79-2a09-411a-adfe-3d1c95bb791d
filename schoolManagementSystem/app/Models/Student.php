<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'matricule',
        'place_of_birth',
        'region_of_origin',
        'marital_status',
        'program_offered',
        'date_of_admission',
        'department_id',
        'nationality',
        'level_id',
        'current_level_id',
        'program_id',
        'academic_year_id'
    ];

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function department(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class)
            ->withPivot('academic_year', 'level_id', 'semester')
            ->withTimestamps();
    }
    public function results(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Result::class);
    }
    public function level(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Level::class, 'level_id', 'id');
    }

    public function program(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function academicYear(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function fees(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Fee::class);
    }

    public function has_paid_fee($academic_year, $semester)
    {

        // Check if the student has paid the registration fee

        $registration_fee = $this->fees()
            ->where('academic_year_id', $academic_year)
            ->where('fee_type_id', 1) // Assuming 1 represents registration fee.
            ->exists();


        if ($semester == 1) {
            Log::info("Semester 1: " . $this->fees()->where('academic_year_id', $academic_year)->exists());

            // Check if the student has paid at least part of the tuition fee.
            $tuition_fee = $this->fees()
                ->where('academic_year_id', $academic_year)
                ->exists();

            // Return true if either registration or tuition fee has been paid.
            if ($registration_fee || $tuition_fee) {
                return true;
            }
        }

        // For Semester 2: Check if the student has fully completed their fee payment.
        if ($semester == 2) {
            return $this->fees()
                ->where('academic_year_id', $academic_year)
                ->where('status', 'complete') // Assuming 'complete' indicates full payment.
                ->exists() && $registration_fee;
        }

        // If none of the conditions match, return false.
        return false;
    }


    public function currentLevel()
    {
        return $this->belongsTo(Level::class, 'current_level_id', 'id');
    }
}
