<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class AssignmentSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'assignment_id',
        'student_id',
        'group_id',
        'submission_text',
        'file_paths',
        'file_metadata',
        'submitted_at',
        'is_late',
        'status',
        'score',
        'feedback',
        'private_notes',
        'graded_at',
        'graded_by',
        'attempt_number',
        'metadata'
    ];

    protected $casts = [
        'file_paths' => 'array',
        'file_metadata' => 'array',
        'submitted_at' => 'datetime',
        'graded_at' => 'datetime',
        'is_late' => 'boolean',
        'metadata' => 'array',
    ];

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function group()
    {
        return $this->belongsTo(AssignmentGroup::class);
    }

    public function gradedBy()
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    public function getGradePercentageAttribute()
    {
        if (!$this->score || !$this->assignment) {
            return null;
        }
        
        return round(($this->score / $this->assignment->max_score) * 100, 2);
    }

    public function getGradeLetterAttribute()
    {
        $percentage = $this->grade_percentage;
        
        if ($percentage === null) return null;
        
        if ($percentage >= 90) return 'A';
        if ($percentage >= 80) return 'B';
        if ($percentage >= 70) return 'C';
        if ($percentage >= 60) return 'D';
        return 'F';
    }

    public function getFileUrlsAttribute()
    {
        if (!$this->file_paths) {
            return [];
        }
        
        return collect($this->file_paths)->map(function ($path) {
            return Storage::url($path);
        })->toArray();
    }

    public function getDownloadUrlsAttribute()
    {
        if (!$this->file_paths) {
            return [];
        }
        
        return collect($this->file_paths)->map(function ($path, $index) {
            return route('assignment-submissions.download', [
                'submission' => $this->id,
                'file' => $index
            ]);
        })->toArray();
    }

    public function getTotalFileSizeAttribute()
    {
        if (!$this->file_metadata) {
            return 0;
        }
        
        return collect($this->file_metadata)->sum('size');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', '!=', 'draft');
    }

    public function scopeGraded($query)
    {
        return $query->where('status', 'graded');
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('attempt_number', 'desc');
    }

    public function markAsSubmitted()
    {
        $this->update([
            'status' => 'submitted',
            'submitted_at' => now(),
            'is_late' => $this->assignment->due_date < now()
        ]);
    }

    public function grade($score, $feedback = null, $gradedBy = null)
    {
        $this->update([
            'score' => $score,
            'feedback' => $feedback,
            'status' => 'graded',
            'graded_at' => now(),
            'graded_by' => $gradedBy
        ]);
    }
}
