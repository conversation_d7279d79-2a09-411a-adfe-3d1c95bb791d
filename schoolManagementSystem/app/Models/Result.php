<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\CalculatesGradePoint;

class Result extends Model
{
    use HasFactory, CalculatesGradePoint;

    protected $fillable = [
        'course_id', 'student_id', 'ca_mark', 'exam_mark',
        'total_mark', 'academic_year_id', 'semester', 'grade',
        'resultable_id', 'resultable_type', 'level_id', 'grade_point',
        'weighted_point'
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::saving(function ($result) {
            $result->calculateTotalAndGrade();
        });
    }

    public function resultable(): \Illuminate\Database\Eloquent\Relations\MorphTo
    {
        return $this->morphTo();
    }


    public function academicYear(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);

    }
    public function course(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function student(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function level(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Level::class);
    }

    public function calculateTotalAndGrade(): void
    {
        $total = ($this->ca_mark ?? 0) + ($this->exam_mark ?? 0);
        $this->total_mark = $total;

        list($this->grade, $this->grade_point) = $this->calculateGradePoint($total);

        if ($this->course) {
            $this->weighted_point = $this->course->credit_value * $this->grade_point;
        }
    }
}
