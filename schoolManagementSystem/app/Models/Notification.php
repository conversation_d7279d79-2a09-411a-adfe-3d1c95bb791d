<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'action_url',
        'priority',
        'is_read',
        'read_at',
        'is_sent',
        'sent_at',
        'expires_at'
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'is_sent' => 'boolean',
        'read_at' => 'datetime',
        'sent_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getIsExpiredAttribute()
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expires_at')
                    ->where('expires_at', '<=', now());
    }

    public function scopeUnsent($query)
    {
        return $query->where('is_sent', false);
    }

    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now()
        ]);
    }

    public function markAsSent()
    {
        $this->update([
            'is_sent' => true,
            'sent_at' => now()
        ]);
    }

    public static function createForUser($userId, $type, $title, $message, $data = null, $actionUrl = null, $priority = 'medium')
    {
        return static::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'action_url' => $actionUrl,
            'priority' => $priority
        ]);
    }

    public static function createForMultipleUsers($userIds, $type, $title, $message, $data = null, $actionUrl = null, $priority = 'medium')
    {
        $notifications = [];
        
        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'action_url' => $actionUrl,
                'priority' => $priority,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        
        return static::insert($notifications);
    }

    public static function notifyAssignmentCreated($assignment, $studentIds)
    {
        $title = "New Assignment: {$assignment->title}";
        $message = "A new assignment has been posted for {$assignment->course->name}. Due date: {$assignment->due_date->format('M d, Y')}";
        $actionUrl = "/student/assignments/{$assignment->id}";
        
        return static::createForMultipleUsers(
            $studentIds,
            'assignment',
            $title,
            $message,
            ['assignment_id' => $assignment->id, 'course_id' => $assignment->course_id],
            $actionUrl,
            'medium'
        );
    }

    public static function notifyGradeReleased($submission)
    {
        $title = "Grade Released: {$submission->assignment->title}";
        $message = "Your assignment has been graded. Score: {$submission->score}/{$submission->assignment->max_score}";
        $actionUrl = "/student/assignments/{$submission->assignment->id}/submission";
        
        return static::createForUser(
            $submission->student->user_id,
            'grade',
            $title,
            $message,
            ['submission_id' => $submission->id, 'assignment_id' => $submission->assignment->id],
            $actionUrl,
            'medium'
        );
    }

    public static function notifyAssessmentAvailable($assessment, $studentIds)
    {
        $title = "Assessment Available: {$assessment->title}";
        $message = "A new {$assessment->type} is now available for {$assessment->course->name}. Available until: {$assessment->end_time->format('M d, Y H:i')}";
        $actionUrl = "/student/assessments/{$assessment->id}";
        
        return static::createForMultipleUsers(
            $studentIds,
            'assessment',
            $title,
            $message,
            ['assessment_id' => $assessment->id, 'course_id' => $assessment->course_id],
            $actionUrl,
            'high'
        );
    }

    public static function notifyCourseMaterialAdded($material, $studentIds)
    {
        $title = "New Course Material: {$material->title}";
        $message = "New learning material has been added to {$material->course->name}";
        $actionUrl = "/student/courses/{$material->course_id}/materials";
        
        return static::createForMultipleUsers(
            $studentIds,
            'material',
            $title,
            $message,
            ['material_id' => $material->id, 'course_id' => $material->course_id],
            $actionUrl,
            'low'
        );
    }

    public static function notifyAnnouncementPublished($announcement, $userIds)
    {
        $title = $announcement->title;
        $message = substr($announcement->content, 0, 150) . (strlen($announcement->content) > 150 ? '...' : '');
        $actionUrl = "/announcements/{$announcement->id}";
        
        return static::createForMultipleUsers(
            $userIds,
            'announcement',
            $title,
            $message,
            ['announcement_id' => $announcement->id],
            $actionUrl,
            $announcement->priority
        );
    }
}
