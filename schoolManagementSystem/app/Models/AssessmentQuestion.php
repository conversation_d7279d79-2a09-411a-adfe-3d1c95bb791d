<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class AssessmentQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'assessment_id',
        'question',
        'type',
        'options',
        'correct_answers',
        'marks',
        'order',
        'explanation',
        'image_path',
        'metadata'
    ];

    protected $casts = [
        'options' => 'array',
        'correct_answers' => 'array',
        'metadata' => 'array',
    ];

    public function assessment()
    {
        return $this->belongsTo(OnlineAssessment::class, 'assessment_id');
    }

    public function answers()
    {
        return $this->hasMany(AssessmentAnswer::class, 'question_id');
    }

    public function getImageUrlAttribute()
    {
        if (!$this->image_path) {
            return null;
        }
        
        return Storage::url($this->image_path);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function checkAnswer($studentAnswer)
    {
        if (!$this->correct_answers) {
            return null; // Manual grading required
        }

        switch ($this->type) {
            case 'multiple_choice':
                return in_array($studentAnswer, $this->correct_answers);
                
            case 'true_false':
                return $studentAnswer === $this->correct_answers[0];
                
            case 'short_answer':
                // Simple string comparison (case-insensitive)
                $studentAnswer = strtolower(trim($studentAnswer));
                foreach ($this->correct_answers as $correctAnswer) {
                    if (strtolower(trim($correctAnswer)) === $studentAnswer) {
                        return true;
                    }
                }
                return false;
                
            case 'fill_blank':
                // Check if all blanks are filled correctly
                if (!is_array($studentAnswer)) {
                    return false;
                }
                
                foreach ($this->correct_answers as $index => $correctAnswer) {
                    if (!isset($studentAnswer[$index]) || 
                        strtolower(trim($studentAnswer[$index])) !== strtolower(trim($correctAnswer))) {
                        return false;
                    }
                }
                return true;
                
            case 'essay':
                // Essays require manual grading
                return null;
                
            default:
                return null;
        }
    }

    public function getMarksAwarded($studentAnswer)
    {
        $isCorrect = $this->checkAnswer($studentAnswer);
        
        if ($isCorrect === null) {
            // Manual grading required
            return null;
        }
        
        return $isCorrect ? $this->marks : 0;
    }

    public function getFormattedOptionsAttribute()
    {
        if ($this->type !== 'multiple_choice' || !$this->options) {
            return null;
        }
        
        $formatted = [];
        foreach ($this->options as $index => $option) {
            $formatted[] = [
                'id' => chr(65 + $index), // A, B, C, D...
                'text' => $option
            ];
        }
        
        return $formatted;
    }

    public function getDifficultyLevelAttribute()
    {
        // Calculate difficulty based on answer statistics
        $totalAnswers = $this->answers()->count();
        
        if ($totalAnswers === 0) {
            return 'unknown';
        }
        
        $correctAnswers = $this->answers()->where('is_correct', true)->count();
        $correctPercentage = ($correctAnswers / $totalAnswers) * 100;
        
        if ($correctPercentage >= 80) {
            return 'easy';
        } elseif ($correctPercentage >= 60) {
            return 'medium';
        } else {
            return 'hard';
        }
    }

    public function getStatistics()
    {
        $totalAnswers = $this->answers()->count();
        $correctAnswers = $this->answers()->where('is_correct', true)->count();
        
        $stats = [
            'total_answers' => $totalAnswers,
            'correct_answers' => $correctAnswers,
            'incorrect_answers' => $totalAnswers - $correctAnswers,
            'correct_percentage' => $totalAnswers > 0 ? round(($correctAnswers / $totalAnswers) * 100, 2) : 0,
            'difficulty_level' => $this->difficulty_level
        ];
        
        // Add option statistics for multiple choice questions
        if ($this->type === 'multiple_choice' && $this->options) {
            $optionStats = [];
            foreach ($this->options as $index => $option) {
                $count = $this->answers()
                    ->whereJsonContains('answer', $index)
                    ->count();
                
                $optionStats[chr(65 + $index)] = [
                    'text' => $option,
                    'count' => $count,
                    'percentage' => $totalAnswers > 0 ? round(($count / $totalAnswers) * 100, 2) : 0
                ];
            }
            $stats['option_statistics'] = $optionStats;
        }
        
        return $stats;
    }
}
