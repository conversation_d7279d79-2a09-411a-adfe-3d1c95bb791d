<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'code',
        'description',
        'department_id',
        'lecturer_id',
        'credit_value',
        'credit_hours',
        'level_id',
        'semester',
        'is_general'
    ];


    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function lecturer()
    {
        return $this->belongsTo(User::class, 'lecturer_id');
    }

    public function students()
    {
        return $this->belongsToMany(Student::class)
            ->withPivot('academic_year', 'level_id', 'semester')
            ->withTimestamps();
    }

    public function materials()
    {
        return $this->hasMany(CourseMaterial::class);
    }

    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }

    public function visibleMaterials()
    {
        return $this->materials()->visible()->ordered();
    }

    public function publishedAssignments()
    {
        return $this->assignments()->published();
    }

    public function results()
    {
        return $this->morphMany(Result::class, 'resultable');
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function academic_year(){
        return $this->belongsTo(AcademicYear::class);
    }


}
