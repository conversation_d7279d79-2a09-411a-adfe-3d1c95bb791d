<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class School extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'admin_id',
        'school_email',
        'address',
        'director_name',
        'logo',
        'programs',
    ];


    protected $casts = [
        'programs' => 'array',
    ];

    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    public function departments(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Department::class);
    }

    public function getProgramsAttribute()
    {
        return json_decode($this->attributes['programs'], true);
    }

    public function students(): \Illuminate\Database\Eloquent\Relations\HasManyThrough
    {
        return $this->hasManyThrough(Student::class, Department::class);
    }

   // Rename the array accessor to something like this:
   public function getProgramsArrayAttribute()
   {
       return json_decode($this->attributes['programs'], true);
   }

   // Keep the many-to-many relationship with Program model
   public function programRelation(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
{
    return $this->belongsToMany(Program::class, 'program_school');
}

}
