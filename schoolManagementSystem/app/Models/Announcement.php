<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Announcement extends Model
{
    use HasFactory;

    protected $fillable = [
        'created_by',
        'course_id',
        'academic_year_id',
        'level_id',
        'title',
        'content',
        'type',
        'target_audience',
        'priority',
        'is_published',
        'send_notification',
        'send_email',
        'published_at',
        'expires_at',
        'attachments',
        'metadata'
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'send_notification' => 'boolean',
        'send_email' => 'boolean',
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'attachments' => 'array',
        'metadata' => 'array',
    ];

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function getIsExpiredAttribute()
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function getExcerptAttribute($length = 150)
    {
        return substr(strip_tags($this->content), 0, $length) . 
               (strlen(strip_tags($this->content)) > $length ? '...' : '');
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->whereNotNull('published_at');
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expires_at')
                    ->where('expires_at', '<=', now());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByAudience($query, $audience)
    {
        return $query->where('target_audience', $audience);
    }

    public function scopeForCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    public function scopeGeneral($query)
    {
        return $query->whereNull('course_id');
    }

    public function publish()
    {
        $this->update([
            'is_published' => true,
            'published_at' => now()
        ]);

        // Send notifications if enabled
        if ($this->send_notification) {
            $this->sendNotifications();
        }

        return $this;
    }

    public function unpublish()
    {
        $this->update([
            'is_published' => false,
            'published_at' => null
        ]);

        return $this;
    }

    public function sendNotifications()
    {
        $userIds = $this->getTargetUserIds();
        
        if (!empty($userIds)) {
            Notification::notifyAnnouncementPublished($this, $userIds);
        }
    }

    public function getTargetUserIds()
    {
        $query = User::query();

        switch ($this->target_audience) {
            case 'students':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'student');
                });
                break;

            case 'lecturers':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'lecturer');
                });
                break;

            case 'admins':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'admin');
                });
                break;

            case 'course_students':
                if ($this->course_id) {
                    $query->whereHas('student.courses', function ($q) {
                        $q->where('courses.id', $this->course_id);
                    });
                }
                break;

            case 'all':
            default:
                // No additional filtering needed
                break;
        }

        // Filter by academic year and level if specified
        if ($this->academic_year_id || $this->level_id) {
            $query->whereHas('student', function ($q) {
                if ($this->academic_year_id) {
                    $q->where('academic_year_id', $this->academic_year_id);
                }
                if ($this->level_id) {
                    $q->where('current_level_id', $this->level_id);
                }
            });
        }

        return $query->pluck('id')->toArray();
    }

    public function getAttachmentUrls()
    {
        if (!$this->attachments) {
            return [];
        }

        return collect($this->attachments)->map(function ($attachment) {
            return [
                'name' => $attachment['name'] ?? 'Unknown',
                'url' => asset('storage/' . $attachment['path']),
                'size' => $attachment['size'] ?? null,
                'type' => $attachment['type'] ?? null,
            ];
        })->toArray();
    }
}
