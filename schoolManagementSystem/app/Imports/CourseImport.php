<?php

namespace App\Imports;

use App\Models\Course;
use App\Models\Department;
use App\Models\Level;
use App\Models\User;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CourseImport implements ToModel, WithHeadingRow
{

    private $school_id, $department_id, $level_id, $lecturer_id;
    public function __construct($school_id, $department_id)
    {
        $this->school_id = $school_id;
        $this->department_id = $department_id;
    }
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        if(isset($row['level'])){
            $level = Level::where('name', $row['level'])->first();
            $this->level_id = $level->id ?? null;
        }
        if(isset($row['department'])){
            $department = Department::where('code', $row['department'])->first();
            $this->department_id = $department->id ?? null;
        }

        if(isset($row['lecturer'])){
            $lecturer = User::where('email', $row['lecturer'])->first();
            $this->lecturer_id = $lecturer->id ?? null;
        }

       return new Course([
            'name' => $row['name'],
            'code' => $row['code'],
            'semester' => $row['semester'] ?? 1,
            'school_id' => $this->school_id,
            'department_id' => $this->department_id,
            'lecturer_id' => $this->lecturer_id ?? null,
            'level_id' => $this->level_id ?? null,
            'description' => $row['description'] ?? null,
            'credit_value' => $row['credit_value'] ?? null,
            'credit_hours' => $row['credit_hours'] ?? null
        ]);
    }
}
