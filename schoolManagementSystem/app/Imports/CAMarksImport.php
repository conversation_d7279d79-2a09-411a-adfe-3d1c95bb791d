<?php

namespace App\Imports;

use App\Models\ContinuousAssessment;
use App\Models\Result;
use App\Models\Student;
use App\Models\Level;
use App\Models\Course;
use App\Traits\CalculatesGradePoint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CAMarksImport implements ToCollection, WithHeadingRow
{
    use CalculatesGradePoint;
    private $academic_year, $level_name, $semester, $level_id;

    public function __construct($academic_year, $level_name, $semester)
    {
        $this->academic_year = $academic_year;
        $this->level_name = $level_name;
        $this->semester = $semester;
        $this->level_id = $level_name;
    }

    public function collection(Collection $rows)
    {

        $rows->chunk(100)->each(function ($chunk) {
            $students = Student::whereIn('matricule', $chunk->pluck('matricule'))->get()->keyBy('matricule');
            $courses = Course::whereIn('code', $chunk->pluck('course_code'))->get()->keyBy('code');

            DB::transaction(function () use ($chunk, $students, $courses) {
                foreach ($chunk as $row) {
                    $student = $students->get($row['matricule']);

                    if ($student && isset($courses[$row['course_code']])) {
                        $course = $courses[$row['course_code']];

                        $ca = ContinuousAssessment::updateOrCreate([
                            'course_id' => $course->id,
                            'student_id' => $student->id,
                            'academic_year_id' => $this->academic_year,
                        ], [
                            'score' => $row['ca_mark'],
                        ]);


                        $result = Result::firstOrNew([
                            'course_id' => $course->id,
                            'student_id' => $student->id,
                            'academic_year_id' => $this->academic_year,
                            'semester' => $this->semester,
                            'level_id' => $this->level_id,
                        ]);

                        $result->ca_mark = $row['ca_mark'];
                        $result->total_mark = ($result->ca_mark ?? 0) + ($result->exam_mark ?? 0);
                        $grade = $this->calculateGradePoint($result->total_mark);
                        $result->grade = $grade[0];
                        $result->grade_point = $grade[1];

                        $result->weighted_point = $course->credit_value * $result->grade_point;
                        $result->resultable_id = $ca->id;
                        $result->resultable_type = ContinuousAssessment::class;

                        $result->save();
                    }
                }
            });
        });
    }
}
