<?php

namespace App\Imports;

use App\Models\School;
use App\Models\Program;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SchoolsImport implements ToModel, WithHeadingRow
{
    public function model(array $row): \Illuminate\Database\Eloquent\Model|School|null
    {
        $programs = array_filter(array_map('trim', explode(',', $row['programs'])), function ($programName) {
            return Program::where('name', $programName)
                           ->orWhere('abbreviation', $programName)->exists();
        });

        $programIds = Program::whereIn('name', $programs)
            ->orWhereIn('abbreviation', $programs)
            ->pluck('id');

        $school = new School([
            'name' => $row['name'],
            'admin_id' => $row['admin_id'] ?: null,
            'school_email' => $row['school_email'],
            'address' => $row['address'],
            'director_name' => $row['director_name'] ?: null,
            'logo' => $row['logo'] ?: null,
            'programs' => json_encode($programs),
        ]);

        $school->save();
        $school->programRelation()->attach($programIds);

        return $school;
    }
}
