<?php

namespace App\Imports;

use App\Models\Department;
use App\Models\Level;
use App\Models\Program;
use App\Models\Student;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UserImport implements ToModel, WithHeadingRow
{
    protected $role;

    public function __construct(string $role)
    {
        $this->role = $role;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        // Check if required fields are present
        if (empty($row['email']) || empty($row['first_name']) || empty($row['last_name'])) {
            Log::warning('Row skipped due to missing essential fields', ['row' => $row]);
            return null;
        }

        // Check if user already exists to avoid duplication
        $user = User::firstOrNew(['email' => $row['email']], [
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
            'gender' => $row['gender'],
            'phone_number' => $row['phone_number'],
            'password' => Hash::make($row['password']),
            'dob' => $row['dob'] ?? null,
            'profile_image' => $row['profile_image'] ?? null,
        ]);

        // Save only if it's a new user
        if (!$user->exists) {
            $user->save();
        }

        // Assign role to user if not already assigned
        if (!$user->hasRole($this->role)) {
            $user->assignRole($this->role);
        }

        // Create student record if role is 'student' and department/program exist
        if ($this->role == 'student' && isset($row['department_code'], $row['program_code'])) {
            $department = Department::where('code', $row['department_code'])->first();
            $program = Program::where('name', $row['program_code'])
                        ->orWhere('abbreviation', $row['program_code'])
                        ->first();
            $level = Level::where('name', $row['level'])
                        ->orWhere('year', $row['level'])
                        ->first();

            if (!$level) {
                Log::warning('Level not found for student record, setting level to 200', ['level' => $row['level'], 'row' => $row]);
            }

            $level_id = 1;



            if ($department) {
                Student::updateOrCreate(
                    ['user_id' => $user->id],
                    [
                        'matricule' => $row['matricule'] ?? null,
                        'place_of_birth' => $row['place_of_birth'] ?? null,
                        'region_of_origin' => $row['region_of_origin'] ?? null,
                        'marital_status' => $row['marital_status'] ?? null,
                        'program_id' => $program->id ?? null,
                        'date_of_admission' => $row['date_of_admission'] ?? null,
                        'department_id' => $department->id,
                        'nationality' => $row['nationality'] ?? null,
                        'level_id' => $level->id ?? $level_id,
                        'current_level_id' => $level->id ?? $level_id
                    ]
                );
            } else {
                Log::warning('Department not found for student record', ['department_code' => $row['department_code'], 'row' => $row]);
            }
        }

        return $user;
    }
}
