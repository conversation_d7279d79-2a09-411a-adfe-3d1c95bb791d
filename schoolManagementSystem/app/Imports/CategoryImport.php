<?php

namespace App\Imports;

use App\Models\IncomeCategory;
use App\Models\ExpenseCategory;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CategoryImport implements ToCollection, WithHeadingRow
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row)
        {
            $categoryType = strtolower($row['category']);

            if ($categoryType === 'income') {
                IncomeCategory::create([
                    'name' => $row['name'],
                    'description' => $row['description'],
                ]);
            } elseif ($categoryType === 'expense') {
                ExpenseCategory::create([
                    'name' => $row['name'],
                    'description' => $row['description'],
                ]);
            }
        }
    }
}
