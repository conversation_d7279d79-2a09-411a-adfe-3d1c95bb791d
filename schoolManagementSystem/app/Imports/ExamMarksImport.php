<?php

namespace App\Imports;

use App\Models\Course;
use App\Models\Exam;
use App\Models\Result;
use App\Models\Student;
use App\Traits\CalculatesGradePoint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ExamMarksImport implements ToCollection, WithHeadingRow
{
    use CalculatesGradePoint;

    private $academic_year, $level_name, $semester, $level_id;

    public function __construct($academic_year, $level_name, $semester)
    {
        $this->academic_year = $academic_year;
        $this->level_name = $level_name;
        $this->semester = $semester;
        $this->level_id = $level_name; // Assuming level_name is the ID or use a different mapping if necessary
    }

    public function collection(Collection $rows)
    {
        // Process the rows in chunks to manage memory and performance
        $rows->chunk(100)->each(function ($chunk) {
            // Fetch all students and courses related to the current chunk
            $students = Student::whereIn('matricule', $chunk->pluck('matricule'))->get()->keyBy('matricule') ;
          $courses = Course::whereIn('code', $chunk->pluck('course_code'))->get()->keyBy('code');

            DB::transaction(function () use ($chunk, $students, $courses) {
                foreach ($chunk as $row) {
                    $student = $students->get($row['matricule']);

                    if ($student && isset($courses[$row['course_code']])) {
                        $course = $courses[$row['course_code']];

                        // Update or create an Exam record
                        $exam = Exam::updateOrCreate([
                            'course_id' => $course->id,
                            'student_id' => $student->id,
                            'academic_year_id' => $this->academic_year,
                        ], [
                            'score' => $row['exam_mark'],
                        ]);

                        // Use firstOrNew to get an existing Result or instantiate a new one
                        $result = Result::firstOrNew([
                            'course_id' => $course->id,
                            'student_id' => $student->id,
                            'academic_year_id' => $this->academic_year,
                            'semester' => $this->semester,
                            'level_id' => $this->level_id,
                        ]);

                        // Update exam mark and calculate total mark
                        $result->exam_mark = $row['exam_mark'];
                        $result->total_mark = ($result->ca_mark ?? 0) + ($result->exam_mark ?? 0);

                        // Set resultable relationship for Exam
                        $result->resultable_id = $exam->id;
                        $result->resultable_type = Exam::class;

                        // Calculate grade and grade point
                        $grade = $this->calculateGradePoint($result->total_mark);
                        $result->grade = $grade[0];
                        $result->grade_point = $grade[1];

                        // Calculate weighted point
                        $result->weighted_point = $course->credit_value * $result->grade_point;

                        // Save the updated or newly created Result record
                        $result->save();
                    }
                }
            });
        });
    }
}
