<?php

namespace App\Imports;

use App\Models\Program;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\Importable;

class ProgramsImport implements ToModel, WithHeadingRow
{
    use Importable;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): ?Program
    {
        return new Program([
            'name'          => $row['name'] ?? null,
            'description'   => $row['description'] ?? null,
            'abbreviation'  => $row['abbreviation'] ?? null,
            'cost'          => $row['cost'] ?? null,
            'duration'      => $row['duration'] ?? null,
            'degree'        => $row['degree'] ?? null,
        ]);
    }
}
