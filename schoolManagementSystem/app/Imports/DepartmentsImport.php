<?php

namespace App\Imports;

use App\Models\Department;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class DepartmentsImport implements ToModel, WithHeadingRow
{
    protected $school_id;

    public function __construct($school_id)
    {
        $this->school_id = $school_id;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        Log::info('Processing row: ', $row);

        // Check if the department with the same code already exists
        $existingDepartment = Department::where('code', $row['code'])
                                         ->where('school_id', $this->school_id)
                                         ->first();

        if ($existingDepartment) {
            Log::info('Skipping row. Department with code ' . $row['code'] . ' already exists.');
            return null; // Skip this row if the department exists
        }

        // If the department does not exist, create a new one
        return new Department([
            'name' => $row['name'],
            'school_id' => $this->school_id,
            'code' => $row['code'],
            'description' => $row['description'],
            'admin_id' => $row['admin_id'] ?? null
        ]);
    }
}
