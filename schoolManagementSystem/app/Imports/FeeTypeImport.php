<?php

namespace App\Imports;

use App\Models\FeeType;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class FeeTypeImport implements ToModel, WithHeadingRow
{
    public function model(array $row): FeeType
    {
        return new FeeType([
            'name' => $row['name'],
            'amount' => $row['amount'],
            'installments' => $row['installments'],
        ]);
    }
}
