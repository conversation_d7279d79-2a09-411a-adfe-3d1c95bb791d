<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class StudentsExport implements WithMultipleSheets
{
    protected $groupedStudents;

    public function __construct($groupedStudents)
    {
        $this->groupedStudents = $groupedStudents;
    }

    public function sheets(): array
    {
        $sheets = [];

        foreach ($this->groupedStudents as $levelName => $students) {
            $sheets[] = new StudentLevelSheet($levelName, $students);
        }

        return $sheets;
    }
}

class StudentLevelSheet implements FromArray, WithHeadings, WithTitle
{
    protected $levelName;
    protected $students;

    public function __construct($levelName, $students)
    {
        $this->levelName = $levelName;
        $this->students = $students;
    }

    public function array(): array
    {
        return $this->students->map(function($student) {
            return [
                $student->user->first_name . ' ' . $student->user->last_name,
                $student->matricule,
                $student->user->phone_number,
                $student->program->name ?? '',
                $student->department->name ?? ''
            ];
        })->toArray();
    }

    public function headings(): array
    {
        return [
            'Student Name',
            'Matricule',
            'Phone Number',
            'Program',
            'Department'
        ];
    }

    public function title(): string
    {
        return $this->levelName;
    }
}
