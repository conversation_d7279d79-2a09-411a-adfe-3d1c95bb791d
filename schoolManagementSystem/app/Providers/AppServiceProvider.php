<?php

namespace App\Providers;

use App\Repositories\AcademicYearRepositoryInterfaceImpl;
use App\Repositories\AccountRepositoryInterfaceImpl;
use App\Repositories\CategoryCrudRepository;
use App\Repositories\ExpenseRepository;
use App\Repositories\FeeRepositoryInterfaceImpl;
use App\Repositories\FeeTypeRepositoryInterfaceImpl;
use App\Repositories\IncomeRepository;
use App\Repositories\Interfaces\AcademicYearRepositoryInterface;
use App\Repositories\Interfaces\AccountRepositoryInterface;
use App\Repositories\Interfaces\CategoryCrudInterface;
use App\Repositories\Interfaces\ExpenseInterface;
use App\Repositories\Interfaces\FeeRepositoryInterface;
use App\Repositories\Interfaces\FeeTypeRepositoryInterface;
use App\Repositories\Interfaces\IncomeInterface;
use App\Repositories\Interfaces\ProgramRepositoryInterface;
use App\Repositories\Interfaces\SchoolRepositoryInterface;
use App\Repositories\Interfaces\StatisticsInterface;
use App\Repositories\Interfaces\SystemSettingRepositoryInterface;
use App\Repositories\Interfaces\TransactionInterface;
use App\Repositories\ProgramRepositoryInterfaceImpl;
use App\Repositories\SchoolRepositoryInterfaceImpl;
use App\Repositories\StatisticsInterfaceImpl;
use App\Repositories\SystemSettingRepositoryInterfaceImpl;
use App\Repositories\TransactionInterfaceImpl;
use Illuminate\Support\ServiceProvider;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(ProgramRepositoryInterface::class, ProgramRepositoryInterfaceImpl::class,);
        $this->app->bind(FeeTypeRepositoryInterface::class, FeeTypeRepositoryInterfaceImpl::class);
        $this->app->bind(AcademicYearRepositoryInterface::class, AcademicYearRepositoryInterfaceImpl::class);
        $this->app->bind(AccountRepositoryInterface::class, AccountRepositoryInterfaceImpl::class);
        $this->app->bind(SchoolRepositoryInterface::class, SchoolRepositoryInterfaceImpl::class);
        $this->app->bind(FeeRepositoryInterface::class, FeeRepositoryInterfaceImpl::class);
        $this->app->bind(TransactionInterface::class, TransactionInterfaceImpl::class);
        $this->app->bind(IncomeInterface::class, IncomeRepository::class);
        $this->app->bind(ExpenseInterface::class, ExpenseRepository::class);
        $this->app->bind(CategoryCrudInterface::class, concrete: CategoryCrudRepository::class);
        $this->app->bind(StatisticsInterface::class, concrete: StatisticsInterfaceImpl::class);
        $this->app->bind(SystemSettingRepositoryInterface::class, concrete: SystemSettingRepositoryInterfaceImpl::class);
    }


    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        Ratelimiter::for('custom-api', function (Request $request) {
            return Limit::perMinute(2)->by($request->user()?->id ?: $request->ip());
        });
    }
}
