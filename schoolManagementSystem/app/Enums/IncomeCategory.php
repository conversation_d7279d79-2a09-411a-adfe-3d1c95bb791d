<?php

namespace App\Enums;
use <PERSON><PERSON>\Enum\Enum;


class  IncomeCategory extends Enum
{

    const TUITION = 1;

    const SHARES = 2;

    const DONATION = 3;

    const BUSINESS = 4;

    const GRANTS = 5;

    const OTHERS = 6;


    public static function values(): array
    {
        return [
            self::TUITION,
            self::SHARES,
            self::DONATION,
            self::GRANTS,
            self::BUSINESS,
            self::OTHERS,
        ];
    }

    public static function labels(): array
    {
        return [
            self::TUITION => 'Tuition',
            self::SHARES => 'Shares',
            self::DONATION => 'Donation',
            self::BUSINESS => 'Business',
            self::GRANTS => 'Grants',
            self::OTHERS => 'Others',
        ];

    }

    public static function getLabel($value)
    {
        return self::labels()[$value];
    }
}
