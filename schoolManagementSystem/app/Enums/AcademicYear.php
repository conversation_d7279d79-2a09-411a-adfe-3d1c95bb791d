<?php
namespace App\Enums;

use \Spatie\Enum\Enum;

/**
 * @method static self YEAR_2019_2020()
 * @method static self YEAR_2020_2021()
 * @method static self YEAR_2021_2022()
 * @method static self YEAR_2022_2023()
 * @method static self YEAR_2023_2024()
 * @method static self YEAR_2024_2025()
 * @method static self YEAR_2025_2026()
 * @method static self YEAR_2026_2027()
 * @method static self YEAR_2027_2028()
 * @method static self YEAR_2028_2029()
 * @method static self YEAR_2029_2030()
 */
class AcademicYear extends Enum
{
    protected static function values(): array
    {
        return [
            'YEAR_2019_2020' => '2019/2020',
            'YEAR_2020_2021' => '2020/2021',
            'YEAR_2021_2022' => '2021/2022',
            'YEAR_2022_2023' => '2022/2023',
            'YEAR_2023_2024' => '2023/2024',
            'YEAR_2024_2025' => '2024/2025',
            'YEAR_2025_2026' => '2025/2026',
            'YEAR_2026_2027' => '2026/2027',
            'YEAR_2027_2028' => '2027/2028',
            'YEAR_2028_2029' => '2028/2029',
            'YEAR_2029_2030' => '2029/2030',
        ];
    }
}
