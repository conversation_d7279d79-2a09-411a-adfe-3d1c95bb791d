<?php

namespace App\Enums;

enum EventType
{
    const CA = 'CA'; // Continuous Assessment
    const EO = 'EO'; // End of Lectures
    const SE = 'SE'; // Semester Examination
    const EX = 'EX'; // Extra-curricular Event
    const OT = 'OT'; // Other
    const PR = 'PR'; // Project/Presentation
    const OR = 'OR'; // Orientation
    const RE = 'RE'; // Resumption
    const GR = 'GR'; // Graduation
    const HO = 'HO'; // Holiday
    const ME = 'ME'; // Meeting

    public static function values(): array
    {
        return [
            self::CA,
            self::EO,
            self::SE,
            self::EX,
            self::OT,
            self::PR,
            self::OR,
            self::RE,
            self::GR,
        ];
    }
}