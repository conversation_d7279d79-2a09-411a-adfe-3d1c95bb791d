<?php

namespace App\Enums;
use <PERSON><PERSON>\Enum\Enum;


class  ExpenseCategory extends Enum
{

    const PAYROLL = 1;

    const FUNITURE = 2;

    const CLASSROOM_SUPPLIES = 3;

    const DOCUMENTATION = 4;

    const FIELD_TRIPS = 5;

    const MEETINGS = 6;

    const UTILITY_BILLS = 7;

    const SEMINARS = 8;

    const OTHERS = 9;


    public static function values(): array
    {
        return [
            self::PAYROLL,
            self::FUNITURE,
            self::FIELD_TRIPS,
            self::CLASSROOM_SUPPLIES,
            self::DOCUMENTATION,
            self::MEETINGS,
            self::UTILITY_BILLS,
            self::SEMINARS,
            self::OTHERS,
        ];
    }

    public static function labels(): array
    {
        return [
            self::PAYROLL => 'Payroll',
            self::FUNITURE => 'Furniture',
            self::FIELD_TRIPS => 'Field Trips',
            self::CLASSROOM_SUPPLIES => 'Classroom Supplies', // stationaries
            self::DOCUMENTATION => 'Documentation', // printing
            self::MEETINGS => "Meetings",
            self::UTILITY_BILLS => "Utility Bills",
            self::SEMINARS => "Seminars",
            self::OTHERS => "Others"

            // marketing or promotions
            // intertainment
            // transportation

        ];

    }

    public static function getLabel($value)
    {
        return self::labels()[$value];
    }
}
