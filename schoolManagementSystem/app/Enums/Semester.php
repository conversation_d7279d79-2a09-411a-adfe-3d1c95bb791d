<?php

namespace App\Enums;
use <PERSON><PERSON>\Enum\Enum;

class <PERSON><PERSON><PERSON> extends Enum
{
    const FIRST = 1;
    const SECOND = 2;
    const RESIT = 3;

    public static function labels()
    {
        return [
            self::FIRST => 'First',
            self::SECOND => 'Second',
            self::RESIT => 'Resit',
        ];
    }

    public static function values()
    {
        return [
            self::FIRST,
            self::SECOND,
            self::RESIT,
        ];
    }

    public static function getLabel($value)
    {
        return self::labels()[$value];
    }


}
