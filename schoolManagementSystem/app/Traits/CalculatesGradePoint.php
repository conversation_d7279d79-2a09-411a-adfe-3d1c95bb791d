<?php

namespace App\Traits;

trait CalculatesGradePoint
{
    public function calculateGradePoint($total_mark): array
    {
        if ($total_mark >= 80) {
            return [
                'A',
                4
            ];
        } elseif ($total_mark >= 70) {
            return [
                'B+',
                3.5
            ];
        } elseif ($total_mark >= 60) {
            return [
                'B',
                3
            ];
        } elseif ($total_mark >= 55) {
            return [
                'C+',
                2.5
            ];
        } elseif ($total_mark >= 50) {
            return [
                'C',
                2
            ];
        } elseif ($total_mark >= 45) {
            return [
                'D+',
                1.5
            ];
        } elseif ($total_mark >= 40) {
            return [
                'D',
                1
            ];
        } else {
            return [
                'F',
                0
            ];
        }
    }
}
