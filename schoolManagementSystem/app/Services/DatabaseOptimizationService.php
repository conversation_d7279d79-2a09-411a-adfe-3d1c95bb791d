<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseOptimizationService
{
    /**
     * Add missing indexes for better performance
     */
    public function addPerformanceIndexes()
    {
        $indexes = [
            // Users table
            'users' => [
                ['email'],
                ['created_at'],
                ['updated_at'],
            ],
            
            // Students table
            'students' => [
                ['user_id'],
                ['department_id'],
                ['level_id'],
                ['current_level_id'],
                ['academic_year_id'],
                ['matricule'],
                ['date_of_admission'],
            ],
            
            // Courses table
            'courses' => [
                ['department_id'],
                ['level_id'],
                ['code'],
                ['credits'],
                ['is_active'],
            ],
            
            // Course registrations
            'course_student' => [
                ['course_id', 'student_id'],
                ['academic_year'],
                ['semester'],
                ['level_id'],
            ],
            
            // Assignments table
            'assignments' => [
                ['course_id', 'is_published'],
                ['due_date', 'is_published'],
                ['academic_year_id', 'level_id'],
                ['created_by'],
                ['start_date'],
                ['type'],
            ],
            
            // Assignment submissions
            'assignment_submissions' => [
                ['assignment_id', 'status'],
                ['student_id', 'submitted_at'],
                ['graded_by'],
                ['status'],
            ],
            
            // Course materials
            'course_materials' => [
                ['course_id', 'is_visible'],
                ['type', 'is_visible'],
                ['uploaded_by'],
                ['sort_order'],
            ],
            
            // Online assessments
            'online_assessments' => [
                ['course_id', 'type', 'is_published'],
                ['start_time', 'end_time'],
                ['academic_year_id', 'level_id'],
                ['created_by'],
                ['type'],
            ],
            
            // Assessment attempts
            'assessment_attempts' => [
                ['assessment_id', 'status'],
                ['student_id', 'status'],
                ['started_at'],
                ['submitted_at'],
            ],
            
            // Assessment questions
            'assessment_questions' => [
                ['assessment_id', 'order'],
                ['type', 'marks'],
            ],
            
            // Assessment answers
            'assessment_answers' => [
                ['question_id', 'is_correct'],
                ['attempt_id'],
            ],
            
            // Notifications
            'notifications' => [
                ['user_id', 'is_read'],
                ['type', 'created_at'],
                ['priority', 'is_read'],
                ['created_at'],
            ],
            
            // Announcements
            'announcements' => [
                ['is_published', 'published_at'],
                ['target_audience', 'type'],
                ['course_id', 'is_published'],
                ['created_by'],
                ['expires_at'],
            ],
        ];
        
        foreach ($indexes as $table => $tableIndexes) {
            if (Schema::hasTable($table)) {
                foreach ($tableIndexes as $columns) {
                    $indexName = $table . '_' . implode('_', $columns) . '_index';
                    
                    if (!$this->indexExists($table, $indexName)) {
                        try {
                            Schema::table($table, function ($tableSchema) use ($columns, $indexName) {
                                $tableSchema->index($columns, $indexName);
                            });
                        } catch (\Exception $e) {
                            // Index might already exist or column doesn't exist
                            continue;
                        }
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * Check if index exists
     */
    protected function indexExists($table, $indexName)
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        
        foreach ($indexes as $index) {
            if ($index->Key_name === $indexName) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Optimize database tables
     */
    public function optimizeTables()
    {
        $tables = DB::select('SHOW TABLES');
        $databaseName = config('database.connections.mysql.database');
        
        foreach ($tables as $table) {
            $tableName = $table->{"Tables_in_{$databaseName}"};
            DB::statement("OPTIMIZE TABLE {$tableName}");
        }
        
        return true;
    }
    
    /**
     * Analyze table statistics
     */
    public function analyzeTableStatistics()
    {
        $tables = [
            'users', 'students', 'courses', 'course_student', 'assignments',
            'assignment_submissions', 'course_materials', 'online_assessments',
            'assessment_attempts', 'notifications', 'announcements'
        ];
        
        $statistics = [];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                $size = $this->getTableSize($table);
                
                $statistics[$table] = [
                    'row_count' => $count,
                    'size_mb' => $size,
                    'avg_row_size' => $count > 0 ? round($size * 1024 * 1024 / $count, 2) : 0,
                ];
            }
        }
        
        return $statistics;
    }
    
    /**
     * Get table size in MB
     */
    protected function getTableSize($table)
    {
        $result = DB::select("
            SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = ? AND table_name = ?
        ", [config('database.connections.mysql.database'), $table]);
        
        return $result[0]->size_mb ?? 0;
    }
    
    /**
     * Clean up old data
     */
    public function cleanupOldData()
    {
        $cleanupActions = [];
        
        // Clean up old notifications (older than 3 months)
        $deletedNotifications = DB::table('notifications')
            ->where('created_at', '<', now()->subMonths(3))
            ->where('is_read', true)
            ->delete();
        
        $cleanupActions['deleted_notifications'] = $deletedNotifications;
        
        // Clean up expired announcements
        $deletedAnnouncements = DB::table('announcements')
            ->whereNotNull('expires_at')
            ->where('expires_at', '<', now()->subMonth())
            ->delete();
        
        $cleanupActions['deleted_announcements'] = $deletedAnnouncements;
        
        // Clean up old assessment attempts (keep only latest 5 per student per assessment)
        $this->cleanupOldAssessmentAttempts();
        $cleanupActions['cleaned_assessment_attempts'] = true;
        
        // Clean up old logs (if using database logging)
        if (Schema::hasTable('logs')) {
            $deletedLogs = DB::table('logs')
                ->where('created_at', '<', now()->subMonth())
                ->delete();
            
            $cleanupActions['deleted_logs'] = $deletedLogs;
        }
        
        return $cleanupActions;
    }
    
    /**
     * Clean up old assessment attempts
     */
    protected function cleanupOldAssessmentAttempts()
    {
        // Keep only the latest 5 attempts per student per assessment
        $subquery = DB::table('assessment_attempts as a1')
            ->select('a1.id')
            ->whereColumn('a1.assessment_id', 'assessment_attempts.assessment_id')
            ->whereColumn('a1.student_id', 'assessment_attempts.student_id')
            ->orderBy('a1.attempt_number', 'desc')
            ->limit(5);
        
        DB::table('assessment_attempts')
            ->whereNotIn('id', $subquery)
            ->delete();
    }
    
    /**
     * Get slow query log
     */
    public function getSlowQueries()
    {
        try {
            // Enable slow query log temporarily
            DB::statement("SET GLOBAL slow_query_log = 'ON'");
            DB::statement("SET GLOBAL long_query_time = 1"); // Log queries taking more than 1 second
            
            // This would require reading from the slow query log file
            // For now, return a placeholder
            return [
                'message' => 'Slow query logging enabled. Check MySQL slow query log file for details.',
                'log_file' => '/var/log/mysql/mysql-slow.log'
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Could not enable slow query logging: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get database performance metrics
     */
    public function getPerformanceMetrics()
    {
        $metrics = [];
        
        try {
            // Get connection count
            $connections = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            $metrics['active_connections'] = $connections[0]->Value ?? 0;
            
            // Get query cache hit rate
            $cacheHits = DB::select("SHOW STATUS LIKE 'Qcache_hits'");
            $cacheInserts = DB::select("SHOW STATUS LIKE 'Qcache_inserts'");
            
            $hits = $cacheHits[0]->Value ?? 0;
            $inserts = $cacheInserts[0]->Value ?? 0;
            $total = $hits + $inserts;
            
            $metrics['query_cache_hit_rate'] = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
            
            // Get buffer pool usage
            $bufferPool = DB::select("SHOW STATUS LIKE 'Innodb_buffer_pool_pages_%'");
            $metrics['buffer_pool'] = [];
            
            foreach ($bufferPool as $stat) {
                $key = str_replace('Innodb_buffer_pool_pages_', '', $stat->Variable_name);
                $metrics['buffer_pool'][$key] = $stat->Value;
            }
            
        } catch (\Exception $e) {
            $metrics['error'] = 'Could not retrieve performance metrics: ' . $e->getMessage();
        }
        
        return $metrics;
    }
}
