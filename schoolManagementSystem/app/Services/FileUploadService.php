<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    protected $allowedMimeTypes = [
        'documents' => [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
        ],
        'images' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
        ],
        'videos' => [
            'video/mp4',
            'video/avi',
            'video/quicktime',
            'video/x-msvideo',
        ],
        'audio' => [
            'audio/mpeg',
            'audio/wav',
            'audio/ogg',
            'audio/mp4',
        ],
    ];

    protected $maxFileSizes = [
        'documents' => 50 * 1024 * 1024, // 50MB
        'images' => 10 * 1024 * 1024,    // 10MB
        'videos' => 500 * 1024 * 1024,   // 500MB
        'audio' => 100 * 1024 * 1024,    // 100MB
    ];

    public function uploadFile(UploadedFile $file, string $directory, array $options = [])
    {
        $this->validateFile($file, $options);

        $filename = $this->generateFilename($file, $options);
        $path = $this->getStoragePath($directory, $filename);

        // Handle image optimization
        if ($this->isImage($file) && ($options['optimize_images'] ?? true)) {
            $this->uploadOptimizedImage($file, $path, $options);
        } else {
            $file->storeAs($directory, $filename, $options['disk'] ?? 'public');
        }

        return [
            'path' => $path,
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientOriginalExtension(),
        ];
    }

    public function uploadMultipleFiles(array $files, string $directory, array $options = [])
    {
        $uploadedFiles = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $uploadedFiles[] = $this->uploadFile($file, $directory, $options);
            }
        }

        return $uploadedFiles;
    }

    public function deleteFile(string $path, string $disk = 'public')
    {
        return Storage::disk($disk)->delete($path);
    }

    public function deleteMultipleFiles(array $paths, string $disk = 'public')
    {
        return Storage::disk($disk)->delete($paths);
    }

    public function getFileUrl(string $path, string $disk = 'public')
    {
        if ($disk === 'public') {
            return Storage::url($path);
        }

        return Storage::disk($disk)->url($path);
    }

    public function fileExists(string $path, string $disk = 'public')
    {
        return Storage::disk($disk)->exists($path);
    }

    public function getFileSize(string $path, string $disk = 'public')
    {
        return Storage::disk($disk)->size($path);
    }

    public function downloadFile(string $path, string $name = null, string $disk = 'public')
    {
        return Storage::disk($disk)->download($path, $name);
    }

    protected function validateFile(UploadedFile $file, array $options = [])
    {
        $fileType = $this->getFileType($file);
        $maxSize = $options['max_size'] ?? $this->maxFileSizes[$fileType] ?? (50 * 1024 * 1024);

        // Check file size
        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException("File size exceeds maximum allowed size of " . $this->formatBytes($maxSize));
        }

        // Check mime type
        if (isset($options['allowed_types'])) {
            if (!in_array($file->getMimeType(), $options['allowed_types'])) {
                throw new \InvalidArgumentException("File type not allowed");
            }
        } else {
            $allowedTypes = $this->allowedMimeTypes[$fileType] ?? [];
            if (!in_array($file->getMimeType(), $allowedTypes)) {
                throw new \InvalidArgumentException("File type not allowed");
            }
        }

        // Check for malicious files
        $this->scanForMaliciousContent($file);
    }

    protected function generateFilename(UploadedFile $file, array $options = [])
    {
        $prefix = $options['prefix'] ?? '';
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        $extension = $file->getClientOriginalExtension();

        if (isset($options['preserve_name']) && $options['preserve_name']) {
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $safeName = Str::slug($originalName);
            return $prefix . $safeName . '_' . $timestamp . '_' . $random . '.' . $extension;
        }

        return $prefix . $timestamp . '_' . $random . '.' . $extension;
    }

    protected function getStoragePath(string $directory, string $filename)
    {
        return $directory . '/' . $filename;
    }

    protected function getFileType(UploadedFile $file)
    {
        $mimeType = $file->getMimeType();

        foreach ($this->allowedMimeTypes as $type => $mimeTypes) {
            if (in_array($mimeType, $mimeTypes)) {
                return $type;
            }
        }

        return 'documents'; // Default fallback
    }

    protected function isImage(UploadedFile $file)
    {
        return in_array($file->getMimeType(), $this->allowedMimeTypes['images']);
    }

    protected function uploadOptimizedImage(UploadedFile $file, string $path, array $options = [])
    {
        $quality = $options['image_quality'] ?? 85;
        $maxWidth = $options['max_width'] ?? 1920;
        $maxHeight = $options['max_height'] ?? 1080;

        $image = Image::make($file);

        // Resize if necessary
        if ($image->width() > $maxWidth || $image->height() > $maxHeight) {
            $image->resize($maxWidth, $maxHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        // Save optimized image
        $disk = $options['disk'] ?? 'public';
        $fullPath = Storage::disk($disk)->path($path);
        
        // Ensure directory exists
        $directory = dirname($fullPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        $image->save($fullPath, $quality);
    }

    protected function scanForMaliciousContent(UploadedFile $file)
    {
        // Basic security checks
        $content = file_get_contents($file->getPathname());
        
        // Check for common malicious patterns
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new \InvalidArgumentException("File contains potentially malicious content");
            }
        }
    }

    protected function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    public function createThumbnail(string $imagePath, int $width = 300, int $height = 300, string $disk = 'public')
    {
        if (!$this->fileExists($imagePath, $disk)) {
            throw new \InvalidArgumentException("Image file not found");
        }

        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        $fullPath = Storage::disk($disk)->path($imagePath);
        $thumbnailFullPath = Storage::disk($disk)->path($thumbnailPath);

        // Ensure thumbnail directory exists
        $thumbnailDir = dirname($thumbnailFullPath);
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }

        $image = Image::make($fullPath);
        $image->fit($width, $height);
        $image->save($thumbnailFullPath, 85);

        return $thumbnailPath;
    }

    public function getFileInfo(string $path, string $disk = 'public')
    {
        if (!$this->fileExists($path, $disk)) {
            return null;
        }

        $storage = Storage::disk($disk);
        
        return [
            'path' => $path,
            'size' => $storage->size($path),
            'last_modified' => $storage->lastModified($path),
            'url' => $this->getFileUrl($path, $disk),
            'mime_type' => $storage->mimeType($path),
        ];
    }
}
