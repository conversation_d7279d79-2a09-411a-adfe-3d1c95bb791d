<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class SecurityAuditService
{
    /**
     * Perform comprehensive security audit
     */
    public function performSecurityAudit()
    {
        return [
            'password_security' => $this->auditPasswordSecurity(),
            'user_accounts' => $this->auditUserAccounts(),
            'file_permissions' => $this->auditFilePermissions(),
            'database_security' => $this->auditDatabaseSecurity(),
            'session_security' => $this->auditSessionSecurity(),
            'api_security' => $this->auditApiSecurity(),
            'recommendations' => $this->getSecurityRecommendations(),
        ];
    }
    
    /**
     * Audit password security
     */
    protected function auditPasswordSecurity()
    {
        $weakPasswords = [];
        $commonPasswords = [
            'password', '123456', '*********', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        $users = User::all();
        $stats = [
            'total_users' => $users->count(),
            'weak_passwords' => 0,
            'never_changed' => 0,
            'old_passwords' => 0,
        ];
        
        foreach ($users as $user) {
            // Check for common weak passwords
            foreach ($commonPasswords as $commonPassword) {
                if (Hash::check($commonPassword, $user->password)) {
                    $weakPasswords[] = [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'weakness' => 'Common password'
                    ];
                    $stats['weak_passwords']++;
                    break;
                }
            }
            
            // Check if password was never changed (same as created date)
            if ($user->password_changed_at === null || 
                $user->password_changed_at->eq($user->created_at)) {
                $stats['never_changed']++;
            }
            
            // Check for old passwords (older than 90 days)
            if ($user->password_changed_at && 
                $user->password_changed_at->lt(now()->subDays(90))) {
                $stats['old_passwords']++;
            }
        }
        
        return [
            'statistics' => $stats,
            'weak_passwords' => $weakPasswords,
            'recommendations' => [
                'Enforce strong password policy',
                'Require password changes every 90 days',
                'Implement password history to prevent reuse',
                'Add two-factor authentication'
            ]
        ];
    }
    
    /**
     * Audit user accounts
     */
    protected function auditUserAccounts()
    {
        $suspiciousAccounts = [];
        $stats = [
            'total_users' => User::count(),
            'inactive_users' => 0,
            'admin_users' => 0,
            'unverified_users' => 0,
        ];
        
        // Check for inactive users (no login in 6 months)
        $inactiveUsers = User::where('last_login_at', '<', now()->subMonths(6))
            ->orWhereNull('last_login_at')
            ->get();
        
        $stats['inactive_users'] = $inactiveUsers->count();
        
        // Check admin users
        $adminUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();
        
        $stats['admin_users'] = $adminUsers->count();
        
        // Check unverified users
        $unverifiedUsers = User::whereNull('email_verified_at')->get();
        $stats['unverified_users'] = $unverifiedUsers->count();
        
        // Flag suspicious accounts
        foreach ($adminUsers as $admin) {
            if ($admin->created_at->gt(now()->subDays(30))) {
                $suspiciousAccounts[] = [
                    'user_id' => $admin->id,
                    'email' => $admin->email,
                    'issue' => 'Recently created admin account',
                    'created_at' => $admin->created_at
                ];
            }
        }
        
        return [
            'statistics' => $stats,
            'suspicious_accounts' => $suspiciousAccounts,
            'inactive_users' => $inactiveUsers->take(10)->pluck('email'),
            'recommendations' => [
                'Disable inactive user accounts',
                'Review admin account creation',
                'Implement account lockout policies',
                'Regular access reviews'
            ]
        ];
    }
    
    /**
     * Audit file permissions
     */
    protected function auditFilePermissions()
    {
        $issues = [];
        $recommendations = [];
        
        // Check storage directory permissions
        $storagePath = storage_path();
        if (is_writable($storagePath)) {
            $permissions = substr(sprintf('%o', fileperms($storagePath)), -4);
            if ($permissions !== '0755') {
                $issues[] = "Storage directory has permissions {$permissions}, should be 0755";
            }
        }
        
        // Check public uploads
        $publicPath = public_path('storage');
        if (file_exists($publicPath)) {
            $files = Storage::disk('public')->allFiles();
            $publicFiles = array_filter($files, function ($file) {
                return !str_starts_with($file, 'private/');
            });
            
            if (count($publicFiles) > 100) {
                $issues[] = "Large number of public files (" . count($publicFiles) . ") - consider moving sensitive files to private storage";
            }
        }
        
        return [
            'issues' => $issues,
            'recommendations' => [
                'Use private storage for sensitive files',
                'Implement file access controls',
                'Regular file permission audits',
                'Scan uploaded files for malware'
            ]
        ];
    }
    
    /**
     * Audit database security
     */
    protected function auditDatabaseSecurity()
    {
        $issues = [];
        $recommendations = [];
        
        try {
            // Check for default database credentials
            $dbConfig = config('database.connections.mysql');
            if ($dbConfig['username'] === 'root' && $dbConfig['password'] === '') {
                $issues[] = 'Using default database credentials (root with no password)';
            }
            
            // Check for SQL injection vulnerabilities (basic check)
            $vulnerableQueries = $this->findPotentialSqlInjections();
            if (!empty($vulnerableQueries)) {
                $issues[] = 'Potential SQL injection vulnerabilities found';
            }
            
            // Check database user privileges
            $privileges = DB::select('SHOW GRANTS');
            foreach ($privileges as $privilege) {
                if (str_contains($privilege->{'Grants for ' . $dbConfig['username'] . '@%'}, 'ALL PRIVILEGES')) {
                    $issues[] = 'Database user has ALL PRIVILEGES - consider using principle of least privilege';
                }
            }
            
        } catch (\Exception $e) {
            $issues[] = 'Could not complete database security audit: ' . $e->getMessage();
        }
        
        return [
            'issues' => $issues,
            'recommendations' => [
                'Use strong database credentials',
                'Limit database user privileges',
                'Enable database audit logging',
                'Regular database security updates',
                'Use parameterized queries only'
            ]
        ];
    }
    
    /**
     * Audit session security
     */
    protected function auditSessionSecurity()
    {
        $config = config('session');
        $issues = [];
        
        if (!$config['secure'] && app()->environment('production')) {
            $issues[] = 'Session cookies not marked as secure in production';
        }
        
        if (!$config['http_only']) {
            $issues[] = 'Session cookies not marked as HTTP only';
        }
        
        if ($config['same_site'] !== 'strict') {
            $issues[] = 'Session SameSite attribute not set to strict';
        }
        
        if ($config['lifetime'] > 120) {
            $issues[] = 'Session lifetime is longer than recommended (120 minutes)';
        }
        
        return [
            'issues' => $issues,
            'current_config' => $config,
            'recommendations' => [
                'Set secure flag for HTTPS',
                'Enable HTTP-only cookies',
                'Use strict SameSite policy',
                'Implement session timeout',
                'Regenerate session IDs on login'
            ]
        ];
    }
    
    /**
     * Audit API security
     */
    protected function auditApiSecurity()
    {
        $issues = [];
        
        // Check for rate limiting
        if (!class_exists('App\Http\Middleware\ApiRateLimitMiddleware')) {
            $issues[] = 'No API rate limiting middleware found';
        }
        
        // Check for API authentication
        $routes = app('router')->getRoutes();
        $unprotectedApiRoutes = [];
        
        foreach ($routes as $route) {
            if (str_starts_with($route->uri(), 'api/') && 
                !in_array('auth:sanctum', $route->middleware())) {
                $unprotectedApiRoutes[] = $route->uri();
            }
        }
        
        if (count($unprotectedApiRoutes) > 5) {
            $issues[] = 'Many unprotected API routes found (' . count($unprotectedApiRoutes) . ')';
        }
        
        return [
            'issues' => $issues,
            'unprotected_routes' => array_slice($unprotectedApiRoutes, 0, 10),
            'recommendations' => [
                'Implement API rate limiting',
                'Use API authentication for all endpoints',
                'Implement API versioning',
                'Add request/response logging',
                'Use HTTPS for all API calls'
            ]
        ];
    }
    
    /**
     * Get security recommendations
     */
    protected function getSecurityRecommendations()
    {
        return [
            'immediate' => [
                'Enable HTTPS across the entire application',
                'Implement strong password policies',
                'Add two-factor authentication',
                'Set up API rate limiting',
                'Configure security headers'
            ],
            'short_term' => [
                'Implement comprehensive logging',
                'Set up intrusion detection',
                'Regular security audits',
                'Employee security training',
                'Backup and disaster recovery plan'
            ],
            'long_term' => [
                'Security penetration testing',
                'Compliance certifications',
                'Advanced threat detection',
                'Security incident response plan',
                'Regular security updates and patches'
            ]
        ];
    }
    
    /**
     * Find potential SQL injection vulnerabilities
     */
    protected function findPotentialSqlInjections()
    {
        // This is a basic check - in production, use specialized tools
        $vulnerablePatterns = [
            'DB::raw(',
            'whereRaw(',
            'selectRaw(',
            'orderByRaw(',
            'havingRaw('
        ];
        
        $vulnerableFiles = [];
        
        // Scan controller files for potential issues
        $controllerPath = app_path('Http/Controllers');
        if (is_dir($controllerPath)) {
            $files = glob($controllerPath . '/**/*.php', GLOB_BRACE);
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                foreach ($vulnerablePatterns as $pattern) {
                    if (str_contains($content, $pattern)) {
                        $vulnerableFiles[] = basename($file);
                        break;
                    }
                }
            }
        }
        
        return array_unique($vulnerableFiles);
    }
}
