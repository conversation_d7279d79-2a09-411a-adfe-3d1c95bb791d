<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CacheService
{
    protected $defaultTtl = 3600; // 1 hour
    protected $longTtl = 86400; // 24 hours
    protected $shortTtl = 300; // 5 minutes

    /**
     * Cache user-specific data
     */
    public function cacheUserData($userId, $key, $data, $ttl = null)
    {
        $cacheKey = "user:{$userId}:{$key}";
        return Cache::put($cacheKey, $data, $ttl ?? $this->defaultTtl);
    }

    /**
     * Get user-specific cached data
     */
    public function getUserData($userId, $key, $default = null)
    {
        $cacheKey = "user:{$userId}:{$key}";
        return Cache::get($cacheKey, $default);
    }

    /**
     * Cache course-specific data
     */
    public function cacheCourseData($courseId, $key, $data, $ttl = null)
    {
        $cacheKey = "course:{$courseId}:{$key}";
        return Cache::put($cacheKey, $data, $ttl ?? $this->defaultTtl);
    }

    /**
     * Get course-specific cached data
     */
    public function getCourseData($courseId, $key, $default = null)
    {
        $cacheKey = "course:{$courseId}:{$key}";
        return Cache::get($cacheKey, $default);
    }

    /**
     * Cache global application data
     */
    public function cacheGlobalData($key, $data, $ttl = null)
    {
        $cacheKey = "global:{$key}";
        return Cache::put($cacheKey, $data, $ttl ?? $this->longTtl);
    }

    /**
     * Get global cached data
     */
    public function getGlobalData($key, $default = null)
    {
        $cacheKey = "global:{$key}";
        return Cache::get($cacheKey, $default);
    }

    /**
     * Cache API response
     */
    public function cacheApiResponse($endpoint, $params, $data, $ttl = null)
    {
        $cacheKey = "api:" . md5($endpoint . serialize($params));
        return Cache::put($cacheKey, $data, $ttl ?? $this->shortTtl);
    }

    /**
     * Get cached API response
     */
    public function getCachedApiResponse($endpoint, $params, $default = null)
    {
        $cacheKey = "api:" . md5($endpoint . serialize($params));
        return Cache::get($cacheKey, $default);
    }

    /**
     * Invalidate user cache
     */
    public function invalidateUserCache($userId, $pattern = '*')
    {
        $keys = $this->getKeys("user:{$userId}:{$pattern}");
        return Cache::deleteMultiple($keys);
    }

    /**
     * Invalidate course cache
     */
    public function invalidateCourseCache($courseId, $pattern = '*')
    {
        $keys = $this->getKeys("course:{$courseId}:{$pattern}");
        return Cache::deleteMultiple($keys);
    }

    /**
     * Invalidate global cache
     */
    public function invalidateGlobalCache($pattern = '*')
    {
        $keys = $this->getKeys("global:{$pattern}");
        return Cache::deleteMultiple($keys);
    }

    /**
     * Cache student dashboard data
     */
    public function cacheStudentDashboard($studentId, $data)
    {
        return $this->cacheUserData($studentId, 'dashboard', $data, $this->shortTtl);
    }

    /**
     * Get cached student dashboard data
     */
    public function getStudentDashboard($studentId)
    {
        return $this->getUserData($studentId, 'dashboard');
    }

    /**
     * Cache lecturer dashboard data
     */
    public function cacheLecturerDashboard($lecturerId, $data)
    {
        return $this->cacheUserData($lecturerId, 'lecturer_dashboard', $data, $this->shortTtl);
    }

    /**
     * Get cached lecturer dashboard data
     */
    public function getLecturerDashboard($lecturerId)
    {
        return $this->getUserData($lecturerId, 'lecturer_dashboard');
    }

    /**
     * Cache course materials
     */
    public function cacheCourseMaterials($courseId, $materials)
    {
        return $this->cacheCourseData($courseId, 'materials', $materials, $this->defaultTtl);
    }

    /**
     * Get cached course materials
     */
    public function getCourseMaterials($courseId)
    {
        return $this->getCourseData($courseId, 'materials');
    }

    /**
     * Cache assignments for a course
     */
    public function cacheCourseAssignments($courseId, $assignments)
    {
        return $this->cacheCourseData($courseId, 'assignments', $assignments, $this->shortTtl);
    }

    /**
     * Get cached course assignments
     */
    public function getCourseAssignments($courseId)
    {
        return $this->getCourseData($courseId, 'assignments');
    }

    /**
     * Cache notification count for user
     */
    public function cacheNotificationCount($userId, $count)
    {
        return $this->cacheUserData($userId, 'notification_count', $count, 60); // 1 minute
    }

    /**
     * Get cached notification count
     */
    public function getNotificationCount($userId)
    {
        return $this->getUserData($userId, 'notification_count');
    }

    /**
     * Remember callback result with caching
     */
    public function remember($key, $ttl, $callback)
    {
        return Cache::remember($key, $ttl, $callback);
    }

    /**
     * Remember callback result forever
     */
    public function rememberForever($key, $callback)
    {
        return Cache::rememberForever($key, $callback);
    }

    /**
     * Flush all cache
     */
    public function flush()
    {
        return Cache::flush();
    }

    /**
     * Get cache keys matching pattern
     */
    protected function getKeys($pattern)
    {
        if (config('cache.default') === 'redis') {
            return Redis::keys($pattern);
        }
        
        // For other cache drivers, we'll need to track keys manually
        // This is a simplified implementation
        return [];
    }

    /**
     * Cache statistics data
     */
    public function cacheStats($key, $data, $ttl = null)
    {
        $cacheKey = "stats:{$key}";
        return Cache::put($cacheKey, $data, $ttl ?? $this->defaultTtl);
    }

    /**
     * Get cached statistics
     */
    public function getStats($key, $default = null)
    {
        $cacheKey = "stats:{$key}";
        return Cache::get($cacheKey, $default);
    }

    /**
     * Cache search results
     */
    public function cacheSearchResults($query, $filters, $results, $ttl = null)
    {
        $cacheKey = "search:" . md5($query . serialize($filters));
        return Cache::put($cacheKey, $results, $ttl ?? $this->shortTtl);
    }

    /**
     * Get cached search results
     */
    public function getCachedSearchResults($query, $filters, $default = null)
    {
        $cacheKey = "search:" . md5($query . serialize($filters));
        return Cache::get($cacheKey, $default);
    }

    /**
     * Warm up cache with commonly accessed data
     */
    public function warmUpCache()
    {
        // Cache commonly accessed global data
        $this->cacheGlobalData('academic_years', \App\Models\AcademicYear::all());
        $this->cacheGlobalData('levels', \App\Models\Level::all());
        $this->cacheGlobalData('departments', \App\Models\Department::all());
        $this->cacheGlobalData('programs', \App\Models\Program::all());
        
        return true;
    }
}
