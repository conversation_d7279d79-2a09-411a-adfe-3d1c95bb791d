<?php

namespace App\Repositories;

use App\Models\Account;
use App\Models\Expense;
use App\Models\PayRoll;
use App\Models\Transaction;
use App\Repositories\Interfaces\ExpenseInterface;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ExpenseRepository implements ExpenseInterface
{
    public function all()
    {
        return $this->query()->get();
    }

    public function findById($id)
    {
        return $this->query()->find($id);
    }

    public function create(array $data)
    {
        $ref = $this->getSchoolInitials() . "-" . strtoupper(Str::random(6)) . str_replace('-', '', Carbon::now()->format('Y-m-d'));
        $data['reference'] = $ref;
        $expense = Expense::create($data);

        $this->recordTransaction($expense);

        // update the account balance by deducting the amount
        $account = Account::find($data['account_id']);
        $account->decrement('balance', $data['amount']);

        return $expense;
    }

    public function update(array $data, $id)
    {
        $expense = $this->query()->find($id);

        if ($expense) {
            $oldAmount = $expense->amount;
            $newAmount = $data['amount'];
            $account = $expense->account; // Assuming the expense has a relation to account

            // Update the expense
            $expense->update($data);

            // Update the associated transaction
            $transaction = Transaction::where('transactionable_id', $expense->id)
                                      ->where('transactionable_type', Expense::class)
                                      ->first();

            if ($transaction) {
                $transaction->update([
                    'amount' => $newAmount,
                    'reference' => $expense->reference,
                    'transaction_date' => now()->format('Y-m-d'),
                    'status' => 'approved',
                ]);
            }

            // Adjust account balance if the amount has changed
            if ($newAmount !== $oldAmount) {
                $account->decrement('balance', $oldAmount);
                $account->increment('balance', $newAmount);
            }

            return $expense;
        }

        return null;
    }

    public function destroy($id)
    {
        $expense = $this->query()->find($id);

        if ($expense) {
            // Delete the associated transaction
            Transaction::where('transactionable_id', $expense->id)
                        ->where('transactionable_type', Expense::class)
                        ->delete();

            // Delete the expense
            return $expense->delete();
        }

        return false;
    }

    public function getExpensesByDate($startDate, $endDate)
    {
        return Expense::whereBetween('created_at', [$startDate, $endDate])->get();
    }

    public function payLecturer($amount, $lecturer, $account, $date)
    {
        $expense = Expense::create([
            'amount' => $amount,
            'reference' => $this->getSchoolInitials() . "-" . strtoupper(Str::random(6)) . str_replace('-', '', Carbon::now()->format('Y-m-d')),
            'lecturer_id' => $lecturer->id,
            'user_id' => $lecturer->id,
            'account_id' => $account,
        ]);

        PayRoll::create([
            'user_id' => $lecturer->id,
            'account_id' =>  $account,
            'amount' => $amount,
            'payment_date' => $date,
            'reference' => $expense->reference,
            'status' => 'approved'
        ]);

        $this->recordTransaction($expense);

        return $expense;
    }

    private function recordTransaction(Expense $expense)
    {
        return Transaction::create([
            'amount' => $expense->amount,
            'transactionable_id' => $expense->id,
            'transactionable_type' => Expense::class,
            'transaction_type' => 'Expense',
            'transaction_date' => now()->format('Y-m-d'),
            'type' => 'expense',
            'reference' => $expense->reference,
            'user_id' => $expense->user_id,
            'created_by_id' => Auth::id(),
            'status' => 'approved'
        ]);
    }

        public function getFilteredExpenses($conditions)
    {
        $query = Expense::query();

        if (isset($conditions['start_date']) && isset($conditions['end_date'])) {
            $query->whereBetween('expense_date', [$conditions['start_date'], $conditions['end_date']]);
        }

        if (isset($conditions['reference'])) {
            $query->where('reference', $conditions['reference']);
        }

        if (isset($conditions['account_id'])) {
            $query->where('account_id', $conditions['account_id']);
        }

        return $query->with('student', 'account', 'transactions', 'lecturer')->get();
}


    /**
     * Get an expense by its reference.
     *
     * @param string $reference
     * @return \App\Models\Expense|null
     */
    public function getByReference($reference)
    {
        return $this->query()->where('reference', $reference)->first();
    }

    /**
     * Get all expenses associated with a given account.
     *
     * @param int $account
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByAccount($account)
    {
        return $this->query()->where('account_id', $account)->get();
    }

    /**
     * Base query for Expense model with relationships.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function query()
    {
        return Expense::with('student', 'account', 'transactions', 'lecturer', 'category');
    }




    private function getSchoolInitials()
    {
        return config('app.school.initials');
    }
}
