<?php

namespace App\Repositories;

use App\Models\AcademicYear;

class AcademicYearRepositoryInterfaceImpl implements Interfaces\AcademicYearRepositoryInterface
{

    protected AcademicYear $model;

    public function __construct(AcademicYear $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        return $this->model->all();
    }

    public function find($id)
    {
        return $this->model->findOrFail($id);
    }

    public function search(mixed $query)
    {
       return $this->model->query()
           ->where('name', 'LIKE', "%{$query}%")
           ->orWhere('start_date', 'LIKE', "%{$query}%")
           ->orWhere('end_date', 'LIKE', "%{$query}%")
           ->orWhere('description', 'LIKE', "%{$query}%");
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update($id, array $data)
    {
        $academicYear = $this->model->findOrFail($id);
        $academicYear->update($data);
        return $academicYear;
    }

    public function delete($id)
    {
        $academicYear = $this->model->findOrFail($id);
        return $academicYear->delete();
    }
}
