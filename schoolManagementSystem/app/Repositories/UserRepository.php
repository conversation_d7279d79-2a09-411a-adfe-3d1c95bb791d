<?php

namespace App\Repositories;

use App\Models\Student;
use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;
use Illuminate\Support\Facades\DB;

class UserRepository implements UserRepositoryInterface
{
    public function findByUsernameAndPhoneNumber($username, $phoneNumber)
    {
        return User::where('email', $username)
            ->orWhere('phone_number', $phoneNumber)
            ->first();
    }

    public function createToken($user, $tokenName)
    {
        return $user->createToken($tokenName)->plainTextToken;
    }

    public function findByEmail($email)
    {
        return User::where('email', $email)->first();
    }

    public function findByUsername($username)
    {
        return User::where('username', $username)->first();
    }

    public function findByPhoneNumber($phoneNumber)
    {
        return User::where('phone_number', $phoneNumber)->first();
    }

    public function findById($id)
    {
        return User::find($id);
    }

    public function findByRole($role)
    {
        return User::whereHas('roles', function ($query) use ($role) {
            $query->where('name', $role);
        })->get();
    }

    public function findbyMatricule($matricule)
    {
        return Student::with('user', 'currentLevel', 'academicYear', 'department.school')->where('matricule', $matricule)->first();
    }

    public function deleteToken($user, $tokenName)
    {
        $user->tokens()->delete();
    }

    public function create(array $data, $role)
    {
        try {
            DB::beginTransaction();
            $user = User::create([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'gender' => $data['gender'],
                'email' => $data['email'],
                'phone_number' => $data['phone_number'],
                'password' => $data['password'],
                'username' => $data['username'],
                'profile_image' => $data['profile_image'],
                'dob' => $data['dob'],

            ]);

            if ($role === 'student') {
                $user->student()->create([
                    'matricule' => $data['matricule'] ?? null,
                    'place_of_birth' => $data['place_of_birth'] ?? null,
                    'region_of_origin' => $data['region_of_origin'] ?? null,
                    'marital_status' => $data['marital_status'] ?? null,
                    'date_of_admission' => $data['date_of_admission'] ?? null,
                    'department_id' => $data['department_id'] ?? null,
                    'nationality' => $data['nationality']    ?? null,
                    'level_id' => $data['level_id'] ?? null,
                    'current_level_id' => $data['current_level_id'] ?? $data['level_id'] ?? null,
                    'program_id' => $data['program_id'] ?? null,
                    'academic_year_id' => $data['academic_year_id'] ?? null,
                ]);
            }
            $user->assignRole($role);
            DB::commit();
            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, $id)
    {
        return User::where('id', $id)->update($data);
    }

    public function delete($id)
    {
        return User::where('id', $id)->delete();
    }
}
