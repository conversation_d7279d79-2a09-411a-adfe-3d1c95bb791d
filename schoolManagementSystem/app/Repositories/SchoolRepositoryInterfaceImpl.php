<?php

namespace App\Repositories;

use App\Models\Department;
use App\Models\School;
use App\Repositories\Interfaces\SchoolRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class SchoolRepositoryInterfaceImpl implements SchoolRepositoryInterface
{
    protected $cacheTTL = 60 * 1; // 1 hour cache expiration time

    public function all()
    {
        return Cache::remember('schools_all', $this->cacheTTL, function () {
            return School::with('admin', 'departments', 'programRelation')->get();
        });

    }

    public function getSchoolsByAdminId($admin_id)
    {
        $cacheKey = "schools_by_admin_{$admin_id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($admin_id) {
            return School::where('admin_id', $admin_id)->get();
        });
    }

    public function findById($id)
    {
        $cacheKey = "school_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($id) {
            return School::find($id);
        });
    }

    public function findBySlug($slug)
    {
        $cacheKey = "school_slug_{$slug}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($slug) {
            return School::where('slug', $slug)->first();
        });
    }

    public function create(array $data)
    {
        $school = School::create($data);
        $this->clearCache(); // Clear cache after creation
        return $school;
    }

    public function update(array $data, $id)
    {
        $school = School::find($id);

        if (!$school) {
            throw new \Exception('School not found');
        }

        $school->update($data);
        $this->clearCache(); // Clear cache after update

        return $school;
    }

    public function delete($id)
    {
        $deleted = School::destroy($id);
        if ($deleted) {
            $this->clearCache(); // Clear cache after deletion
        }
        return $deleted;
    }

    public function findByName($name)
    {
        $cacheKey = "school_name_{$name}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($name) {
            return School::where('name', $name)->first();
        });
    }

    public function getDepartmentsBySchoolId($school_id)
    {
        $cacheKey = "departments_by_school_{$school_id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($school_id) {
            return Department::where('school_id', $school_id)->get();
        });
    }

    // Utility Methods

    /**
     * Clear cache related to schools.
     */
    protected function clearCache()
    {
        Cache::tags(['schools'])->flush();
    }
}
