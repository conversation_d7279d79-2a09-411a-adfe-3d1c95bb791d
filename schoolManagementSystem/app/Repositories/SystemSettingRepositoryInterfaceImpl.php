<?php

namespace App\Repositories;

use App\Models\SystemSetting;
use App\Repositories\Interfaces\SystemSettingRepositoryInterface;

class SystemSettingRepositoryInterfaceImpl implements SystemSettingRepositoryInterface

{
    public function all()
    {
        return SystemSetting::select('key', 'value')->get();
    }

    public function get($key)
    {
        return SystemSetting::where('key', $key)->first();
    }

    public function update($key, $value)
    {
        $setting = SystemSetting::where('key', $key)->first();
        if(!$setting){
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException("System system with $key not found");
        }
        if(SystemSetting::where('key', $key)->update(['value' => $value])){
            return SystemSetting::where('key', $key)->first();
        };

        throw new \Exception('Failed to update system setting');
    }

    public function delete($key)
    {
        return SystemSetting::where('key', $key)->delete();
    }

    public function clear()
    {
        return SystemSetting::truncate();
    }

}


