<?php

namespace App\Repositories;

use App\Models\ExpenseCategory;
use App\Models\IncomeCategory;
use App\Repositories\Interfaces\CategoryCrudInterface;

class CategoryCrudRepository implements CategoryCrudInterface
{

    public function allIncomeCategories()
    {
        return IncomeCategory::all();
    }


    public function allExpenseCategories()
    {
        return ExpenseCategory::all();
    }


    public function createIncomeCategory(array $data)
    {
        return IncomeCategory::create($data);
    }


    public function createExpenseCategory(array $data)
    {
        return ExpenseCategory::create($data);
    }


    public function updateIncomeCategory(array $data, $id)
    {
        return IncomeCategory::find($id)->update($data);
    }


    public function updateExpenseCategory(array $data, $id)
    {
        return ExpenseCategory::find($id)->update($data);
    }


    public function deleteIncomeCategory($id)
    {
        return IncomeCategory::find($id)->delete();
    }


    public function deleteExpenseCategory($id)
    {
        return ExpenseCategory::find($id)->delete();
    }


    public function findById(string $model, $Id)
    {

        return $model::find($Id);
    }


    public function search(string $model, $params)
    {

        return $model::where($params)->get();
    }

}
