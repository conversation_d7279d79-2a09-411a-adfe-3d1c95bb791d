<?php

namespace App\Repositories;

use App\Models\Course;
use App\Models\Student;
use App\Models\SystemSetting;
use Illuminate\Support\Facades\Log;

class CourseRegistrationRepository
{
    public function getRegisteredCourses($student_id, $academic_year, $semester, $level_id)
{
    try {
        $student = Student::with([
            'courses' => function ($query) use ($academic_year, $semester, $level_id) {
                $query->wherePivot('academic_year', $academic_year)
                      ->wherePivot('semester', $semester)
                      ->wherePivot('level_id', $level_id);
            },
            'department.school',
            'courses.level',
            'courses.lecturer',
            'courses.academic_year',
            'program',
            'user'
        ])->find($student_id);


        if (!$student) {
            throw new \Exception("Student not found");
        }

        if ($student->courses->isEmpty()) {
            return [];
        }

        // Structure data for the view
        return [
            'student' => [
                'first_name' => $student->user->first_name,
                'last_name' => $student->user->last_name,
                'matricule' => $student->matricule,
                'program_offered' => $student->program->name,
                'level' => $student->level->name,
                'department' => $student->department->name,
                'school' => $student->department->school->name,
                'nationality' => $student->nationality,
                'place_of_birth' => $student->place_of_birth,
                'region_of_origin' => $student->region_of_origin,
                'marital_status' => $student->marital_status,
                'date_of_admission' => $student->date_of_admission,
                'academic_year' => $student->academicYear ? $student->academicYear->name : SystemSetting::get('academic_year'),



            ],
            'courses' => $student->courses->map(function ($course) {
                return [
                    'name' => $course->name,
                    'code' => $course->code,
                    'credit_value' => $course->credit_value,
                    'lecturer' => $course->lecturer ? $course->lecturer->first_name: "NA",
                    'level' => $course->level ? $course->level->name : "NA"
                ];


            })->toArray(),
            'stats' => [
                'total_credits' => $student->courses->sum('credit_value'),
                'total_hours' => $student->courses->sum('credit_hours'),
            ]
        ];

    } catch (\Exception $e) {
        Log::error('Error fetching registered courses: ' . $e->getMessage(), [
            'student_id' => $student_id,
            'academic_year' => $academic_year,
            'semester' => $semester,
            'level_id' => $level_id,
            'exception' => $e
        ]);

        throw $e; // Rethrow the exception if you want to propagate it further
    }
}




    public function registerCourse($student_id, $academic_year, $semester, $level_id, $course_id)
    {
        $student = Student::find($student_id);
        $course = Course::find($course_id);
        $courses = $student->courses()->attach($course, ['academic_year' => $academic_year, 'level_id' => $level_id, 'semester' => $semester]);
        return $courses;
    }

    public function unregisterCourse($student_id,  $course_id)
    {
        $student = Student::find($student_id);
        $course = Course::find($course_id);
        $student->courses()->detach($course);
    }

    public function getRegisteredCourse($student_id, $academic_year, $semester, $level_id, $course_id)
    {
        return Student::find($student_id)
            ->courses()
            ->wherePivot('academic_year', $academic_year)
            ->wherePivot('semester', $semester)
            ->wherePivot('level_id', $level_id)
            ->wherePivot('course_id', $course_id)
            ->first();
    }


}
