<?php

namespace App\Repositories;

use App\Models\Program;
use App\Repositories\Interfaces\ProgramRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class ProgramRepositoryInterfaceImpl implements ProgramRepositoryInterface
{
    protected $cacheTTL = 60 * 1; // 1 hour cache expiration time

    public function createOrUpdate(array $data)
    {
        $program = Program::updateOrCreate(
            ['id' => $data['id'] ?? null],
            $data
        );

        $this->clearCache(); // Clear cache after creating or updating

        return $program;
    }

    public function delete($id)
    {
        $program = Program::find($id);
        if ($program) {
            $deleted = $program->delete();
            if ($deleted) {
                $this->clearCache(); // Clear cache after deletion
            }
            return $deleted;
        }
        return false;
    }

    public function getById($id)
    {
        $cacheKey = "program_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($id) {
            return Program::find($id);
        });
    }

    public function all()
    {
        return Cache::remember('programs_all', $this->cacheTTL, function () {
            return Program::all();
        });
    }

    public function getBySchool(int $schoolId)
    {
        $cacheKey = "programs_by_school_{$schoolId}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($schoolId) {
            return Program::whereHas('schools', function ($query) use ($schoolId) {
                $query->where('school_id', $schoolId);
            })->get();
        });
    }

    public function search($input): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $cacheKey = "program_search_{$input}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($input) {
            $results = Program::query()
                ->where('name', 'LIKE', "%{$input}%")
                ->orWhere('description', 'LIKE', "%{$input}%")
                ->orWhere('cost', 'LIKE', "%{$input}%");

            return $results->paginate(10);
        });
    }

    public function findByNameOrAbbreviation($keys)
    {
        $cacheKey = "program_findByNameOrAbbreviation_" . implode('_', $keys);

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($keys) {
            $query = Program::query();
            foreach ($keys as $key) {
                $query->orWhere('name', 'LIKE', "%{$key}%")
                    ->orWhere('abbreviation', 'LIKE', "%{$key}%");
            }
            return $query->get();
        });
    }

    public function findByIds($ids)
    {
        $cacheKey = "programs_by_ids_" . implode('_', $ids);

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($ids) {
            return Program::whereIn('id', $ids)->get();
        });
    }

    // Utility Methods

    /**
     * Clear cache related to programs.
     */
    protected function clearCache()
    {
        Cache::tags(['programs'])->flush();
    }
}
