<?php

namespace App\Repositories;

use App\Models\FeeType;
use App\Repositories\Interfaces\FeeTypeRepositoryInterface;

class FeeTypeRepositoryInterfaceImpl implements FeeTypeRepositoryInterface
{
    protected FeeType $model;

    public function __construct(FeeType $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        return $this->model->all();
    }

    public function find($id)
    {
        return $this->model->findOrFail($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update($id, array $data)
    {
        $feeType = $this->model->findOrFail($id);
        $feeType->update($data);
        return $feeType;
    }

    public function delete($id)
    {
        $feeType = $this->model->findOrFail($id);
        return $feeType->delete();
    }

    public function search($input)
    {
        return $this->model->query()
            ->where('name', 'LIKE', "%$input%")
            ->orWhere('description', 'LIKE', "%$input%");
    }
}
