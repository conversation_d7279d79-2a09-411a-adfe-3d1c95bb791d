<?php

namespace App\Repositories;

use App\Models\ContinuousAssessment;
use App\Models\Exam;
use App\Models\Result;
use App\Repositories\Interfaces\ResultRepositoryInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ResultRepository implements ResultRepositoryInterface
{
    public function getResults($student_id, $academic_year, $semester, $level_id): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember("results:{$student_id}:{$academic_year}:{$semester}:{$level_id}", 60, function () use ($student_id, $academic_year, $semester, $level_id) {
            return $this->getResult($student_id, $academic_year, $semester, $level_id);
        });
    }

    public function getCAResults($student_id, $academic_year, $semester, $level_id): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember("ca_results:{$student_id}:{$academic_year}:{$semester}:{$level_id}", 60, function () use ($student_id, $academic_year, $semester, $level_id) {
            return $this->getResult($student_id, $academic_year, $semester, $level_id);
        });
    }

    public function getExamResults($student_id, $academic_year, $semester, $level_id): \Illuminate\Database\Eloquent\Collection
    {
        return Cache::remember("exam_results:{$student_id}:{$academic_year}:{$semester}:{$level_id}", 60, function () use ($student_id, $academic_year, $semester, $level_id) {
            return $this->getResult($student_id, $academic_year, $semester, $level_id);
        });
    }

    public function updateCAMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark)
    {
        return $this->updateMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark, 'ca_mark');
    }

    public function updateExamMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark)
    {
        return $this->updateMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark, 'exam_mark');
    }

    private function getResult($student_id, $academic_year, $semester, $level_id): \Illuminate\Database\Eloquent\Collection
    {
        return Result::with('course', 'student', 'student.department.school')
            ->where('student_id', $student_id)
            ->where('academic_year_id', $academic_year)
            ->where('semester', $semester)
            ->where('level_id', $level_id)
            // ->whereHas('course', function($query) use ($student_id, $academic_year, $semester, $level_id) {
            //     $query->whereHas('students', function($q) use ($student_id, $academic_year, $semester, $level_id) {
            //         $q->where('student_id', $student_id)
            //           ->where('academic_year', $academic_year)
            //           ->where('semester', $semester)
            //           ->where('course_student.level_id', $level_id);
            //     });
            ->get();
    }

    private function updateMark($course_id, $student_id, $academic_year, $semester, $level_id, $mark, $field)
    {
        $results = Result::where([
            'student_id' => $student_id,
            'course_id' => $course_id,
            'academic_year_id' => $academic_year,
            'semester' => $semester,
            'level_id' => $level_id
        ])->first();

        if ($results) {
            $results->$field = $mark;

            ContinuousAssessment::where([
                'student_id' => $student_id,
                'course_id' => $course_id,
            ])->update(['score' => $mark]);

            $results->save();
        }

        return $results;
    }

    public function getResultByTypeByMatriculeAndByAcademicYear($data)
    {
        $cacheKey = "result_by_matricule:{$data['matricule']}:{$data['academic_year_id']}:{$data['semester']}:{$data['level_id']}";

        return Cache::remember($cacheKey, 05, function () use ($data) {
            return DB::table('results')
                ->select('results.*', 'students.matricule', 'students.user_id', 'students.program_id', 'students.level_id')
                ->join('students', 'students.id', '=', 'results.student_id')
                ->where('students.matricule', $data['matricule'])
                ->where('results.academic_year_id', $data['academic_year_id'])
                ->where('results.semester', $data['semester'])
                ->where('students.level_id', $data['level_id'])
                ->get();
        });
    }

    public function getAllResultsBySemesterAndYearAndLevel($academicYearId = null, $semester = null, $levelId = null, $perPage = 10)
    {
        $cacheKey = "all_results:{$academicYearId}:{$semester}:{$levelId}:{$perPage}";

        return Cache::remember($cacheKey, 05, function () use ($academicYearId, $semester, $levelId, $perPage) {
            return Result::select(DB::raw('
                    student_id,
                    MAX(course_id) as course_id,
                    MAX(academic_year_id) as academic_year_id,
                    MAX(semester) as semester,
                    MAX(level_id) as level_id,
                    MAX(grade) as grade,
                    MAX(grade_point) as grade_point,
                    MAX(weighted_point) as weighted_point,
                    SUM(ca_mark) as total_ca_marks,
                    SUM(exam_mark) as total_exam_marks,
                    SUM(total_mark) as total_marks
                '))
                ->with(['course.level', 'student'])
                ->when($academicYearId, function ($query) use ($academicYearId) {
                    $query->where('academic_year_id', $academicYearId);
                })
                ->when($semester, function ($query) use ($semester) {
                    $query->where('semester', $semester);
                })
                ->when($levelId, function ($query) use ($levelId) {
                    $query->where('level_id', $levelId);
                })
                ->groupBy('student_id', 'course_id', 'level_id') // Group by student_id to get results per student
                ->paginate($perPage);
        });
    }

    public function getAllResultsBySemesterAndYearAndLevelUnpaginated($academicYearId = null, $semester = null, $levelId = null, $perPage = 10)
    {
        $cacheKey = "all_results:{$academicYearId}:{$semester}:{$levelId}";

        return Cache::remember($cacheKey, 05, function () use ($academicYearId, $semester, $levelId, $perPage) {
            return Result::select(DB::raw('
                    student_id,
                    MAX(course_id) as course_id,
                    MAX(academic_year_id) as academic_year_id,
                    MAX(semester) as semester,
                    MAX(level_id) as level_id,
                    MAX(grade) as grade,
                    MAX(grade_point) as grade_point,
                    MAX(weighted_point) as weighted_point,
                    SUM(ca_mark) as total_ca_marks,
                    SUM(exam_mark) as total_exam_marks,
                    SUM(total_mark) as total_marks
                '))
                ->with(['course.level', 'student'])
                ->when($academicYearId, function ($query) use ($academicYearId) {
                    $query->where('academic_year_id', $academicYearId);
                })
                ->when($semester, function ($query) use ($semester) {
                    $query->where('semester', $semester);
                })
                ->when($levelId, function ($query) use ($levelId) {
                    $query->where('level_id', $levelId);
                })
                ->groupBy('student_id', 'course_id', 'level_id') // Group by student_id to get results per student
                ->get();

        });
    }


    public function getAllCAMarks($academicYearId = null, $semester = null, $levelId = null, $perPage = 10)
    {
        $cacheKey = "all_ca_marks:{$academicYearId}:{$semester}:{$levelId}:{$perPage}";

        return Cache::remember($cacheKey, 05, function () use ($academicYearId, $semester, $levelId, $perPage) {
            return ContinuousAssessment::with(['course.level', 'student', 'results' => function($query) use ($semester) {
                $query->when($semester, function($q) use ($semester) {
                    $q->where('semester', $semester);
                });
            }])
            ->when($academicYearId, function ($query) use ($academicYearId) {
                $query->where('academic_year_id', $academicYearId);
            })
            ->when($levelId, function ($query) use ($levelId) {
                $query->whereHas('course', function ($query) use ($levelId) {
                    $query->where('level_id', $levelId);
                });
            })
            ->paginate($perPage);
        });
    }

    public function getCAMarksForStudent($studentId, $academicYearId = null, $semester = null, $levelId = null)
    {
        $cacheKey = "ca_marks_for_student:{$studentId}:{$academicYearId}:{$semester}:{$levelId}";

        return Cache::remember($cacheKey, 10, function () use ($studentId, $academicYearId, $semester, $levelId) {
            return ContinuousAssessment::with(['course.level', 'student', 'results' => function($query) use ($semester) {
                $query->when($semester, function($q) use ($semester) {
                    $q->where('semester', $semester);
                });
            }])
            ->where('student_id', $studentId)
            ->when($academicYearId, function ($query) use ($academicYearId) {
                $query->where('academic_year_id', $academicYearId);
            })
            // ->when($levelId, function ($query) use ($levelId) {
            //     $query->whereHas('course', function ($query) use ($levelId): void {
            //         $query->where('level_id', $levelId);
            //     });
            // })
            ->get();
        });
    }

    public function getAllExamMarks($academicYearId = null, $semester = null, $levelId = null, $perPage = 10)
    {
        $cacheKey = "all_exam_marks:{$academicYearId}:{$semester}:{$levelId}:{$perPage}";

        return Cache::remember($cacheKey, 10, function () use ($academicYearId, $semester, $levelId, $perPage) {
            return Exam::with(['course.level', 'student', 'results' => function($query) use ($semester) {
                $query->when($semester, function($q) use ($semester) {
                    $q->where('semester', $semester);
                });
            }])
            ->when($academicYearId, function ($query) use ($academicYearId) {
                $query->where('academic_year_id', $academicYearId);
            })
            ->when($levelId, function ($query) use ($levelId) {
                $query->whereHas('course', function ($query) use ($levelId) {
                    $query->where('level_id', $levelId);
                });
            })
            ->paginate($perPage);
        });
    }

    public function getExamMarksForStudent($studentId, $academicYearId = null, $semester = null, $levelId = null)
    {
        $cacheKey = "exam_marks_for_student:{$studentId}:{$academicYearId}:{$semester}:{$levelId}";

        return Cache::remember($cacheKey, 10, function () use ($studentId, $academicYearId, $semester, $levelId) {
            return Exam::with(['course.level', 'student', 'results' => function($query) use ($semester) {
                $query->when($semester, function($q) use ($semester) {
                    $q->where('semester', $semester);
                });
            }])
            ->where('student_id', $studentId)
            ->when($academicYearId, function ($query) use ($academicYearId) {
                $query->where('academic_year_id', $academicYearId);
            })
            ->when($levelId, function ($query) use ($levelId) {
                $query->whereHas('course', function ($query) use ($levelId) {
                    $query->where('level_id', $levelId);
                });
            })
            ->get();
        });
    }
}
