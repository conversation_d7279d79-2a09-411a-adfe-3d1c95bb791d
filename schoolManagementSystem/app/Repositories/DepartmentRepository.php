<?php

namespace App\Repositories;

use App\Models\Department;
use App\Repositories\Interfaces\DepartmentRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class DepartmentRepository implements DepartmentRepositoryInterface
{
    protected $cacheTTL = 60 * 5; // 1 hour for cache timeout

    public function all()
    {
        return Cache::remember('departments_all', $this->cacheTTL, function () {
            return Department::with('school')->get();
        });
    }

    public function withPagination($request)
    {
        $perPage = $request->input('per_page', 10);
        $cacheKey = "departments_pagination_{$perPage}_page_{$request->input('page', 1)}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($perPage) {
            return Department::with('school')->paginate($perPage);
        });
    }

    public function findById($id)
    {
        $cacheKey = "department_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($id) {
            return Department::find($id);
        });
    }


    public function create(array $data)
    {
        $department = Department::create($data);
        $this->clearCache(); // Clear cache after creating
        return $department;
    }

    public function update(array $data, $id)
    {
        $department = Department::find($id);

        if (!$department) {
            throw new \Exception('Department not found');
        }

        $changedData = array_filter($data, function ($value, $key) use ($department) {
            return $department->$key !== $value;
        }, ARRAY_FILTER_USE_BOTH);

        if (!empty($changedData)) {
            $department->update($changedData);
            $this->clearCache(); // Clear cache after updating
        }

        return $department;
    }

    public function delete($id)
    {
        $deleted = Department::destroy($id);
        if ($deleted) {
            $this->clearCache(); // Clear cache after deletion
        }
        return $deleted;
    }

    public function getDepartmentById($id)
    {
        return $this->findById($id);
    }

    public function search($query)
    {
        $cacheKey = "departments_search_{$query}";

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($query) {
            return Department::where('name', 'like', '%' . $query . '%')
                ->orWhere('code', 'like', '%' . $query . '%')
                ->orWhere('description', 'like', '%' . $query . '%')
                ->get();
        });
    }

    // Utility Methods

    /**
     * Clear cache related to departments.
     */
    protected function clearCache()
    {
        Cache::tags(['departments'])->flush();
    }
}
