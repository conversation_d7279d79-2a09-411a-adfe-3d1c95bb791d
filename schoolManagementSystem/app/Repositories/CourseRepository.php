<?php

namespace App\Repositories;

use App\Models\Course;
use App\Models\Student;
use App\Repositories\Interfaces\CourseRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class CourseRepository implements CourseRepositoryInterface
{
    protected $cacheTTL = 01; // 1 minute

    public function all($request)
    {
        $cacheKey = $this->generateCacheKey($request->all());

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($request) {
            $query = Course::query();

            if ($request->has('level_id')) {
                $query->where('level_id', $request->input('level_id'))
                ->orWhereNull('level_id');
            }

            if ($request->has('department_id')) {
                $query->where('department_id', $request->input('department_id'));
            }

            if ($request->has('lecturer_id')) {
                $query->where('lecturer_id', $request->input('lecturer_id'))
                ->orWhereNull('lecturer_id');
            }

            if ($request->has('semester')) {
                $query->where('semester', $request->input('semester'))
                // check for null too
                    ->orWhereNull('semester');
            }

            // load the relations
            $query->with('level', 'department', 'lecturer');
            return $query->get();
        });
    }

    public function findById($id)
    {
        $cacheKey = "course_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($id) {
            return Course::with('level', 'lecturer', 'department')->find($id);
        });
    }

    public function create(array $data)
    {
        $course = Course::create($data);
        $this->clearCache(); // Clear cache after creation
        return $course;
    }

    public function update(array $data, $id)
    {
        $course = Course::find($id);

        if (!$course) {
            throw new \Exception('Course not found');
        }

        $changedData = array_filter($data, function ($value, $key) use ($course) {
            return $course->$key !== $value;
        }, ARRAY_FILTER_USE_BOTH);

        if (!empty($changedData)) {
            $course->update($changedData);
            $this->clearCache(); // Clear cache after updating
        }

        return $course;
    }

    public function delete($id)
    {
        $deleted = Course::destroy($id);
        if ($deleted) {
            $this->clearCache(); // Clear cache after deletion
        }
        return $deleted;
    }

    public function getCourseById($id)
    {
        return $this->findById($id);
    }

    public function search($query)
    {
        $cacheKey = "course_search_{$query}";

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($query) {
            return Course::where('name', 'like', '%' . $query . '%')
                ->orWhere('code', 'like', '%' . $query . '%')
                ->orWhere('description', 'like', '%' . $query . '%')
                ->get();
        });
    }

    public function getCourseByLecturer($id)
    {
        $cacheKey = "courses_by_lecturer_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($id) {
            return Course::where('lecturer_id', $id)->get();
        });
    }

    public function getDepartmentByCourseId($id)
    {
        $cacheKey = "department_by_course_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($id) {
            return Course::where('id', $id)->with('department')->first();
        });
    }

    public function getCoursesByDepartment($id)
    {
        $cacheKey = "courses_by_department_{$id}";

        return Cache::remember($cacheKey, $this->cacheTTL, function() use ($id) {
            return Course::where('department_id', $id)->get();
        });
    }

    public function getCoursesByStudentDepartment()
    {
        $cacheKey = "courses_by_student_department";

        return Cache::remember($cacheKey, $this->cacheTTL, function() {
            $studentDepartments = Student::pluck('department_id');
            return Course::whereIn('department_id', $studentDepartments)->get();
        });
    }

    // Utility Methods

    /**
     * Generate a unique cache key for requests based on filters
     */
    protected function generateCacheKey($filters)
    {
        return 'courses_' . md5(json_encode($filters));
    }

    /**
     * Clear cache keys related to courses
     */
    protected function clearCache()
    {
        Cache::tags(['courses'])->flush();
    }
}
