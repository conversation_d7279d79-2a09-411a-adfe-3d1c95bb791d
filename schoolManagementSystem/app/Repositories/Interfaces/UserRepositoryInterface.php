<?php

namespace App\Repositories\Interfaces;

use App\Models\User;

interface UserRepositoryInterface
{
    public function findByUsernameAndPhoneNumber($username, $phoneNumber);
    public function createToken($user, $tokenName);

    public function findByUsername($username);

    public function findByEmail($email);

    public function findByPhoneNumber($phoneNumber);

    public function findById($id);

    public function findByRole($role);

    public function findbyMatricule($matricule);


}
