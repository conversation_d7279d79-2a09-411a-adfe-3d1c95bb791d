<?php

namespace App\Repositories\Interfaces;

interface StudentRepositoryInterface
{
    public function all();
    public function findById($id);

    public function findByMatricule($matricule);

    public function create(array $data);

    public function updateUserAccount(array $data, $id);
    public function updateStudent(array $data, $id);
    public function delete($id);
    public function search($query);
}
