<?php

namespace App\Repositories\Interfaces;

interface CategoryCrudInterface
{

    public function allIncomeCategories();


    public function allExpenseCategories();


    public function createIncomeCategory(array $data);


    public function createExpenseCategory(array $data);


    public function updateIncomeCategory(array $data, $id);


    public function updateExpenseCategory(array $data, $id);


    public function deleteIncomeCategory($id);


    public function deleteExpenseCategory($id);


    public function findById(string $model, $Id);


    public function search(string $mode,$params);
}
