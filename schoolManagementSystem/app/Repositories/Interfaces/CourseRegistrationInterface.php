<?php

namespace App\Repositories\Interfaces;


interface CourseRegistrationInterface
{
    public function getRegisteredCourses($student_id, $academic_year, $semester, $level_id);
    public function registerCourse($student_id, $academic_year, $semester, $level_id, $course_id);
    public function unregisterCourse($student_id, $academic_year, $semester, $level_id, $course_id);

    public function getRegisteredCourse($student_id, $academic_year, $semester, $level_id, $course_id);

}
