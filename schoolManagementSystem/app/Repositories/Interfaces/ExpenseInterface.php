<?php

namespace App\Repositories\Interfaces;

interface ExpenseInterface
{
    public function all();

    public function findById($id);

    public function create(array $data);

    public function update(array $data, $id);

    public function destroy($id);

    public function getExpensesByDate($startDate, $endDate);

    public function payLecturer($amount, $lecturer, $account, $date);

    public function getByReference($reference);

    public function getByAccount($account);


    public function getFilteredExpenses($filters);
}
