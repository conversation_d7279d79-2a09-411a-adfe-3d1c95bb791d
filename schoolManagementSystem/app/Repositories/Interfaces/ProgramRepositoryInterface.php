<?php

namespace App\Repositories\Interfaces;

interface ProgramRepositoryInterface {
    public function createOrUpdate(array $data);
    public function delete($id);
    public function getById($id);

    public function all();

    public function getBySchool(int $schoolId);

    public function search($input): \Illuminate\Contracts\Pagination\LengthAwarePaginator;

    public function findByNameOrAbbreviation($key);

    public function findByIds($ids);

}
