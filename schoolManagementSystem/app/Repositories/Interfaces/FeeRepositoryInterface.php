<?php

namespace App\Repositories\Interfaces;

use App\Models\Fee;

interface FeeRepositoryInterface
{
    public function createFee(array $data);
    public function updateFee(int $id, array $data);
    public function getFeeById(int $id);

    public function checkFeeStatus(int $student_id, int $academic_year_id);

    public function createTransaction(Fee $fee, $amount);

    public function getFeeByStudent(int $student_id, int $academic_year_id);
    public function getAllFeeByStudent(int $student_id);

    public function getAllIinstallments($student_id, $academic_year_id);
    public function getFeeByStudentAndYearAndReference(int $student_id, int $academic_year_id, int $fee_type_id, string $ref);

    public function getFeeByStudentAndYear(int $student_id, int $academic_year_id, int $fee_type_id);

    public function getAll($query);

    public function addInstallment(int $fee_id, float $amount, string $reference, int $account_id);

    public function getInstallmentById(int $id);

    public function updateFeeStatus(Fee $ree);

    public function getRegistrationFeeByStudentAndYear($student_id, $academic_year_id);

    public function feeStatus($student_id, $academic_year_id);
}
