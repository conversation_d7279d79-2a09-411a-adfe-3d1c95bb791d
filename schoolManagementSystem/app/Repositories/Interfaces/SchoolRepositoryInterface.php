<?php

namespace App\Repositories\Interfaces;

use App\Models\School;

interface SchoolRepositoryInterface
{
    public function all();
    public function getSchoolsByAdminId($admin_id);
    public function findById($id);

    public function findBySlug($slug);

    public function create(array $data);

    public function update(array $data, $id);

    public function delete($id);

    public function findByName($name);

    public function getDepartmentsBySchoolId($school_id);



}
