<?php

namespace App\Repositories\Interfaces;

interface ResultRepositoryInterface
{
    public function getResults($student_id, $academic_year, $semester, $level_id);
    public function getCAResults($student_id, $academic_year, $semester, $level_id);
    public function getExamResults($student_id, $academic_year, $semester, $level_id);
    public function updateCAMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark);
    public function updateExamMark($student_id, $course_id, $academic_year, $semester, $level_id, $mark);

    // New methods
    public function getAllCAMarks($academic_year_id = null, $semester = null, $level_id = null, $perPage = 10);
    public function getCAMarksForStudent($student_id, $academic_year_id = null, $semester = null, $level_id = null);
    public function getAllExamMarks($academic_year_id = null, $semester = null, $level_id = null, $perPage = 10);
    public function getExamMarksForStudent($student_id, $academic_year_id = null, $semester = null, $level_id = null);
    public function getAllResultsBySemesterAndYearAndLevel($semester, $academic_year_id = null, $level_id = null, $perPage = 10);

}
