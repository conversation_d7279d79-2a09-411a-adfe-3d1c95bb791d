<?php

namespace App\Repositories;

use App\Models\Student;
use App\Models\User;
use App\Repositories\Interfaces\StudentRepositoryInterface;

class StudentRepository implements StudentRepositoryInterface
{
    public function all()
    {
        return Student::with('user', 'department', 'level', 'currentLevel')->get();
    }

    public function findById($id)
    {
        return Student::with('academicYear', 'user', 'department', 'level', 'program', 'currentLevel')->find($id);
    }

    public function findByMatricule($matricule)
    {
        return Student::with('user.roles', 'department', 'academicYear', 'level', 'program')->where('matricule', $matricule)->first();
    }

    public function create(array $data)
    {
        return Student::create($data);
    }

    public function updateUserAccount(array $data, $id)
    {
        $student = Student::with('user')->find($id);

        $userData = array_filter($data, function($key) {
            return in_array($key, [
                'first_name',
                'last_name',
                'gender',
                'phone_number',
                'dob',
                'profile_image',
                'updated_at',
            ]);
        }, ARRAY_FILTER_USE_KEY);

        $student->user->update($userData);

        $studentData = array_filter($data, function($key) {
            return in_array($key, [
                'matricule',
                'place_of_birth',
                'region_of_origin',
                'marital_status',
                'date_of_admission',
                'department_id',
                'nationality',
                'program_id'
            ]);
        }, ARRAY_FILTER_USE_KEY);

        if(isset($studentData['matricule'])) {
            $studentData['matricule'] = strtoupper($studentData['matricule']);

            if ($student->matricule === $studentData['matricule']) {
                unset($studentData['matricule']);
            }
        }

        $student->update($studentData);

        return $student;
    }


    public function updateStudent(array $data, $id)
    {
        $student = Student::find($id);
        $student->update($data);
        return $student;
    }

    public function delete($id)
    {
        return Student::where('user_id', $id)->delete();
    }

    public function search($searchQuery)
    {

        return Student::where('matricule', 'like', '%' . $searchQuery . '%')
        ->orWhere('place_of_birth', 'like', '%' . $searchQuery . '%')
        ->orWhere('region_of_origin', 'like', '%' . $searchQuery . '%')
        ->orWhere('marital_status', 'like', '%' . $searchQuery . '%')
        ->orWhere('date_of_admission', 'like', '%' . $searchQuery . '%')
        ->orWhereHas('department', function ($query) use ($searchQuery) {
            $query->where('name', 'like', '%' . $searchQuery . '%');
        })
        ->orWhereHas('level', function ($query) use ($searchQuery) {
            $query->where('name', 'like', '%' . $searchQuery . '%');
        })
        ->orWhere('nationality', 'like', '%' . $searchQuery . '%')
        ->orWhereHas('user', function ($query) use ($searchQuery) {
            $query->where('first_name', 'like', '%' . $searchQuery . '%')

            ->orWhere('last_name', 'like', '%' . $searchQuery . '%')
            ->orWhere('email', 'like', '%' . $searchQuery . '%');
        })
        ->orWhereHas('program', function ($query) use ($searchQuery){
            $query->where('name', 'like', '%'.$searchQuery. '%')
            ->orWhere('abbreviation', 'like', '%' . $searchQuery . '%');
        })
        ->with('user', 'department', 'level')
        ->limit(10)
        ->get();
    }

    public function updateAcaemdicYearAndLevel(array $data)
    {
        $student = Student::find($data['student_id']);
        try{
            $student->update([
                'academic_year_id' => $data['academic_year_id'],
                'level_id' => $data['level_id'],
            ]);

            return $student;
        }catch (\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }



}
