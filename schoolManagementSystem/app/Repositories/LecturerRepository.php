<?php

namespace App\Repositories;

use App\Models\User;
use App\Repositories\Interfaces\LectuerRepositoryInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;

class LecturerRepository implements LectuerRepositoryInterface
{
    protected $cacheTTL = 60 * 1; // 1 min cache expiration
    protected $cacheTag = 'lecturers'; // Cache tag for lecturers

    public function all()
    {
        return $this->cacheRemember('lecturers_all', function () {
            return User::with('lecturerCourses')
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'lecturer');
                })->get();
        });
    }

    public function findById($id)
    {
        $cacheKey = "lecturer_{$id}";

        return $this->cacheRemember($cacheKey, function () use ($id) {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'lecturer');
            })->find($id);
        });
    }

    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            $data['password'] = Hash::make($data['password']);
            $data['profile_image'] = $data['profile'] ?? null;
            $user = User::create($data);

            $user->assignRole('lecturer');

            DB::commit();

            $this->clearCache(); // Clear cache after creating a new lecturer

            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, $id)
    {
        $user = User::find($id);

        if (!$user) {
            throw new \Exception('User not found');
        }

        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Filter out unchanged fields
        $changedData = array_filter($data, function ($value, $key) use ($user) {
            return $user->$key !== $value;
        }, ARRAY_FILTER_USE_BOTH);

        if (count($changedData) > 0) {
            $user->update($changedData);
            $this->clearCache(); // Clear cache after updating the lecturer
        }

        return $user;
    }

    public function delete($id)
    {
        $deleted = User::destroy($id);

        if ($deleted) {
            $this->clearCache(); // Clear cache after deletion
        }

        return $deleted;
    }

    public function search($query)
    {
        $cacheKey = "lecturer_search_{$query}";

        return $this->cacheRemember($cacheKey, function () use ($query) {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'lecturer');
            })->where('name', 'like', '%' . $query . '%')->get();
        });
    }

    public function paginate($perPage = 15, $pageName = 'page', $page = null)
    {
        $cacheKey = "lecturers_paginate_{$perPage}_{$pageName}_{$page}";

        return $this->cacheRemember($cacheKey, function () use ($perPage, $pageName, $page) {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'lecturer');
            })->paginate($perPage, ['*'], $pageName, $page);
        });
    }

    // Utility Method for Cache Management
    protected function clearCache()
    {
        if (Cache::supportsTags()) {
            Cache::tags([$this->cacheTag])->flush();
        } else {
            Cache::flush(); // Fallback if cache tags are not supported
        }
    }

    // Utility method to handle cache with or without tags
    protected function cacheRemember($key, $callback)
    {
        if (Cache::supportsTags()) {
            return Cache::tags([$this->cacheTag])->remember($key, $this->cacheTTL, $callback);
        }

        return Cache::remember($key, $this->cacheTTL, $callback); // Fallback if tags aren't supported
    }
}
