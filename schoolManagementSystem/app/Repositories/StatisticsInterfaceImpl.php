<?php

namespace App\Repositories;

use App\Http\Resources\StudentResource;
use App\Models\Result;
use App\Repositories\Interfaces\StatisticsInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use League\Csv\Writer;

class StatisticsInterfaceImpl implements StatisticsInterface
{
    public function getGeneralStatistics(array $filters): array
    {
        // Query to get the statistics based on filters
        $query = Result::with(['student.department.school', 'course', 'academicYear', 'level', 'student.user'])
            ->when($filters['academic_year_id'] ?? null, function (Builder $query) use ($filters) {
                return $query->where('academic_year_id', $filters['academic_year_id']);
            })
            ->when($filters['level_id'] ?? null, function (Builder $query) use ($filters) {
                return $query->where('level_id', $filters['level_id']);
            })
            ->when($filters['department_id'] ?? null, function (Builder $query) use ($filters) {
                return $query->whereHas('student', function (Builder $query) use ($filters) {
                    $query->where('department_id', $filters['department_id']);
                });
            });

        // Getting statistics
        $results = $query->get();

        return [
            'best_student' => $this->getBestStudent($results),
            'worst_student' => $this->getWorstStudent($results),
            'top_5_students' => $this->getTopStudents($results),
            'top_per_department' => $this->getTopAndWorstPerDepartment($results),
            'top_per_level' => $this->getTopAndWorstPerLevel($results),
            'best_course' => $this->getBestCourse($results),
            'best_department' => $this->getBestDepartment($results),
            'best_level' => $this->getBestLevel($results),
            'best_school' => $this->getBestSchool($results),
        ];
    }

    private function calculateGPA($studentResults)
    {
        // Sum the weighted points and credit values for each course
        $totalWeightedPoints = $studentResults->sum('weighted_point');
        $totalCredits = $studentResults->sum(function ($result) {
            return $result->course->credit_value;
        });

        // Calculate GPA
        return $totalCredits ? $totalWeightedPoints / $totalCredits : 0;
    }

    private function getBestStudent($results)
    {
        return $results->groupBy('student_id')->map(function ($group) {
            return [
                'student' => StudentResource::make($group->first()->student->load('user')),
                'gpa' => $this->calculateGPA($group),
            ];
        })->sortByDesc('gpa')->first();
    }

    private function getWorstStudent($results)
    {
        return $results->groupBy('student_id')->map(function ($group) {
            return [
                'student' => StudentResource::make($group->first()->student->load('user')),
                'gpa' => $this->calculateGPA($group),
            ];
        })->sortBy('gpa')->first();
    }

    private function getTopStudents($results)
    {
        return $results->groupBy('student_id')->map(function ($group) {
            return [
                'student' => StudentResource::make($group->first()->student->load('user')),
                'gpa' => $this->calculateGPA($group),
            ];
        })->sortByDesc('gpa')->take(5);
    }

    private function getTopAndWorstPerDepartment($results)
    {
        return $results->groupBy('student.department_id')->map(function ($group) {
            $studentsGPA = $group->groupBy('student_id')->map(function ($studentResults) {
                return [
                    'student' => $studentResults->first()->student->load('user'),
                    'gpa' => $this->calculateGPA($studentResults),
                ];
            });

            return [
                'top' => $studentsGPA->sortByDesc('gpa')->first(),
                'worst' => $studentsGPA->sortBy('gpa')->first(),
            ];
        });
    }

    private function getTopAndWorstPerLevel($results)
    {
        return $results->groupBy('level_id')->map(function ($group) {
            $studentsGPA = $group->groupBy('student_id')->map(function ($studentResults) {
                return [
                    'student' => $studentResults->first()->student->load('user'),
                    'gpa' => $this->calculateGPA($studentResults),
                ];
            });

            return [
                'top' => $studentsGPA->sortByDesc('gpa')->first(),
                'worst' => $studentsGPA->sortBy('gpa')->first(),
            ];
        });
    }

    private function getBestCourse($results)
    {
        return $results->groupBy('course_id')->map(function ($group) {
            return [
                'average_grade_point' => $group->avg('grade_point'),
                'top_student' => $group->sortByDesc('grade_point')->first(),
            ];
        })->sortByDesc('average_grade_point')->first();
    }

    private function getBestDepartment($results)
    {
        return $results->groupBy('department_id')->map(function ($group) {
            return [
                'average_grade_point' => $group->avg('grade_point'),
                'top_student' => $group->sortByDesc('grade_point')->first(),
            ];
        })->sortByDesc('average_grade_point')->first();
    }

    private function getBestLevel($results)
    {
        return $results->groupBy('level_id')->map(function ($group) {
            return [
                'average_grade_point' => $group->avg('grade_point'),
                'top_student' => $group->sortByDesc('grade_point')->first(),
            ];
        })->sortByDesc('average_grade_point')->first();
    }

    private function getBestSchool($results)
    {
        return $results->groupBy('school_id')->map(function ($group) {
            return [
                'average_grade_point' => $group->avg('grade_point'),
                'top_student' => $group->sortByDesc('grade_point')->first(),
            ];
        })->sortByDesc('average_grade_point')->first();
    }

    public function exportStatisticsToCSV(array $filters): string
    {
        // Get statistics
        $statistics = $this->getGeneralStatistics($filters);

        // Create CSV file
        $csv = Writer::createFromPath('php://temp', 'w+');
        $csv->insertOne(['Metric', 'Description']);

        // Handle best and worst students
        if (isset($statistics['best_student'])) {
            $bestStudent = $statistics['best_student'];
            $departmentName = $bestStudent['student']->department->name ?? 'N/A';
            $schoolName = $bestStudent['student']->department->school->name ?? 'N/A';  // Assuming department belongs to a school
            $csv->insertOne([
                'Best Student',
                "Student: {$bestStudent['student']->matricule}, GPA: {$bestStudent['gpa']}, Department: $departmentName, School: $schoolName"
            ]);
        }

        if (isset($statistics['worst_student'])) {
            $worstStudent = $statistics['worst_student'];
            $departmentName = $worstStudent['student']->department->name ?? 'N/A';
            $schoolName = $worstStudent['student']->department->school->name ?? 'N/A';
            $csv->insertOne([
                'Worst Student',
                "Student: {$worstStudent['student']->matricule}, GPA: {$worstStudent['gpa']}, Department: $departmentName, School: $schoolName"
            ]);
        }

        // Handle top 5 students
        if (isset($statistics['top_5_students'])) {
            $csv->insertOne(['Top 5 Students', '']);
            foreach ($statistics['top_5_students'] as $topStudent) {
                $departmentName = $topStudent['student']->department->name ?? 'N/A';
                $schoolName = $topStudent['student']->department->school->name ?? 'N/A';
                $csv->insertOne([
                    "Student: {$topStudent['student']->matricule}, GPA: {$topStudent['gpa']}, Department: $departmentName, School: $schoolName"
                ]);
            }
        }

        // (Other sections like top/worst per department, level, etc.)

        // Save the CSV to a file
        $filePath = 'statistics_' . date('Ymd_His') . '.csv';
        Storage::disk('public')->put($filePath, $csv->toString());

        // Generate and return the full URL to download the file
        $fullUrl = Storage::disk('public')->url($filePath);

        return $fullUrl;
    }

}
