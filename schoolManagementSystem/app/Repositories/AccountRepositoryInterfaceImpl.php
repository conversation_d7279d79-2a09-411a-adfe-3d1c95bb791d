<?php

namespace App\Repositories;

use App\Models\Account;
use Illuminate\Support\Facades\Cache;
use App\Repositories\Interfaces\AccountRepositoryInterface;

class AccountRepositoryInterfaceImpl implements AccountRepositoryInterface
{
    public function all()
    {
        return Cache::remember('accounts_all', 60, function () {
            return Account::all();
        });
    }

    public function create(array $data)
    {
        $account = Account::create([
            'name' => $data['name'],
        ]);

        // Clear the cache to ensure the next fetch retrieves the updated data
        Cache::forget('accounts_all');

        return $account;
    }

    public function findById($id)
    {
        $cacheKey = "account_{$id}";

        return Cache::remember($cacheKey, 60, function () use ($id) {
            return Account::find($id);
        });
    }

    public function update(array $data, $id)
    {
        $account = $this->findById($id);

        if ($account) {
            $account->update($data);
            // Clear the cache for the specific account
            Cache::forget("account_{$id}");
            // Clear the accounts list cache to ensure it reflects updated data
            Cache::forget('accounts_all');
        }

        return $account;
    }

    public function delete($id)
    {
        $account = $this->findById($id);

        if ($account) {
            $account->delete();
            // Clear the cache for the specific account
            Cache::forget("account_{$id}");
            // Clear the accounts list cache to ensure it reflects updated data
            Cache::forget('accounts_all');
        }

        return true;
    }
}
