<?php

namespace App\Repositories;

use App\Models\Transaction;
use App\Repositories\Interfaces\TransactionInterface;

class TransactionInterfaceImpl implements TransactionInterface
{
    public function getAllTransactions($filters=null)
    {
        return Transaction::query()
            ->when($filters, function ($query) use ($filters) {
                if (isset($filters['student_id'])) {
                    $query->where('student_id', $filters['student_id']);
                }

                if (isset($filters['reference'])) {
                    $query->where('reference', $filters['reference']);
                }

                if (isset($filters['status'])) {
                    $query->where('status', $filters['status']);
                }

                if (isset($filters['date'])) {
                    $query->whereDate('created_at', $filters['date']);
                }

                if (isset($filters['start_date']) && isset($filters['end_date'])) {
                    $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
                }

                return $query;
            })
            // paginate
            ->paginate($filters['per_page'] ?? 10);
    }


    public function getTransactionById($id)
    {
        return Transaction::with('user')->where('id', $id)->first();

    }


    public function getTransactionByReference($reference)
    {
        return Transaction::with( 'user')->where('reference', $reference)->first();
    }

    public function getUserTransactions($user_id)

    {

        return Transaction::with('user')->where('created_by_id', $user_id)
                            ->paginate(10);
    }
}
