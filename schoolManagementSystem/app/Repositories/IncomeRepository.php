<?php

namespace App\Repositories;

use App\Enums\TransactionStatus;
use App\Models\Account;
use App\Models\Income;
use App\Models\Transaction;
use App\Repositories\Interfaces\IncomeInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class IncomeRepository implements IncomeInterface
{
    /**
     * Get all incomes.
     *
     * @return \Illuminate\Database\Eloquent\Collection<int, Income>
     */
    public function all()
    {
        return $this->query()->get();
    }

    /**
     * Find an income by id.
     *
     * @param int $id
     * @return Income|null
     */
    public function findById($id)
    {
        return $this->query()->find($id);
    }

    /**
     * Create a new income.
     *
     * @param array $data
     * @return Income
     */
    public function create(array $data)
    {
        // randome reference

        $ref = $this->getSchoolInitials() . "-" . strtoupper(Str::random(6)) . str_replace('-', '', Carbon::now()->format('Y-m-d'));
        $data['reference'] = $ref;


        $income = Income::create($data);

        $income->refresh();
        if ($income) {

            // Create a new transaction for the income
            Transaction::create([
                'amount' => $data['amount'],
                'transactionable_id' => $income->id,
                'transactionable_type' => Income::class,
                'transaction_type' => 'Income',
                'transaction_date' => now()->format('Y-m-d'),
                'type' => 'income',
                'reference' => $income->reference ?? $data['reference'],
                'user_id' => $data['user_id'] ?? null,
                'created_by_id' => Auth::id(),
                'status' => 'approved'
            ]);
        }

        // increment account amount
        $account = Account::find($data['account_id']);
        $account->increment('balance', $data['amount']);
        return $income;
    }

    /**
     * Update an income and its transaction.
     *
     * @param array $data
     * @param int $id
     * @return Income|null
     */
    public function update(array $data, $id)
    {
        $income = $this->query()->find($id);

        if ($income) {
            $oldAmount = $income->amount;
            $newAmount = $data['amount'];

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Update the income
                $income->update($data);

                // Update the associated transaction if it exists
                $transaction = Transaction::where('transactionable_id', $income->id)
                    ->where('transactionable_type', Income::class)
                    ->first();

                if ($transaction) {
                    $transaction->update([
                        'amount' => $newAmount,
                        'reference' => $income->type,
                        'status' => "approved",
                    ]);
                }

                // Update the account balance
                $account = Account::find($income->account_id);

                if ($account) {
                    $amountDifference = $newAmount - $oldAmount;
                    $account->balance += $amountDifference;
                    $account->save();

                }

                DB::commit();

                $income->load('account', 'transactions');
                return $income;

            } catch (\Exception $e) {
                DB::rollBack();
                // Log the error or handle it as needed
                return null;
            }
        }

        return null;
    }

    /**
     * Delete an income and its transaction by id.
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $income = $this->query()->find($id);

        if ($income) {
            // Delete the associated transaction first
            Transaction::where('transactionable_id', $income->id)
                ->where('transactionable_type', Income::class)
                ->delete();

            // Then delete the income
         $income->delete();

            // Decrement the account balance
            $account = Account::find($income->account_id);
            if ($account) {
                $account->balance -= $income->amount;
                $account->save();
            }

            return true;


        }

        return false;
    }

    /**
     * Search incomes based on parameters.
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection<int, Income>
     */
    public function search($params)
    {
        $query = $this->query();

        if (isset($params['student_id'])) {
            $query->where('student_id', $params['student_id']);
        }

        if (isset($params['account_id'])) {
            $query->where('account_id', $params['account_id']);
        }

        return $query->get();
    }

    /**
     * Base query for Income model with relationships.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function query()
    {
        return Income::with('student', 'account', 'transactions', 'category');
    }

    private function getSchoolInitials()
    {
        return env('SCHOOL_ABBREVIATION');
    }
}
