<?php

namespace App\Repositories;

use App\Models\Fee;
use App\Models\Installment;
use App\Models\Student;
use App\Models\Transaction;
use App\Repositories\Interfaces\FeeRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FeeRepositoryInterfaceImpl implements FeeRepositoryInterface
{
    /**
     * @throws \Exception
     */
    public function createFee(array $data)
    {
        DB::beginTransaction();

        try {
            // Check for duplicate fee
            $this->checkDuplicateFee($data);

            $reference = strtoupper('REF' . uniqid());



            // Create the fee record
            $fee = Fee::create([
                'student_id' => $data['student_id'],
                'academic_year_id' => $data['academic_year_id'],
                'fee_type_id' => $data['fee_type_id'],
                'amount' => $data['amount'],
                'reference' => $reference,
                'bank_ref' => $data['bank_ref'],
                'account_id' => $data['account_id'] ?? null,
                'status' => $data['status'],
                'payment_date' => $data['payment_date'],
                'payment_channel' => $data['payment_channel'],
            ]);

            // Create Transaction
            $this->createTransaction($fee, $data['amount'], $reference);

            // Update fee status based on the payment
            $this->updateFeeStatus($fee);

            DB::commit();
            return $fee;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }


    public function updateFee(int $id, array $data)
    {
        $fee = Fee::findOrFail($id);

        // Check for duplicate fee during update
        $this->checkDuplicateFee($data, $id);

        $fee->update($data);
        return $fee;
    }

    public function getFeeById(int $id)
    {
        return Fee::with('transactions', 'installments', 'student.user', 'student.program', 'student.level', 'student.department.school', 'academicYear', 'account', 'feeType')->find($id);
    }

    public function getAllIinstallments(
        $student_id,
        $academic_year_id)
    {

        return Installment::with('fee', 'student.user', 'fee.student.program', 'student.level', 'student.department.school', 'academicYear', 'account', 'feeType')
        ->whereAll(
            [
                'student_id' => $student_id,
                'academic_year_id' => $academic_year_id
            ]
        )
        ->get();
    }


    public function createTransaction($model, $amount, $ref = null): void
    {
        try {
        DB::beginTransaction();
        $transaction = new Transaction([
            'amount' => $amount,
            'reference' => $ref ?? "TRANS" . uniqid(),
            'created_by_id' => Auth::id(),
            'status' => 'approved',
            'description' => 'Fee payment',
            'transaction_date' => Carbon::now()->format('Y-m-d'),
            'transaction_type' => 'Income'
        ]);

        $transaction->transactionable()->associate($model);
        $transaction->save();
        DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

    }

    public function checkFeeStatus($student_id, $academic_year_id): string
    {
        $student = Student::with('program')->findOrFail($student_id);
        $programCost = $student->program->cost;

        $totalInstallmentAmount = Installment::where('student_id', $student_id)
            ->where('academic_year_id', $academic_year_id)
            ->sum('amount');


        return $totalInstallmentAmount >= $programCost ? 'complete' : 'incomplete';
    }

    public function getAll($params = null)
    {
        return Fee::query()
            ->with('transactions','installments', 'academicYear', 'account', 'student', 'feeType')
            ->when($params, function ($query) use ($params) {
                $query->where('student_id', $params['student_id']);
            })
            ->get();
    }

    public function getFeeByStudent(int $student_id, int $academic_year_id)
    {
        return Fee::where('student_id', $student_id)
            ->where('academic_year_id', $academic_year_id)
            ->get();
    }

    public function getAllFeeByStudent(int $student_id)
    {
        return Fee::with('student', 'academicYear')
            ->where('student_id', $student_id)
            ->get()
            ->groupBy(function ($fee) {
                return $fee->academicYear->name; // Group by academic year name
            });
    }


    public function getFeeByStudentAndYearAndReference(int $student_id, int $academic_year_id, int $fee_type_id, string $ref)
    {
        return Fee::where('student_id', $student_id)
            ->where('academic_year_id', $academic_year_id)
            ->where('fee_type_id', $fee_type_id)
            ->where('bank_ref', $ref)
            ->first();
    }

    public function getFeeByStudentAndYear(int $student_id, int $academic_year_id, int $fee_type_id)
    {
        return Fee::where('student_id', $student_id)
            ->where('academic_year_id', $academic_year_id)
            ->where('fee_type_id', $fee_type_id)
            ->first();
    }

    public function addInstallment(int $fee_id, float $amount, string $reference, $account_id)
    {
        DB::beginTransaction();

        try {
            $fee = Fee::findOrFail($fee_id);

            $installment = Installment::create([
                'fee_id' => $fee_id,
                'amount' => $amount,
                'academic_year_id' => $fee->academic_year_id,
                'student_id' => $fee->student_id,
                'reference' => $reference,
            ]);

            $this->createTransaction($installment, $amount);

            $this->updateFeeStatus($fee);

            DB::commit();
            return $installment;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateFeeStatus(Fee $fee): void
    {
        $student = Student::with('program')->findOrFail($fee->student_id);
        $programCost = $student->program->cost;

        $totalInstallmentAmount = Installment::where('fee_id', $fee->id)->sum('amount');

        $fee->update(['status' => $totalInstallmentAmount >= $programCost ? 'complete' : 'incomplete']);
    }

    private function checkDuplicateFee(array $data, int $excludeId = null)
    {
        $query = Fee::where('student_id', $data['student_id'])
            ->where('academic_year_id', $data['academic_year_id'])
            ->where('fee_type_id', $data['fee_type_id'])
            ->where('bank_ref', $data['bank_ref']);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        $existingFee = $query->first();

        if ($existingFee) {
            throw new \Exception('
            A fee with the same bank reference already exists for this student and academic year. Please choose a different bank reference.
            .');
        }
    }

    public function getRegistrationFeeByStudentAndYear($student_id, $academic_year_id)
    {
        return Fee::where('student_id', $student_id)
            ->where('academic_year_id', $academic_year_id)
            ->where('fee_type_id', 1)
            ->first();
    }

    public function feeStatus($studentId, $academicYearId)
    {
        // Initialize response array with defaults
        $feeStatus = [
            'registration_fee' => false,
            'tuition_fee' => false,
        ];

        // Check if the student has paid the registration fee
        $registrationFee = Fee::where('student_id', $studentId)
            ->where('academic_year_id', $academicYearId)
            ->where('fee_type_id', 1)
            ->exists();

        // Check if the student has paid the tuition fee
        $tuitionFee = Fee::where('student_id', $studentId)
            ->where('academic_year_id', $academicYearId)
            ->where('fee_type_id', 2)
            ->where('status', 'complete')
            ->exists();

        // Update feeStatus array with the actual status
        if ($registrationFee) {
            $feeStatus['registration_fee'] = true;
        }

        if ($tuitionFee) {
            $feeStatus['tuition_fee'] = true;
        }

        return $feeStatus;
    }


    public function getInstallmentById(int $id)
    {
        return Installment::with('fee', 'fee.feeType', 'student.user', 'student.program', 'student.level', 'student.department.school', 'fee.academicYear')->find($id);
    }

}
