# Prepare and export the CSV data as requested by the user

import csv

# Data to be written to CSV
data = [
    ["name", "abbreviation", "description", "duration", "cost"],
    ["Education Science (HND)", "HND_Edu_Sci", "Higher National Diploma in Education Science. Entry Requirements: A/Level, BAC. Registration Fee: 50,000. Tuition Fee: 250,000", 2, 300000],
    ["Education Science (BSc Top-up)", "BSC_Edu_Sci", "Bachelor of Science (1-year top-up) in Education Science. Entry Requirements: HND/HPD. Registration Fee: 50,000. Tuition Fee: 350,000", 1, 400000],
    ["Business & Finance (HND)", "HND_Bus_Fin", "Higher National Diploma in Business & Finance. Entry Requirements: A/Level, BAC. Registration Fee: 50,000. Tuition Fee: 250,000", 2, 300000],
    ["Business & Finance (BSc Top-up)", "BSC_Bus_Fin", "Bachelor of Science (1-year top-up) in Business & Finance. Entry Requirements: HND/HPD. Registration Fee: 50,000. Tuition Fee: 350,000", 1, 400000],
    ["Management & Science (HND)", "HND_Mng_Sci", "Higher National Diploma in Management & Science. Entry Requirements: A/Level, BAC. Registration Fee: 50,000. Tuition Fee: 250,000", 2, 300000],
    ["Management & Science (BSc Top-up)", "BSC_Mng_Sci", "Bachelor of Science (1-year top-up) in Management & Science. Entry Requirements: HND/HPD. Registration Fee: 50,000. Tuition Fee: 350,000", 1, 400000],
    ["Home Economics, Tourism & Hospitality (HND)", "HND_Home_Tour", "Higher National Diploma in Home Economics, Tourism & Hospitality Management. Entry Requirements: A/Level, BAC. Registration Fee: 50,000. Tuition Fee: 250,000", 2, 300000],
    ["Home Economics, Tourism & Hospitality (BSc Top-up)", "BSC_Home_Tour", "Bachelor of Science (1-year top-up) in Home Economics, Tourism & Hospitality Management. Entry Requirements: HND/HPD. Registration Fee: 50,000. Tuition Fee: 350,000", 1, 400000],
    ["Law (HND)", "HND_Law", "Higher National Diploma in Law. Entry Requirements: A/Level, BAC. Registration Fee: 50,000. Tuition Fee: 250,000", 2, 300000],
    ["Law (BSc Top-up)", "BSC_Law", "Bachelor of Science (1-year top-up) in Law. Entry Requirements: HND/HPD. Registration Fee: 50,000. Tuition Fee: 350,000", 1, 400000]
]

# File path for the CSV file
csv_file_path = 'programs_ebenezer.csv'

# Writing to csv file
with open(csv_file_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerows(data)

csv_file_path
