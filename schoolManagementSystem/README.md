# School Management System API

This is the backend API for a School Management System. It provides endpoints for managing users, schools, departments, courses, and more.

## Features

- User authentication (login, logout)
- CRUD operations for schools, departments, courses, and users
- Role-based access control (admin, lecturer, student, etc.)
- Error logging for debugging

## Setup

1. Clone the repository:

    ```bash
    git clone <repository_url>
    ```

2. Install dependencies:

    ```bash
    composer install
    ```

3. Copy the `.env.example` file to `.env` and configure your environment variables, including database connection details and application key:

    ```bash
    cp .env.example .env
    ```

4. Generate an application key:

    ```bash
    php artisan key:generate
    ```

5. Run migrations and seeders to set up the database:

    ```bash
    php artisan migrate --seed
    ```

6. Start the development server:

    ```bash
    php artisan serve
    ```

## API Documentation

API documentation can be found in the `docs` directory.

## Authors

- [Your Name](https://github.com/yourusername)

## License

This project is licensed under the [MIT License](LICENSE).
