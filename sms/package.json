{"name": "sms", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src", "format-check": "prettier --check .", "write": "prettier --write .", "prepare": "husky install", "preview": "vite preview", "test": ""}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.6", "@mui/material": "^5.15.6", "@mui/x-charts": "^7.5.1", "@mui/x-date-pickers": "^6.19.2", "@react-pdf/renderer": "^3.4.1", "@reduxjs/toolkit": "^2.1.0", "apexcharts": "^3.49.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "dayjs": "^1.11.11", "framer-motion": "^12.10.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "react-toastify": "^10.0.5"}, "devDependencies": {"@eslint/compat": "^1.1.0", "@eslint/js": "^9.5.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^9.5.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "globals": "^15.6.0", "husky": "^8.0.0", "lint-staged": "^15.2.7", "postcss": "^8.4.38", "prettier": "^3.3.2", "tailwindcss": "^3.4.3", "typescript-eslint": "^7.13.1", "vite": "^5.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run format-check"}}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}