{"javascript.preferences.importModuleSpecifier": "non-relative", "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "git.enableSmartCommit": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "editor.wordWrap": "on", "workbench.editor.labelFormat": "short", "explorer.compactFolders": false, "editor.guides.bracketPairs": true, "editor.mouseWheelZoom": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.linkedEditing": true, "conventionalCommits.lineBreak": "\\n", "eslint.lintTask.enable": true, "eslint.codeAction.showDocumentation": {"enable": true}, "editor.tokenColorCustomizations": {"textMateRules": [{"name": "italic font", "scope": ["comment", "keyword", "storage", "keyword.control", "keyword.control.from", "keyword.control.flow", "keyword.operator.new", "keyword.control.import", "keyword.control.export", "keyword.control.default", "keyword.control.trycatch", "keyword.control.conditional", "storage.type", "storage.type.class", "storage.modifier.tsx", "storage.type.function", "storage.modifier.async", "variable.language", "variable.language.this", "variable.language.super", "meta.class", "meta.var.expr", "constant.language.null", "support.type.primitive", "entity.name.method.js", "entity.other.attribute-name", "punctuation.definition.comment", "text.html.basic entity.other.attribute-name", "tag.decorator.js entity.name.tag.js", "tag.decorator.js punctuation.definition.tag.js", "source.js constant.other.object.key.js string.unquoted.label.js"], "settings": {"fontStyle": "italic"}}]}, "javascript.updateImportsOnFileMove.enabled": "always", "diffEditor.ignoreTrimWhitespace": false, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.organizeImports": "always", "source.fixAll": "always"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/node_modules": true, "**/.vscode": false}, "git.autofetch": true, "git.ignoreRebaseWarning": true, "git.openRepositoryInParentFolders": "never", "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}