# SMS app

## Installation

- Clone project
- Run `yarn` or `npm install` to install packages

## Setting Up the Docker Environment

### Prerequisites

- **Docker**: Ensure Docker is installed and running on your machine.
- **Docker Compose**: Ensure Docker Compose is installed.

### Setup Scripts

You can use the provided setup scripts for your operating system:

- **For Bash (Linux/MacOS)**: Use `setup.sh`.
- **For Windows PowerShell**: Use `setup.ps1`.

#### Bash Setup

1. **Run the Setup Script**:

   ```bash
   ./setup.sh
   ```

#### PowerShell Setup

1. **Run the Setup Script**:

   ```powershell
   .\setup.ps1
   ```

### Script Details

- **`setup.sh`**: This script will pull the latest Docker image, build the application (if needed), and run the Docker container on Linux or macOS.
- **`setup.ps1`**: This script will pull the latest Docker image, build the application (if needed), and run the Docker container on Windows.
