name: Auto-Request Reviewer

on:
  pull_request_target:
    types: [opened]

jobs:
  add-reviewer:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Request Reviewer
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          PR_NUMBER=$(jq --raw-output .number < "${GITHUB_EVENT_PATH}")
          curl -X POST \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token $GITHUB_TOKEN" \
            https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER/requested_reviewers \
            -d '{"reviewers":["spykelionel"]}'
