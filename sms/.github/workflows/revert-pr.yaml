name: Auto-<PERSON>ert Unauthorized Commits

on:
  push:
    branches:
      - main

permissions:
  contents: write
  pull-requests: write
  workflows: write # Add workflows permission to allow updates to workflow files

jobs:
  check_and_revert:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: skye8-tech/sms
          ssh-strict: true
          persist-credentials: true
          clean: true
          lfs: false
          submodules: false
          set-safe-directory: true

      - name: Check commit author
        id: check_author
        run: |
          AUTHOR=$(git log -1 --pretty=format:'%an <%ae>')
          if [[ "$AUTHOR" != "spykelionel"* && "$AUTHOR" != "spykelion"* ]]; then
            echo "REVERT=true" >> $GITHUB_OUTPUT
          else
            echo "REVERT=false" >> $GITHUB_OUTPUT

      - name: Set up Git
        if: steps.check_author.outputs.REVERT == 'true'
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Revert commit
        if: steps.check_author.outputs.REVERT == 'true'
        run: |
          COMMIT_HASH=$(git rev-parse HEAD)
          BRANCH_NAME="revert-$COMMIT_HASH"
          git checkout -b $BRANCH_NAME
          git revert --no-edit $COMMIT_HASH
          git push -u origin $BRANCH_NAME

      - name: Create Pull Request
        if: steps.check_author.outputs.REVERT == 'true'
        uses: peter-evans/create-pull-request@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "Revert unauthorized commit ${{ github.sha }}"
          title: "Revert unauthorized commit"
          body: |
            This pull request reverts commit ${{ github.sha }} as it was not made by an authorized user (spykelionel or spykelion).

            Please review and merge this revert if appropriate.
          branch: revert-${{ github.sha }}
          base: main
