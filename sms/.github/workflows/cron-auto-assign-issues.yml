name: Auto-Assign Issues to Authors

on:
  schedule:
    - cron: "0 0 * * *" # Runs daily at midnight (UTC)
  workflow_dispatch: # Allows manual triggering of the workflow

jobs:
  auto-assign:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "14"

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Assign issues to their authors
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Get all open issues
          issues=$(curl -s -H "Authorization: token $GITHUB_TOKEN" "https://api.github.com/repos/${{ github.repository }}/issues?state=open&per_page=100")

          # Iterate over each issue and self-assign to the author
          echo "$issues" | jq -c '.[]' | while read issue; do
            issue_number=$(echo "$issue" | jq -r '.number')
            issue_author=$(echo "$issue" | jq -r '.user.login')
            curl -s -X POST \
              -H "Authorization: token $GITHUB_TOKEN" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/repos/${{ github.repository }}/issues/$issue_number/assignees" \
              -d "{\"assignees\":[\"$issue_author\"]}"
          done
