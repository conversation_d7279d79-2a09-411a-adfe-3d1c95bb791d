name: Auto-Assign Issue

on:
  issues:
    types: [opened]

jobs:
  assign-issue:
    runs-on: ubuntu-latest

    steps:
      - name: Assign issue to creator
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          ISSUE_NUMBER=$(jq --raw-output .issue.number < "${GITHUB_EVENT_PATH}")
          ISSUE_CREATOR=$(jq --raw-output .issue.user.login < "${GITHUB_EVENT_PATH}")
          curl -X POST \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token $GITHUB_TOKEN" \
            https://api.github.com/repos/${{ github.repository }}/issues/$ISSUE_NUMBER/assignees \
            -d "{\"assignees\":[\"$ISSUE_CREATOR\"]}"
