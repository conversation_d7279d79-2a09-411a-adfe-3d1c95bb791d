name: Code Formatting Check

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  prettier:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "16"

      - name: Install dependencies
        run: |
          npm install --legacy-peer-deps
        env:
          CI: true

      - name: Check formatting
        run: npx prettier --check .
