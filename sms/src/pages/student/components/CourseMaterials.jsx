import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  TextField,
  MenuItem,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  DownloadOutlined,
  FolderOutlined,
  PictureAsPdfOutlined,
  VideoLibraryOutlined,
  AudiotrackOutlined,
  ImageOutlined,
  LinkOutlined,
  SearchOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useGetRegisteredCoursesQuery } from '@app';

const CourseMaterials = () => {
  const [selectedCourse, setSelectedCourse] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [materialType, setMaterialType] = useState('all');

  const { data: registeredCourses, isLoading: isCoursesLoading } = useGetRegisteredCoursesQuery('registeredCourses');

  // Mock data for materials - replace with actual API call
  const materials = [
    {
      id: 1,
      title: 'Introduction to Programming - Lecture 1',
      type: 'document',
      course: 'Computer Science 101',
      courseId: 1,
      uploadedBy: 'Dr. Smith',
      uploadedAt: '2024-01-15',
      fileSize: '2.5 MB',
      downloadCount: 45,
    },
    {
      id: 2,
      title: 'Database Design Tutorial Video',
      type: 'video',
      course: 'Database Systems',
      courseId: 2,
      uploadedBy: 'Prof. Johnson',
      uploadedAt: '2024-01-14',
      fileSize: '125 MB',
      downloadCount: 32,
    },
    {
      id: 3,
      title: 'Algorithm Analysis Slides',
      type: 'document',
      course: 'Data Structures',
      courseId: 3,
      uploadedBy: 'Dr. Brown',
      uploadedAt: '2024-01-13',
      fileSize: '1.8 MB',
      downloadCount: 28,
    },
  ];

  const getFileIcon = (type) => {
    switch (type) {
      case 'document':
        return <PictureAsPdfOutlined color="error" />;
      case 'video':
        return <VideoLibraryOutlined color="primary" />;
      case 'audio':
        return <AudiotrackOutlined color="secondary" />;
      case 'image':
        return <ImageOutlined color="success" />;
      case 'link':
        return <LinkOutlined color="info" />;
      default:
        return <FolderOutlined />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'document':
        return 'error';
      case 'video':
        return 'primary';
      case 'audio':
        return 'secondary';
      case 'image':
        return 'success';
      case 'link':
        return 'info';
      default:
        return 'default';
    }
  };

  const filteredMaterials = materials.filter(material => {
    const matchesCourse = !selectedCourse || material.courseId.toString() === selectedCourse;
    const matchesSearch = !searchTerm || material.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = materialType === 'all' || material.type === materialType;
    return matchesCourse && matchesSearch && matchesType;
  });

  const handleDownload = (materialId) => {
    // Implement download functionality
    console.log('Downloading material:', materialId);
  };

  if (isCoursesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Course Materials
        </Typography>
        <Typography variant="body1" color="text.secondary" mb={3}>
          Access and download course materials, lecture notes, and resources.
        </Typography>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Search Materials"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchOutlined sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  select
                  label="Filter by Course"
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                >
                  <MenuItem value="">All Courses</MenuItem>
                  {registeredCourses?.data?.map((course) => (
                    <MenuItem key={course.id} value={course.id.toString()}>
                      {course.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  select
                  label="Material Type"
                  value={materialType}
                  onChange={(e) => setMaterialType(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="document">Documents</MenuItem>
                  <MenuItem value="video">Videos</MenuItem>
                  <MenuItem value="audio">Audio</MenuItem>
                  <MenuItem value="image">Images</MenuItem>
                  <MenuItem value="link">Links</MenuItem>
                </TextField>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Materials List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {filteredMaterials.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No materials found matching your criteria.
          </Alert>
        ) : (
          <Card>
            <List>
              {filteredMaterials.map((material, index) => (
                <React.Fragment key={material.id}>
                  <ListItem
                    sx={{
                      py: 2,
                      '&:hover': {
                        backgroundColor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemIcon>
                      {getFileIcon(material.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle1" fontWeight="medium">
                            {material.title}
                          </Typography>
                          <Chip
                            label={material.type}
                            size="small"
                            color={getTypeColor(material.type)}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box mt={1}>
                          <Typography variant="body2" color="text.secondary">
                            Course: {material.course}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Uploaded by {material.uploadedBy} on {material.uploadedAt} • {material.fileSize} • {material.downloadCount} downloads
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleDownload(material.id)}
                        sx={{
                          backgroundColor: 'primary.main',
                          color: 'white',
                          '&:hover': {
                            backgroundColor: 'primary.dark',
                          },
                        }}
                      >
                        <DownloadOutlined />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < filteredMaterials.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Card>
        )}
      </motion.div>
    </Box>
  );
};

export default CourseMaterials;
