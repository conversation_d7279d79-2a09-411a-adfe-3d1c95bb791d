import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  <PERSON>,
  Button,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  QuizOutlined,
  AccessTimeOutlined,
  CheckCircleOutlined,
  PlayArrowOutlined,
  VisibilityOutlined,
  TimerOutlined,
  ScoreOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useGetRegisteredCoursesQuery } from '@app';

const Assessments = () => {
  const [selectedCourse, setSelectedCourse] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [detailsDialog, setDetailsDialog] = useState(false);

  const { data: registeredCourses, isLoading: isCoursesLoading } = useGetRegisteredCoursesQuery('registeredCourses');

  // Mock data for assessments - replace with actual API call
  const assessments = [
    {
      id: 1,
      title: 'Midterm Exam - Programming Fundamentals',
      course: 'Computer Science 101',
      courseId: 1,
      type: 'exam',
      description: 'Comprehensive exam covering programming basics, data types, and control structures.',
      startTime: '2024-02-20 09:00',
      endTime: '2024-02-20 11:00',
      duration: 120,
      totalMarks: 100,
      status: 'upcoming',
      attempts: 0,
      maxAttempts: 1,
      canTake: true,
      lastScore: null,
    },
    {
      id: 2,
      title: 'Quiz 1 - Database Concepts',
      course: 'Database Systems',
      courseId: 2,
      type: 'quiz',
      description: 'Quick quiz on basic database concepts and SQL fundamentals.',
      startTime: '2024-02-15 14:00',
      endTime: '2024-02-15 15:00',
      duration: 30,
      totalMarks: 50,
      status: 'completed',
      attempts: 1,
      maxAttempts: 2,
      canTake: false,
      lastScore: 42,
    },
    {
      id: 3,
      title: 'CA Test - Algorithm Analysis',
      course: 'Data Structures',
      courseId: 3,
      type: 'ca',
      description: 'Continuous assessment test on algorithm complexity and analysis.',
      startTime: '2024-02-18 10:00',
      endTime: '2024-02-18 12:00',
      duration: 90,
      totalMarks: 75,
      status: 'active',
      attempts: 0,
      maxAttempts: 1,
      canTake: true,
      lastScore: null,
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'upcoming':
        return 'info';
      case 'active':
        return 'success';
      case 'completed':
        return 'default';
      case 'expired':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'upcoming':
        return <AccessTimeOutlined />;
      case 'active':
        return <PlayArrowOutlined />;
      case 'completed':
        return <CheckCircleOutlined />;
      default:
        return <QuizOutlined />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'exam':
        return 'error';
      case 'quiz':
        return 'primary';
      case 'ca':
        return 'warning';
      case 'test':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const filteredAssessments = assessments.filter(assessment => {
    const matchesCourse = !selectedCourse || assessment.courseId.toString() === selectedCourse;
    const matchesType = typeFilter === 'all' || assessment.type === typeFilter;
    return matchesCourse && matchesType;
  });

  const handleViewDetails = (assessment) => {
    setSelectedAssessment(assessment);
    setDetailsDialog(true);
  };

  const handleStartAssessment = (assessment) => {
    // Implement assessment start logic
    console.log('Starting assessment:', assessment.id);
  };

  const handleCloseDialog = () => {
    setDetailsDialog(false);
    setSelectedAssessment(null);
  };

  if (isCoursesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Assessments
        </Typography>
        <Typography variant="body1" color="text.secondary" mb={3}>
          Take quizzes, exams, and continuous assessments for your courses.
        </Typography>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Filter by Course"
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                >
                  <MenuItem value="">All Courses</MenuItem>
                  {registeredCourses?.data?.map((course) => (
                    <MenuItem key={course.id} value={course.id.toString()}>
                      {course.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Filter by Type"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="exam">Exams</MenuItem>
                  <MenuItem value="quiz">Quizzes</MenuItem>
                  <MenuItem value="ca">Continuous Assessment</MenuItem>
                  <MenuItem value="test">Tests</MenuItem>
                </TextField>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Assessments List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {filteredAssessments.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No assessments found matching your criteria.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {filteredAssessments.map((assessment) => (
              <Grid item xs={12} key={assessment.id}>
                <Card
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                      <Box flex={1}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="h6" fontWeight="bold">
                            {assessment.title}
                          </Typography>
                          <Chip
                            icon={getStatusIcon(assessment.status)}
                            label={assessment.status}
                            color={getStatusColor(assessment.status)}
                            size="small"
                          />
                          <Chip
                            label={assessment.type.toUpperCase()}
                            color={getTypeColor(assessment.type)}
                            size="small"
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary" mb={1}>
                          Course: {assessment.course}
                        </Typography>
                        <Typography variant="body2" mb={2}>
                          {assessment.description}
                        </Typography>
                      </Box>
                    </Box>

                    <Grid container spacing={2} alignItems="center" mb={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Start Time
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {new Date(assessment.startTime).toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Duration
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {assessment.duration} minutes
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Total Marks
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {assessment.totalMarks} points
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Attempts
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {assessment.attempts}/{assessment.maxAttempts}
                        </Typography>
                      </Grid>
                    </Grid>

                    {assessment.lastScore !== null && (
                      <Box mb={2}>
                        <Alert severity="success" icon={<ScoreOutlined />}>
                          Last Score: {assessment.lastScore}/{assessment.totalMarks} ({Math.round((assessment.lastScore / assessment.totalMarks) * 100)}%)
                        </Alert>
                      </Box>
                    )}

                    <Box display="flex" gap={2} flexWrap="wrap">
                      <Button
                        variant="outlined"
                        startIcon={<VisibilityOutlined />}
                        onClick={() => handleViewDetails(assessment)}
                      >
                        View Details
                      </Button>
                      {assessment.canTake && assessment.status === 'active' && (
                        <Button
                          variant="contained"
                          startIcon={<PlayArrowOutlined />}
                          onClick={() => handleStartAssessment(assessment)}
                          color="success"
                        >
                          Start Assessment
                        </Button>
                      )}
                      {assessment.status === 'upcoming' && (
                        <Button
                          variant="contained"
                          startIcon={<TimerOutlined />}
                          disabled
                        >
                          Not Yet Available
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </motion.div>

      {/* Details Dialog */}
      <Dialog
        open={detailsDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Assessment Details: {selectedAssessment?.title}
        </DialogTitle>
        <DialogContent>
          {selectedAssessment && (
            <Box>
              <Typography variant="body1" mb={2}>
                {selectedAssessment.description}
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Course"
                    secondary={selectedAssessment.course}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Assessment Type"
                    secondary={selectedAssessment.type.toUpperCase()}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Duration"
                    secondary={`${selectedAssessment.duration} minutes`}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Total Marks"
                    secondary={`${selectedAssessment.totalMarks} points`}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Available Time"
                    secondary={`${new Date(selectedAssessment.startTime).toLocaleString()} - ${new Date(selectedAssessment.endTime).toLocaleString()}`}
                  />
                </ListItem>
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
          {selectedAssessment?.canTake && selectedAssessment?.status === 'active' && (
            <Button
              variant="contained"
              onClick={() => handleStartAssessment(selectedAssessment)}
            >
              Start Assessment
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Assessments;
