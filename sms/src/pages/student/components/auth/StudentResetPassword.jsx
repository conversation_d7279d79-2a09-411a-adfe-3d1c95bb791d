import Logo from "@assets/images/logo.jpg";
import Image from "@assets/images/reset-password.svg";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import Modal from "@mui/material/Modal";
import TextField from "@mui/material/TextField";
import { useState } from "react";
import { Link } from "react-router-dom";

function StudentResetPassword() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [open, setOpen] = useState(false);

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const handleConfirmPasswordChange = (event) => {
    setConfirmPassword(event.target.value);
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    if (password === confirmPassword) {
      setOpen(true);
      // Handle password reset logic here
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const modalStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    maxWidth: 400,
    width: "90%",
    bgcolor: "background.paper",
    borderRadius: "1rem",
    boxShadow: "0 20px 25px -5px rgb(0 0 0 / 0.1)",
    p: 4,
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form Section */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6">
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-3">
              Create New Password
            </h2>
            <p className="text-base text-gray-600">
              Make sure your new password is secure and easy to remember
            </p>
          </div>

          {/* Reset Form */}
          <div className="bg-white p-8 rounded-2xl shadow-sm">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-5">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                  </label>
                  <TextField
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={handlePasswordChange}
                    variant="outlined"
                    placeholder="Enter your new password"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={handleTogglePasswordVisibility}
                            edge="end"
                          >
                            {showPassword ? (
                              <VisibilityOffIcon className="text-gray-400" />
                            ) : (
                              <VisibilityIcon className="text-gray-400" />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "12px",
                        "&:hover fieldset": {
                          borderColor: "#22c55e",
                        },
                      },
                    }}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm Password
                  </label>
                  <TextField
                    fullWidth
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    variant="outlined"
                    placeholder="Confirm your new password"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={handleToggleConfirmPasswordVisibility}
                            edge="end"
                          >
                            {showConfirmPassword ? (
                              <VisibilityOffIcon className="text-gray-400" />
                            ) : (
                              <VisibilityIcon className="text-gray-400" />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "12px",
                        "&:hover fieldset": {
                          borderColor: "#22c55e",
                        },
                      },
                    }}
                  />
                </div>
              </div>

              <div className="pt-2">
                <Button
                  fullWidth
                  size="large"
                  variant="contained"
                  type="submit"
                  sx={{
                    bgcolor: "#22c55e",
                    textTransform: "none",
                    fontSize: "1rem",
                    py: 2,
                    borderRadius: "12px",
                    boxShadow: "0 4px 6px -1px rgb(34 197 94 / 0.2)",
                    "&:hover": {
                      bgcolor: "#16a34a",
                      boxShadow: "0 10px 15px -3px rgb(34 197 94 / 0.3)",
                    },
                  }}
                >
                  Reset Password
                </Button>
              </div>

              <div className="text-center">
                <Link
                  to="/student/login"
                  className="inline-flex items-center text-sm font-medium text-green hover:text-green/80 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Login
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Right Side - Image Section */}
      <div className="hidden lg:block w-1/2 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green/60 mix-blend-multiply" />
        <img
          src={Image}
          alt="Reset Password"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex flex-col justify-center p-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-lg">
            <h1 className="text-4xl font-bold text-white mb-6">
              Secure Password Reset
            </h1>
            <p className="text-lg text-white/90">
              Choose a strong password that includes a mix of letters, numbers,
              and symbols to keep your account secure.
            </p>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={modalStyle}>
          <div className="text-center">
            <div className="w-16 h-16 bg-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircleIcon sx={{ fontSize: 40, color: "#22c55e" }} />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Password Reset Successful
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              Your password has been successfully reset. You can now login with
              your new password.
            </p>
            <Link to="/student/login">
              <Button
                fullWidth
                variant="contained"
                sx={{
                  bgcolor: "#22c55e",
                  textTransform: "none",
                  py: 1.5,
                  borderRadius: "12px",
                  "&:hover": {
                    bgcolor: "#16a34a",
                  },
                }}
              >
                Back to Login
              </Button>
            </Link>
          </div>
        </Box>
      </Modal>
    </div>
  );
}

export default StudentResetPassword;
