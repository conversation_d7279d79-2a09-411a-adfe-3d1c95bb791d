import { useStudentLoginMutation } from "@app";
import sideImage from "@assets/images/image.jpg";
import Logo from "@assets/images/logo.jpg";
import { Spinner } from "@components";
import { setUser } from "@features";
import KeyOutlinedIcon from "@mui/icons-material/KeyOutlined";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import { motion } from "framer-motion";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

function StudentLogin() {
  const [matricule, setMatricule] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading }] = useStudentLoginMutation("student login");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleMatriculeChange = (event) => {
    setMatricule(event.target.value);
  };

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleLogin = async (event) => {
    event.preventDefault();
    try {
      const { success, data } = await login({ matricule, password }).unwrap();
      if (success) {
        dispatch(
          setUser({
            user: data?.user ?? data?.student,
            token: data?.token,
            role: data?.role,
            data,
          })
        );
      }
      navigate("/student");
    } catch (error) {
      console.log(error);
      toast.error(
        error?.data?.message ?? "An error occurred when trying to login"
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form Section */}
      <motion.div
        className="flex-1 flex items-center justify-center p-8"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-12">
            <motion.div
              className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </motion.div>
            <motion.h2
              className="text-3xl font-bold text-gray-900 mb-3"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              Welcome Back!
            </motion.h2>
            <motion.p
              className="text-base text-gray-600"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              Sign in to access your student portal
            </motion.p>
          </div>

          {/* Login Form */}
          <motion.div
            className="bg-white p-8 rounded-2xl shadow-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <form className="space-y-6" onSubmit={handleLogin}>
              <div className="space-y-5">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Matricule Number
                  </label>
                  <TextField
                    fullWidth
                    type="text"
                    value={matricule}
                    onChange={handleMatriculeChange}
                    variant="outlined"
                    placeholder="Enter your matricule number"
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <KeyOutlinedIcon className="text-gray-400" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "12px",
                        "&:hover fieldset": {
                          borderColor: "#22c55e",
                        },
                      },
                    }}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <TextField
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={handlePasswordChange}
                    variant="outlined"
                    placeholder="Enter your password"
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={handleTogglePasswordVisibility}
                            edge="end"
                          >
                            {showPassword ? (
                              <VisibilityOffIcon className="text-gray-400" />
                            ) : (
                              <VisibilityIcon className="text-gray-400" />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "12px",
                        "&:hover fieldset": {
                          borderColor: "#22c55e",
                        },
                      },
                    }}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-green focus:ring-green border-gray-300 rounded"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Remember me
                  </label>
                </div>

                <Link
                  to="/student/forgot-password"
                  className="text-sm font-medium text-green hover:text-green/80 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>

              <div className="pt-2">
                <Button
                  fullWidth
                  size="large"
                  variant="contained"
                  type="submit"
                  disabled={isLoading}
                  sx={{
                    bgcolor: "#22c55e",
                    textTransform: "none",
                    fontSize: "1rem",
                    py: 2,
                    borderRadius: "12px",
                    boxShadow: "0 4px 6px -1px rgb(34 197 94 / 0.2)",
                    "&:hover": {
                      bgcolor: "#16a34a",
                      boxShadow: "0 10px 15px -3px rgb(34 197 94 / 0.3)",
                    },
                  }}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Spinner size="small" />
                      <span>Signing in...</span>
                    </div>
                  ) : (
                    "Sign in"
                  )}
                </Button>
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>

      {/* Right Side - Image Section */}
      <motion.div
        className="hidden lg:block w-1/2 relative"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green/60 mix-blend-multiply" />
        <img
          src={sideImage}
          alt="Campus"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex flex-col justify-center p-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-lg">
            <motion.h1
              className="text-4xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              Welcome to EHIST
            </motion.h1>
            <motion.p
              className="text-lg text-white/90"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              Access your student portal to manage your academic journey, view
              results, and stay connected with your education.
            </motion.p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default StudentLogin;
