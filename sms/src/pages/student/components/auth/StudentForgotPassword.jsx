import React from 'react';
import ForgotPassword from '@components/auth/ForgotPassword';
import { useStudentForgotPasswordMutation } from '@app';

function StudentForgotPassword() {
  return (
    <ForgotPassword
      role="student"
      useForgotPasswordMutation={useStudentForgotPasswordMutation}
      title="Forgot Password?"
      subtitle="Enter your email address and we'll send you a link to reset your password."
      loginPath="/student/login"
    />
  );
}

export default StudentForgotPassword;
