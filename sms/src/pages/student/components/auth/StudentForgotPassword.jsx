import Image from "@assets/images/forgot-password.svg";
import Logo from "@assets/images/logo.jpg";
import EmailIcon from "@mui/icons-material/Email";
import Button from "@mui/material/Button";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import { useState } from "react";
import { Link } from "react-router-dom";

function StudentForgotPassword() {
  const [email, setEmail] = useState("");

  const handleEmailChange = (event) => {
    setEmail(event.target.value);
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    // Handle password reset logic here
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form Section */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6">
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-3">
              Forgot Password?
            </h2>
            <p className="text-base text-gray-600">
              Don't worry! We'll help you recover your account
            </p>
          </div>

          {/* Reset Form */}
          <div className="bg-white p-8 rounded-2xl shadow-sm">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <TextField
                  fullWidth
                  type="email"
                  value={email}
                  onChange={handleEmailChange}
                  variant="outlined"
                  placeholder="Enter your email address"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon className="text-gray-400" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "12px",
                      "&:hover fieldset": {
                        borderColor: "#22c55e",
                      },
                    },
                  }}
                />
                <p className="text-sm text-gray-500 mt-2">
                  We'll send you instructions to reset your password
                </p>
              </div>

              <Button
                fullWidth
                size="large"
                variant="contained"
                type="submit"
                sx={{
                  bgcolor: "#22c55e",
                  textTransform: "none",
                  fontSize: "1rem",
                  py: 2,
                  borderRadius: "12px",
                  boxShadow: "0 4px 6px -1px rgb(34 197 94 / 0.2)",
                  "&:hover": {
                    bgcolor: "#16a34a",
                    boxShadow: "0 10px 15px -3px rgb(34 197 94 / 0.3)",
                  },
                }}
              >
                Send Reset Instructions
              </Button>

              <div className="text-center mt-6">
                <Link
                  to="/student/login"
                  className="inline-flex items-center text-sm font-medium text-green hover:text-green/80 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Login
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Right Side - Image Section */}
      <div className="hidden lg:block w-1/2 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green/60 mix-blend-multiply" />
        <img
          src={Image}
          alt="Forgot Password"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex flex-col justify-center p-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-lg">
            <h1 className="text-4xl font-bold text-white mb-6">
              Password Recovery
            </h1>
            <p className="text-lg text-white/90">
              Don't worry about forgetting your password. We've got you covered
              with our secure password recovery process.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StudentForgotPassword;
