import useAuth from "@hooks/useAuth";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import PropTypes from "prop-types"; // Add this import
import { createContext, useContext, useState } from "react";

export const ResultContext = createContext();

export function ResultProvider({ children }) {
  const { user } = useAuth();
  const system = useGetSystemSettings();
  const [filter, setFilter] = useState({
    level: "",
    semester: system?.current_semester,
    academic_year: system?.current_academic_year_id,
  });
  const [query, setQuery] = useState(
    `level_id=${filter.level}&academic_year_id=${filter.academic_year}&student_id=${user?.student?.student_id}&semester=${filter.semester}`
  );

  const value = { query, filter, setFilter, setQuery };
  return (
    <ResultContext.Provider value={value}>{children}</ResultContext.Provider>
  );
}

// Add PropTypes validation
ResultProvider.propTypes = {
  children: PropTypes.node.isRequired, // Add this line
};

export const useResultContext = () => {
  return useContext(ResultContext);
};
