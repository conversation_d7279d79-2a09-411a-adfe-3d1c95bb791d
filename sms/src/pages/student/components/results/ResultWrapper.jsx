import { useGetAllAcademicYearsQuery, useGetAllLevelsQuery } from "@app";
import { Spinner } from "@components";
import { setFilter, setQuery } from "@features/filter/filterSlice";
import useAuth from "@hooks/useAuth";
import { MenuItem, TextField } from "@mui/material";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";

function ResultWrapperComponent({ children }) {
  const dispatch = useDispatch();
  const { filter } = useSelector((state) => state.filter);

  const { data: academicYears } = useGetAllAcademicYearsQuery(
    "getAllAcademicYears"
  );
  const { user } = useAuth();
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("all levels");
  const handleChange = (e) => {
    dispatch(setFilter({ [e.target.name]: e.target.value }));
    dispatch(
      setQuery(
        `level_id=${filter.level}&academic_year_id=${filter.academic_year}&student_id=${user?.student_id}&semester=${filter.semester}`
      )
    );
  };
  return (
    <section className="min-h-screen w-full flex flex-col items-start gap-8 bg-white">
      {/* Filter Bar */}
      <div className="w-full flex flex-col md:flex-row items-center justify-between bg-white shadow-lg p-6 rounded-xl gap-4 border border-gray-100 mb-2">
        <div className="flex flex-row gap-4 w-full md:w-auto justify-between md:justify-start">
          <TextField
            select
            id="academic_year"
            size="small"
            value={filter.academic_year}
            label="Year"
            name="academic_year"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {academicYears?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name?.replace("_", "/")}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            id="semester"
            size="small"
            value={filter.semester}
            label="Semester"
            name="semester"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {[1, 2, 3].map((item) => (
              <MenuItem key={item} value={item}>
                {item}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            id="level"
            size="small"
            value={filter.level}
            label="Level"
            name="level"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {isLevelsLoading ? (
              <Spinner size="14px" />
            ) : (
              levels?.data?.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))
            )}
          </TextField>
        </div>
      </div>
      {/* Header */}
      <div className="w-full flex flex-col items-start gap-2 px-2">
        <p className="text-2xl font-bold text-green tracking-tight">Results</p>
        <div className="w-12 h-1 bg-green rounded-full mb-1" />
      </div>
      <div className="w-full flex flex-col py-4 px-5 items-center justify-between bg-white shadow-sm rounded-md ">
        {children}
      </div>
    </section>
  );
}

ResultWrapperComponent.propTypes = {
  children: PropTypes.node.isRequired,
};

function ResultWrapper({ children }) {
  return (
    // <ResultProvider>
    <ResultWrapperComponent>{children}</ResultWrapperComponent>
    // </ResultProvider>
  );
}

ResultWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ResultWrapper;
