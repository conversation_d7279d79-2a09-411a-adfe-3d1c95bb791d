import { useGetCaResultsQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import useAuth from "@hooks/useAuth";
import {
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";

function CaResults() {
  const menuItems = [
    {
      id: "1",
      tabName: "Ca Results",
      tabLink: "/student/results/ca-results",
    },
    {
      id: "2",
      tabName: "Final Results",
      tabLink: "/student/results/final-results",
    },
  ];

  const theme = createTheme({
    palette: {
      green: {
        main: "#1F7F1F",
      },
    },
  });
  const { filter } = useSelector((state) => state.filter);
  const { user } = useAuth();

  const {
    data: results,
    isLoading: isCaResultsLoading,
    isError,
  } = useGetCaResultsQuery(
    `level_id=${filter.level}&academic_year_id=${filter.academic_year}&student_id=${user?.student_id}&semester=${filter.semester}`
  );

  const { pathname } = useLocation();

  return (
    <section className="w-full flex flex-col items-center justify-between gap-6">
      {/* Tab Navigation */}
      <div className="w-full flex items-center gap-3 py-4">
        {menuItems.map((item) => (
          <Link to={item.tabLink} key={item.id}>
            <span
              className={`px-4 py-2 rounded-full text-md font-semibold transition-all duration-200 cursor-pointer ${
                pathname === item.tabLink
                  ? "bg-green text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-green/10 hover:text-green"
              }`}
            >
              {item.tabName}
            </span>
          </Link>
        ))}
      </div>
      {/* CA Results Table */}
      <div className="w-full flex flex-col items-start justify-between gap-4">
        <p className="text-green font-bold text-lg mb-2">
          Continuous Assessment
        </p>
        <ThemeProvider theme={theme}>
          <div className="flex flex-col w-full my-2">
            <TableContainer
              component={Paper}
              elevation={2}
              className="rounded-xl shadow-md"
            >
              <Table sx={{ maxWidth: "100%", fontFamily: "Inter, sans-serif" }}>
                <TableHead>
                  <TableRow sx={{ background: "#f9fafb" }}>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Course</StyledTableCell>
                    <StyledTableCell>Course Code</StyledTableCell>
                    <StyledTableCell>Credit Value</StyledTableCell>
                    <StyledTableCell>CA Mark</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isCaResultsLoading ? (
                    <Spinner />
                  ) : isError ? (
                    <tr>
                      <td
                        colSpan={5}
                        className="text-center py-6 text-gray-400 text-lg"
                      >
                        Error getting CA marks
                      </td>
                    </tr>
                  ) : results?.data?.length < 1 ? (
                    <tr>
                      <td
                        colSpan={5}
                        className="text-center py-6 text-gray-400 text-lg"
                      >
                        No results for this semester
                      </td>
                    </tr>
                  ) : (
                    results?.data?.map(({ course, ...result }, idx) => (
                      <StyledTableRow
                        key={course.id}
                        className={
                          idx % 2 === 0
                            ? "bg-white transition-all duration-200 hover:bg-green/5"
                            : "bg-gray-50 transition-all duration-200 hover:bg-green/5"
                        }
                      >
                        <StyledTableCell>{idx + 1}</StyledTableCell>
                        <StyledTableCell>
                          {course.name ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {course.code ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {course.credit_value ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {result.score ?? "N/A"}
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </ThemeProvider>
      </div>
    </section>
  );
}

export default CaResults;
