import { summaryDAta } from "@pages/student/components/results/results/results";
import React from "react";

function ResultSummary() {
  return (
    <div className="w-full grid md:grid-cols-4 grid-cols-2  gap-3">
      {summaryDAta.map((card) => (
        <div
          key={card.id}
          className={`${card.color} p-5 rounded-md text-white`}
        >
          <div className="w-full flex items-center justify-center gap-2 mb-3">
            <div className="p-5 bg-white rounded-full"></div>
            <div className="w-full flex flex-col items-start gap-2">
              <p className="text-normal text-sm text-white">{card.title}</p>
              <h2 className="font-semibold text-2xl text-white">
                {card.total}
              </h2>
            </div>
          </div>
          <div className="w-full flex items-center justify-between gap-3">
            <p className="text-white tex-md font-normal">
              {card.subtitle1} :{" "}
              <span className="font-bold text-white text-md">
                {card.value1}
              </span>
            </p>
            <p className="text-white tex-md font-normal">
              {card.subtitle2}:{" "}
              <span className="font-bold text-white text-md">
                {card.value2}
              </span>
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}

export default ResultSummary;
