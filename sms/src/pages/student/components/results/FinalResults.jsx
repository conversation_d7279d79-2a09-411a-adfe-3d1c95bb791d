import {
  useGetAcademicYearByIdQuery,
  useGetDepartmentByIdQuery,
  useGetStudentExamMarksQuery,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import useAuth from "@hooks/useAuth";
import { useDownload } from "@hooks/useDownload";
import { RequestInterceptor } from "@lib/util";
import {
  Button,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";

function FinalResults() {
  const menuItems = [
    {
      id: "1",
      tabName: "Ca Results",
      tabLink: "/student/results/ca-results",
    },
    {
      id: "2",
      tabName: "Final Results",
      tabLink: "/student/results/final-results",
    },
  ];
  const { user } = useAuth();

  const { data: department, isDepartmentLoading } = useGetDepartmentByIdQuery(
    user?.department_id
  );
  const download = useDownload();

  const dispatch = useDispatch();
  const { query, filter } = useSelector((state) => state.filter);
  const {
    data: examMarks,
    isLoading: isExamMarksLoading,
    error: examError,
    isError: isExamError,
    status: examMarksStatus,
    refetch: refetchExamMarks,
    isFetching: isExamMarksFetching,
  } = useGetStudentExamMarksQuery(
    `level_id=${filter.level}&academic_year_id=${filter.academic_year}&student_id=${user?.student_id}&semester=${filter.semester}`
  );
  const { data: academicYear, isLoading: academicYearLoading } =
    useGetAcademicYearByIdQuery(filter?.academic_year);
  const [isResultDownloading, setIsResultDownloading] = useState(
    isExamMarksLoading || isExamMarksFetching
  );
  const { pathname } = useLocation();
  const theme = createTheme({
    palette: {
      green: {
        main: "#1F7F1F",
      },
    },
  });

  const downLoadResult = async () => {
    setIsResultDownloading(true);
    await RequestInterceptor.downloadFile(
      download,
      `results/generate-pdf?level_id=${filter.level}&academic_year_id=${filter.academic_year}&student_id=${user?.student_id}&semester=${filter.semester}`,
      `${user?.matricule}-Semester_${filter?.semester}-results`,
      "application/pdf"
    );
    setIsResultDownloading(false);
  };

  return (
    <section className="w-full flex flex-col items-center justify-between gap-8">
      {/* Tab Navigation */}
      <div className="w-full flex items-center gap-3 py-4">
        {menuItems.map((item) => (
          <Link to={item.tabLink} key={item.id}>
            <span
              className={`px-4 py-2 rounded-full text-md font-semibold transition-all duration-200 cursor-pointer ${
                pathname === item.tabLink
                  ? "bg-green text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-green/10 hover:text-green"
              }`}
            >
              {item.tabName}
            </span>
          </Link>
        ))}
      </div>
      {/* Results Card */}
      <div className="w-full flex flex-col items-center justify-between gap-6">
        <div className="w-full flex flex-col items-center justify-center gap-3">
          <p className="text-green font-bold text-xl mb-2">Semester Results</p>
          <ThemeProvider theme={theme}>
            <div className="flex flex-col w-full my-2">
              <TableContainer
                component={Paper}
                elevation={3}
                className="rounded-2xl shadow-xl"
              >
                <Table
                  sx={{ maxWidth: "100%", fontFamily: "Inter, sans-serif" }}
                >
                  <TableHead>
                    <TableRow sx={{ background: "#f9fafb" }}>
                      <StyledTableCell>S/N</StyledTableCell>
                      <StyledTableCell>Course</StyledTableCell>
                      <StyledTableCell>Course Code</StyledTableCell>
                      <StyledTableCell>Credit Value</StyledTableCell>
                      <StyledTableCell>CA Mark</StyledTableCell>
                      <StyledTableCell>Exam Mark</StyledTableCell>
                      <StyledTableCell>Grade</StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {isResultDownloading ||
                    isExamMarksFetching ||
                    isExamMarksLoading ? (
                      <Spinner />
                    ) : isExamError ? (
                      <tr>
                        <td
                          colSpan={7}
                          className="text-center py-6 text-gray-400 text-lg"
                        >
                          {examError?.data?.message}
                        </td>
                      </tr>
                    ) : examMarks?.data?.results?.length < 1 ? (
                      <tr>
                        <td
                          colSpan={7}
                          className="text-center py-6 text-gray-400 text-lg"
                        >
                          No results for this semester
                        </td>
                      </tr>
                    ) : (
                      examMarks?.data?.results?.map((result, idx) => (
                        <StyledTableRow
                          key={result.id}
                          className={
                            idx % 2 === 0
                              ? "bg-white transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                              : "bg-gray-50 transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                          }
                        >
                          <StyledTableCell>{idx + 1}</StyledTableCell>
                          <StyledTableCell>
                            {result.course?.name ?? "N/A"}
                          </StyledTableCell>
                          <StyledTableCell>
                            {result.course?.code ?? "N/A"}
                          </StyledTableCell>
                          <StyledTableCell>
                            {result.course?.credit_value ?? "N/A"}
                          </StyledTableCell>
                          <StyledTableCell>
                            {result.ca_mark ?? "N/A"}
                          </StyledTableCell>
                          <StyledTableCell>
                            {result.exam_mark ?? "N/A"}
                          </StyledTableCell>
                          <StyledTableCell>
                            {result.grade ?? "N/A"}
                          </StyledTableCell>
                        </StyledTableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </ThemeProvider>
        </div>
        {/* Summary Bar */}
        <div className="w-full flex flex-wrap items-center justify-center gap-6 bg-white rounded-xl shadow p-4 mt-2">
          <div className="flex flex-col items-center">
            <span className="text-gray-500 text-sm">Attempted Credit</span>
            <span className="font-bold text-green text-lg">
              {examMarks?.data?.total_credits}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-gray-500 text-sm">Credit Gotten</span>
            <span className="font-bold text-green text-lg">
              {examMarks?.data?.gotten_points}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-gray-500 text-sm">GPA</span>
            <span className="font-bold text-green text-lg">
              {examMarks?.data?.gpa}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <Button
              variant="contained"
              color="success"
              sx={{
                textTransform: "capitalize",
                borderRadius: 2,
                fontWeight: 600,
                px: 4,
                py: 1.5,
                fontSize: 16,
                boxShadow: "0 2px 8px rgba(34,197,94,0.08)",
              }}
              onClick={downLoadResult}
              disabled={isResultDownloading}
            >
              {isResultDownloading ? <Spinner size="18px" /> : "Download"}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default FinalResults;
