import {
  AccountCircleOutlined,
  AssignmentOutlined,
  AssessmentOutlined,
  Bar<PERSON><PERSON>Outlined,
  BookOutlined,
  FolderOutlined,
  GridViewRounded,
  LogoutOutlined,
  NotificationsOutlined,
  SwapHorizOutlined,
} from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import SchoolIcon from "@mui/icons-material/School";
import {
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { useState } from "react";
import { useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const Sidebar = () => {
  const { role } = useSelector((state) => state.auth);
  const [display, setDisplay] = useState(false);
  const toggle = () => setDisplay(!display);
  const { pathname } = useLocation();

  const studentPaths = [
    {
      tabName: "Dashboard",
      link: `/${role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Courses",
      link: `/${role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Course Materials",
      link: `/${role}/materials`,
      icon: <FolderOutlined />,
    },
    {
      tabName: "Assignments",
      link: `/${role}/assignments`,
      icon: <AssignmentOutlined />,
    },
    {
      tabName: "Assessments",
      link: `/${role}/assessments`,
      icon: <AssessmentOutlined />,
    },
    {
      tabName: "Notifications",
      link: `/${role}/notifications`,
      icon: <NotificationsOutlined />,
    },
    {
      tabName: "Results",
      link: `/${role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Transcript",
      link: `/${role}/transcript`,
      icon: <SchoolIcon />,
    },
    {
      tabName: "Fee Payment",
      link: `/${role}/fee-payment`,
      icon: <SwapHorizOutlined />,
    },
    {
      tabName: "Account",
      link: `/${role}/profile`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Logout",
      link: "", // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  const sideBarItems = studentPaths;

  return (
    <div className="fixed left-0 w-44 bg-white ">
      <>
        <Grid
          className="side w-full items-center"
          justifyContent={"space-between"}
          alignItems={"center"}
        >
          <Grid
            justifyContent={"center"}
            className="py-6 px-11 cursor-pointer"
            placeSelf={"center"}
            item
          >
            <Link to={`/${role}`}>
              <img
                src={logo}
                style={{
                  height: "48px !important",
                  width: "44px !important",
                  color: "#186318",
                  background: "#f3f4f6",
                  border: "1px solid #e5e7eb",
                }}
                className="logo"
              />
            </Link>
            <div
              style={{
                height: "48px",
                width: "44px",
                background: "#f3f4f6",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
              }}
              onClick={toggle}
            >
              <MenuIcon style={{ color: "#186318", fontSize: 32 }} />
            </div>
          </Grid>
          <hr className="text-grey_500" />
          {!display ? (
            <>
              <Grid className="py-5">
                <List>
                  {sideBarItems.map((item) => {
                    return (
                      <Link key={item.tabName} to={item?.link}>
                        <ListItem
                          id="active"
                          className={`hover:bg-[#F0F1F3] hover:border-l-4 my-0 py-0 hover:border-l-[#186318] hover:text-[#186318] w-full mx-1  ${
                            pathname === item.link
                              ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                              : ""
                          }`}
                        >
                          <ListItemIcon className="hover:text-[#186318]">
                            {item?.icon}
                          </ListItemIcon>
                          <ListItemText className="text-[11px]">
                            {item?.tabName}
                          </ListItemText>
                        </ListItem>
                      </Link>
                    );
                  })}
                </List>
              </Grid>
            </>
          ) : (
            <></>
          )}
        </Grid>{" "}
      </>
    </div>
  );
};

export default Sidebar;
