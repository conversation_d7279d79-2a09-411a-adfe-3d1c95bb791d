import LogOutModal from "@components/LogOutModal";
import {
  AccountCircleOutlined,
  AssignmentOutlined,
  AssessmentOutlined,
  BarChartOutlined,
  BookOutlined,
  FolderOutlined,
  GridViewRounded,
  LogoutOutlined,
  NotificationsOutlined,
  SwapHorizOutlined,
} from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import SchoolIcon from "@mui/icons-material/School";
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
  useTheme,
} from "@mui/material";
import AppBar from "@mui/material/AppBar";
import CssBaseline from "@mui/material/CssBaseline";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import Head from "@pages/admin/components/partials/Header";
import PropTypes from "prop-types";
import * as React from "react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const drawerWidth = 280;

export default function DashboardLayout({ children }) {
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);
  const theme = useTheme();

  const handleDrawerClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerToggle = () => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };

  const role = "student";
  const { pathname } = useLocation();
  const [openLogoutModal, setOpenLogoutModal] = useState(false);

  const handleLogout = () => {
    setOpenLogoutModal(true);
  };

  const studentPaths = [
    {
      tabName: "Dashboard",
      link: `/${role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Courses",
      link: `/${role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "📚 Course Materials",
      link: `/${role}/materials`,
      icon: <FolderOutlined />,
      isNew: true,
    },
    {
      tabName: "📝 Assignments",
      link: `/${role}/assignments`,
      icon: <AssignmentOutlined />,
      isNew: true,
    },
    {
      tabName: "🎯 Assessments",
      link: `/${role}/assessments`,
      icon: <AssessmentOutlined />,
      isNew: true,
    },
    {
      tabName: "🔔 Notifications",
      link: `/${role}/notifications`,
      icon: <NotificationsOutlined />,
      isNew: true,
    },
    {
      tabName: "Results",
      link: `/${role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Transcript",
      link: `/${role}/transcript`,
      icon: <SchoolIcon />,
    },
    {
      tabName: "Fee Payment",
      link: `/${role}/fee-payment`,
      icon: <SwapHorizOutlined />,
    },
    {
      tabName: "Account",
      link: `/${role}/profile`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Logout",
      link: "",
      icon: <LogoutOutlined />,
    },
  ];

  const drawer = (
    <div className="h-full flex flex-col bg-white">
      <div className="p-6 flex items-center gap-3 border-b border-gray-100">
        <Link to={`/${role}`} className="flex items-center gap-3">
          <img src={logo} alt="Logo" className="h-10 w-10" />
          <span className="text-green font-semibold text-lg">EHIST</span>
        </Link>
      </div>

      <div className="flex-1 overflow-y-auto">
        <List className="px-4 py-2">
          {studentPaths.map((item) => {
            const isActive = pathname === item.link;
            return (
              <Link
                key={item.tabName}
                to={item?.link}
                onClick={item.tabName === "Logout" ? handleLogout : null}
                className="block"
              >
                <ListItem
                  className={`rounded-lg mb-1 transition-all duration-200 flex items-center gap-3 ${
                    isActive
                      ? "bg-green/10 text-green shadow-sm"
                      : "hover:bg-gray-100 hover:text-green text-gray-600"
                  }`}
                  sx={{
                    minHeight: 48,
                    px: 2.5,
                    cursor: "pointer",
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: 2,
                      color: isActive ? "#16a34a" : "#64748b",
                      fontSize: 28,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "color 0.2s",
                    }}
                  >
                    {React.cloneElement(item.icon, {
                      fontSize: "medium",
                      style: {
                        fontSize: 28,
                        color: isActive ? "#16a34a" : "#64748b",
                        transition: "color 0.2s",
                      },
                    })}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        {item.tabName}
                        {item.isNew && (
                          <span style={{
                            backgroundColor: '#ef4444',
                            color: 'white',
                            fontSize: '10px',
                            padding: '2px 6px',
                            borderRadius: '10px',
                            fontWeight: 'bold'
                          }}>
                            NEW
                          </span>
                        )}
                      </div>
                    }
                    primaryTypographyProps={{
                      fontSize: "1rem",
                      fontWeight: isActive ? 600 : 400,
                      sx: {
                        color: isActive ? "#16a34a" : "#334155",
                        letterSpacing: 0.2,
                        transition: "color 0.2s",
                      },
                    }}
                  />
                </ListItem>
              </Link>
            );
          })}
        </List>
      </div>
    </div>
  );

  return (
    <Box
      sx={{
        display: "flex",
        minHeight: "100vh",
        bgcolor: "background.default",
      }}
    >
      <CssBaseline />
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: "background.paper",
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: "none" } }}
          >
            <MenuIcon style={{ color: "#186318", backgroundColor: "#f3f4f6", borderRadius: "8px", boxShadow: "0 10px 8px rgba(0,0,0,0.16)" }} />
          </IconButton>
          <Head className="text-black" />
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onTransitionEnd={handleDrawerTransitionEnd}
          onClose={handleDrawerClose}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", sm: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              borderRight: "1px solid",
              borderColor: "divider",
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: "100vh",
          bgcolor: "background.default",
        }}
      >
        <Toolbar />
        <div className="max-w-9xl mx-auto">{children}</div>
      </Box>

      {openLogoutModal && (
        <LogOutModal
          openModal={openLogoutModal}
          closeModal={() => setOpenLogoutModal(false)}
        />
      )}
    </Box>
  );
}

DashboardLayout.propTypes = {
  children: PropTypes.node.isRequired,
};
