import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  Button,
  List,
  ListItem,
  ListItemText,
  TextField,
  MenuItem,
  Divider,
  Alert,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  AssignmentOutlined,
  AccessTimeOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  UploadFileOutlined,
  VisibilityOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useGetRegisteredCoursesQuery, useGetMySubmissionsQuery } from '@app';

const Assignments = () => {
  const [selectedCourse, setSelectedCourse] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [submissionDialog, setSubmissionDialog] = useState(false);

  const { data: registeredCourses, isLoading: isCoursesLoading } = useGetRegisteredCoursesQuery('registeredCourses');

  // Mock data for assignments - replace with actual API call
  const assignments = [
    {
      id: 1,
      title: 'Programming Assignment 1: Basic Algorithms',
      course: 'Computer Science 101',
      courseId: 1,
      description: 'Implement basic sorting and searching algorithms in Python.',
      dueDate: '2024-02-15',
      maxScore: 100,
      status: 'pending',
      submitted: false,
      submissionDate: null,
      score: null,
      timeRemaining: '5 days',
      type: 'assignment',
    },
    {
      id: 2,
      title: 'Database Design Project',
      course: 'Database Systems',
      courseId: 2,
      description: 'Design and implement a complete database system for a library management system.',
      dueDate: '2024-02-20',
      maxScore: 150,
      status: 'submitted',
      submitted: true,
      submissionDate: '2024-02-18',
      score: 135,
      timeRemaining: null,
      type: 'project',
    },
    {
      id: 3,
      title: 'Data Structure Analysis',
      course: 'Data Structures',
      courseId: 3,
      description: 'Analyze the time and space complexity of various data structures.',
      dueDate: '2024-01-30',
      maxScore: 80,
      status: 'overdue',
      submitted: false,
      submissionDate: null,
      score: null,
      timeRemaining: 'Overdue',
      type: 'essay',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'submitted':
        return 'success';
      case 'overdue':
        return 'error';
      case 'graded':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <AccessTimeOutlined />;
      case 'submitted':
        return <CheckCircleOutlined />;
      case 'overdue':
        return <WarningOutlined />;
      default:
        return <AssignmentOutlined />;
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesCourse = !selectedCourse || assignment.courseId.toString() === selectedCourse;
    const matchesStatus = statusFilter === 'all' || assignment.status === statusFilter;
    return matchesCourse && matchesStatus;
  });

  const handleViewAssignment = (assignment) => {
    setSelectedAssignment(assignment);
  };

  const handleSubmitAssignment = (assignment) => {
    setSelectedAssignment(assignment);
    setSubmissionDialog(true);
  };

  const handleCloseDialog = () => {
    setSubmissionDialog(false);
    setSelectedAssignment(null);
  };

  if (isCoursesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Assignments
        </Typography>
        <Typography variant="body1" color="text.secondary" mb={3}>
          View and submit your course assignments.
        </Typography>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Filter by Course"
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                >
                  <MenuItem value="">All Courses</MenuItem>
                  {registeredCourses?.data?.map((course) => (
                    <MenuItem key={course.id} value={course.id.toString()}>
                      {course.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Filter by Status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="submitted">Submitted</MenuItem>
                  <MenuItem value="overdue">Overdue</MenuItem>
                  <MenuItem value="graded">Graded</MenuItem>
                </TextField>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </motion.div>

      {/* Assignments List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {filteredAssignments.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No assignments found matching your criteria.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {filteredAssignments.map((assignment) => (
              <Grid item xs={12} key={assignment.id}>
                <Card
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                      <Box flex={1}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="h6" fontWeight="bold">
                            {assignment.title}
                          </Typography>
                          <Chip
                            icon={getStatusIcon(assignment.status)}
                            label={assignment.status}
                            color={getStatusColor(assignment.status)}
                            size="small"
                          />
                          <Chip
                            label={assignment.type}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary" mb={1}>
                          Course: {assignment.course}
                        </Typography>
                        <Typography variant="body2" mb={2}>
                          {assignment.description}
                        </Typography>
                      </Box>
                    </Box>

                    <Grid container spacing={2} alignItems="center" mb={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Due Date
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {assignment.dueDate}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Max Score
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {assignment.maxScore} points
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="caption" color="text.secondary">
                          Time Remaining
                        </Typography>
                        <Typography 
                          variant="body2" 
                          fontWeight="medium"
                          color={assignment.status === 'overdue' ? 'error.main' : 'text.primary'}
                        >
                          {assignment.timeRemaining}
                        </Typography>
                      </Grid>
                      {assignment.submitted && (
                        <Grid item xs={12} sm={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Score
                          </Typography>
                          <Typography variant="body2" fontWeight="medium">
                            {assignment.score ? `${assignment.score}/${assignment.maxScore}` : 'Pending'}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>

                    {assignment.score && (
                      <Box mb={2}>
                        <Typography variant="caption" color="text.secondary">
                          Progress
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(assignment.score / assignment.maxScore) * 100}
                          sx={{ mt: 1, height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    )}

                    <Box display="flex" gap={2} flexWrap="wrap">
                      <Button
                        variant="outlined"
                        startIcon={<VisibilityOutlined />}
                        onClick={() => handleViewAssignment(assignment)}
                      >
                        View Details
                      </Button>
                      {!assignment.submitted && assignment.status !== 'overdue' && (
                        <Button
                          variant="contained"
                          startIcon={<UploadFileOutlined />}
                          onClick={() => handleSubmitAssignment(assignment)}
                        >
                          Submit Assignment
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </motion.div>

      {/* Submission Dialog */}
      <Dialog
        open={submissionDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Submit Assignment: {selectedAssignment?.title}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Upload your assignment files and add any comments.
          </Typography>
          {/* Add file upload component here */}
          <Alert severity="info">
            File upload functionality will be implemented here.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained">Submit</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Assignments;
