import { useGetAllFeesByStudentQuery } from "@app";
import { <PERSON><PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { useDownload } from "@hooks/useDownload";
import { DateWizard } from "@lib/util";
import { Visibility } from "@mui/icons-material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import DownloadIcon from "@mui/icons-material/Download"; // Import DownloadIcon
import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";

import { useState } from "react";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";

function Transactions() {
  const { user: student } = useSelector((state) => state.auth);
  const { data: fees, isLoading: isFeesLoading } = useGetAllFeesByStudentQuery(
    student.student_id,
    "All fees by student"
  );
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);
  const dowload = useDownload();

  const theme = createTheme({
    palette: {
      green: {
        main: "#1F7F1F",
      },
    },
  });

  const handleDownloadReceipt = async (transactionID) => {
    try {
      setIsLoading(true);
      const url = await dowload(
        `fees/get-receipt/${transactionID}`,
        "application/pdf"
      );
      const a = document.createElement("a");
      a.href = url;
      a.download = `${student.matricule}-Receipt`;
      a.click();
      setIsLoading(false);
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.log("Error Downloading date", error);
    }
  };

  return (
    <section className="">
      <div className="w-full flex items-center gap-2">
        <div className="hover:bg-white p-2 rounded-full">
          <ArrowBackIcon onClick={() => navigate("/student/fee-payment")} />
        </div>
        <p className="text-black font-semibold text-md">Transactions</p>
      </div>
      <ThemeProvider theme={theme}>
        <div className="flex flex-col w-full my-2">
          {isFeesLoading ? (
            <Spinner />
          ) : (
            Object.keys(fees?.data).map((year) => (
              <div key={year}>
                <h2 className="text-lg font-bold my-4">
                  Academic Year: {year}
                </h2>
                <TableContainer>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Amount</StyledTableCell>
                        <StyledTableCell>Reference</StyledTableCell>
                        <StyledTableCell>Amount (XAF)</StyledTableCell>
                        <StyledTableCell>Status</StyledTableCell>
                        <StyledTableCell>Date</StyledTableCell>
                        <StyledTableCell>Action</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fees?.data[year]?.map((transaction, index) => (
                        <StyledTableRow key={transaction.id}>
                          <StyledTableCell>{index + 1}</StyledTableCell>
                          <StyledTableCell>
                            {transaction.amount}
                          </StyledTableCell>
                          <StyledTableCell>
                            {transaction.reference}
                          </StyledTableCell>
                          <StyledTableCell>
                            {transaction.amount}
                          </StyledTableCell>
                          <StyledTableCell>
                            <span
                              className={
                                transaction.status === "complete"
                                  ? "text-green"
                                  : "text-red"
                              }
                            >
                              {transaction.status}
                            </span>
                          </StyledTableCell>
                          <StyledTableCell>
                            {DateWizard.toLocaleDate(transaction.created_at)}
                          </StyledTableCell>
                          <StyledTableCell>
                            <div className="flex gap-2">
                              <Link
                                className="text-green font-semibold text-sm hover:cursor-pointer"
                                to={`/student/fee-payment/transactions/${transaction.id}`}
                              >
                                <Visibility />
                              </Link>
                              {isLoading ? (
                                <Spinner size="15px" />
                              ) : (
                                <DownloadIcon
                                  className="hover:cursor-pointer text-green"
                                  onClick={() =>
                                    handleDownloadReceipt(transaction.id)
                                  }
                                />
                              )}
                            </div>
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            ))
          )}
        </div>
      </ThemeProvider>
    </section>
  );
}

export default Transactions;
