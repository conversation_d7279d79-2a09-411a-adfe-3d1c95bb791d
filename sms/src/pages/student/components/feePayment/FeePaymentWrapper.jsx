import { useGetAllAcademicYearsQuery } from "@app";
import { Spinner } from "@components";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { Button, MenuItem, TextField } from "@mui/material";
import { useStudentContext } from "@pages/student/hooks";
import PropTypes from "prop-types"; // Add this import
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

function FeePaymentWrapper({ children }) {
  const [filter, setFilter] = useState({
    school: "",
    dept: "",
    level: "",
    semester: "First Semester",
    year: "2023/2024",
  });
  const { academicYear, setAcademicYear } = useStudentContext();
  const system = useGetSystemSettings();

  console.log(system);

  const handleChange = (e) => {
    console.log(academicYear);
    setFilter((filter) => ({ ...filter, [e.target.name]: e.target.value }));
    setAcademicYear(e.target.value);
  };
  const {
    data: academicYears,
    isLoading: isAcademicYearsLoading,
    status,
  } = useGetAllAcademicYearsQuery("academicYears");
  useEffect(() => {
    if (status === "fulfilled" && academicYears?.data?.length) {
      const defaultYear =
        system?.current_academic_year_id || academicYears.data[0].id;
      setFilter((filter) => ({ ...filter, year: defaultYear }));
      setAcademicYear(defaultYear);
    }
  }, [status, system, academicYears]);

  const navigate = useNavigate();

  return (
    <section className=" max-w-100% top-0 w-full flex flex-col items-start ">
      <div className="w-full flex flex-col items-start justify-between gap-2">
        <div className="w-full flex items-start justify-between">
          <div className="w-full flex flex-col items-start">
            <p className="text-black text-md font-semibold">Pay Fees</p>
            <div className="w-[35px] h-[2px] bg-red"></div>
          </div>
          <div className="w-full flex p-2 justify-end gap-2 items-center">
            <p className="text-grey_300 font-light text-sm">
              {system?.current_semester == 1
                ? "First Semester"
                : "Second Semester"}
            </p>
          </div>
        </div>
        <div className="w-full flex items-center justify-between bg-white shadow-md p-5 rounded-md gap-2">
          <p className="text-black text-sm font-normal">
            Select Academic year:
          </p>
          <div className="w-1/2 flex items-end justify-center gap-3">
            {isAcademicYearsLoading ? (
              <div className="flex gap-2">
                <p className="text-sm italic text-gray-500">
                  Loading Academic Years
                </p>
                <Spinner size="12px" />
              </div>
            ) : (
              <TextField
                select
                id="year"
                size="small"
                value={filter.year}
                label="select year"
                name="year"
                onChange={handleChange}
                fullWidth
              >
                {isAcademicYearsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  academicYears?.data?.map((year) => (
                    <MenuItem value={year.id} key={year.id}>
                      {year.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            )}
          </div>

          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate("/student/fee-payment/transactions")}
          >
            Transaction Details
          </Button>
        </div>
        <div className="w-full bg-white flex items-start justify-between gap-2"></div>
      </div>
      {/* <StudentProvider> */}
      <div className="w-full flex flex-col justify-between">{children}</div>
      {/* </StudentProvider> */}

      <div className="w-full flex items-center justify-center mt-6">
        <p className="text-sm text-black">
          &copy; {new Date().getFullYear()} Copyright:{" "}
          <span className="font-semibold text-green">Skye8</span>
        </p>
      </div>
    </section>
  );
}

// Add prop types validation
FeePaymentWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Add this line
};

export default FeePaymentWrapper;
