import { useGetSingleFeeQuery } from "@app";
import Logo from "@assets/images/logo.jpg";
import { <PERSON><PERSON>, Spinner } from "@components";
import { useDownload } from "@hooks/useDownload";
import { DateWizard, RequestInterceptor } from "@lib/util";
import { Download } from "@mui/icons-material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function TransactionDetails() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data: fee, isLoading: isFeeLoading } = useGetSingleFeeQuery(id);
  const dowload = useDownload();
  const [isLoading, setIsLoading] = useState(false);
  const handleDownloadReceipt = async () => {
    try {
      setIsLoading(true);
      await RequestInterceptor.downloadFile(
        dowload,
        `fees/get-receipt/${id}`,
        `${fee?.data?.student?.matricule}-Receipt`,
        "application/pdf"
      );
      setIsLoading(false);
    } catch (error) {
      console.log("Error Downloading date", error);
    }
  };

  return isFeeLoading ? (
    <Spinner />
  ) : (
    <section className="w-full flex flex-col gap-6">
      <div className="w-full flex items-center gap-2">
        <div className="hover:bg-white p-2 rounded-full">
          <ArrowBackIcon onClick={() => navigate("/student/fee-payment")} />
        </div>
        <p className="text-black font-semibold text-md">Transaction Details</p>
      </div>

      <div className="w-full flex flex-col items-start justify-between gap-4 mb-6">
        <div className="w-full flex items-start justify-between">
          <p className="font-semibold text-black text-sm w-1/5">
            EBENZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
          </p>
          <img src={Logo} alt="" className="w-[50px]" />
          <div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                BAMENDA - SONAC STREET
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                <EMAIL>
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                +237 678332504
              </p>
            </div>
          </div>
        </div>
        <div className="w-full h-[2px] bg-grey_300" />
        <div className="w-full flex items-start justify-between">
          <div className="w-full flex flex-col items-start gap-2">
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">School:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.student?.department?.school?.name ?? "N/A"}
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Department:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.student?.department?.name ?? "N/A"}
              </h2>
            </div>
            <div className="w-full flex items-end gap-3">
              <p className="text-black font-normal text-sm">Name:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.student?.user?.first_name +
                  " " +
                  fee?.data?.student?.user?.last_name}
              </h2>
            </div>
            <div className="w-full flex items-end gap-3">
              <p className="text-black font-normal text-sm">Matricule:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.student?.matricule}
              </h2>
            </div>
          </div>
          <div className="w-full flex flex-col items-start gap-2">
            <div className="w-full flex items-center justify-end gap-3">
              <p className="text-black font-normal text-sm">Level:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.student?.level?.name ?? "N/A"}
              </h2>
            </div>
            <div className="w-full flex items-center justify-end gap-3">
              <p className="text-black font-normal text-sm">Academic Year:</p>
              <h2 className="text-black font-semibold text-md">
                {fee?.data?.academic_year?.name ?? "N/A"}
              </h2>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full flex items-center justify-center">
        <p className="text-green font-normal text-md">Transaction Details</p>
      </div>

      <div className="flex flex-col items-center justify-center w-full px-10 my-2">
        <TableContainer>
          <Table sx={{ maxWidth: "100%" }}>
            <TableHead>
              <TableRow>
                <TableCell></TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell>Payment Motive</TableCell>
                <TableCell>{fee?.data?.fee_type?.name ?? "N/A"}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Amount</TableCell>
                <TableCell>{fee?.data?.amount} XAF</TableCell>
              </TableRow>

              <TableRow>
                <TableCell>Reference</TableCell>
                <TableCell>{fee?.data?.reference ?? "N/A"}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Bank Reference</TableCell>
                <TableCell>{fee?.data?.bank_ref ?? "N/A"}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Payment Channel</TableCell>
                <TableCell>{fee?.data?.payment_channel ?? "N/A"}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Payment Date</TableCell>
                <TableCell>
                  {DateWizard.toLocaleDate(fee?.data?.created_at) ?? "N/A"}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Academic Year</TableCell>
                <TableCell>{fee?.data?.academic_year?.name ?? "N/A"}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Status</TableCell>
                <TableCell>
                  <p
                    className={`uppercase ${
                      fee?.data?.status === "complete"
                        ? "text-green"
                        : "text-red"
                    }`}
                  >
                    {fee?.data?.status ?? "N/A"}
                  </p>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        <div className="my-2">
          {isLoading ? (
            <Spinner />
          ) : (
            <Button
              type="primary"
              onClick={handleDownloadReceipt}
              title="Download Receipt"
              icon={<Download />}
            />
          )}
        </div>
      </div>
    </section>
  );
}

export default TransactionDetails;
