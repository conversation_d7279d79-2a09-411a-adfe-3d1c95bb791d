import React from "react";
import MTNMoMo from "@assets/images/mtnMoney.svg";
import ORANGEMoney from "@assets/images/orangeMoney.svg";

function AvailablePayment() {
  const availablePayment = [
    {
      id: "1",
      paymentMethod: "MTN Mobile Money",
      thumbnail: MTNMoMo,
    },
    {
      id: "2",
      paymentMethod: "Orange Money",
      thumbnail: ORAN<PERSON><PERSON>oney,
    },
  ];
  return (
    <section className="w-full flex flex-col items-center justify-center gap-4 mt-4">
      <p className="font-semibold text-black text-lg">Available Payment</p>
      <div className="w-full flex items-center justify-center gap-3">
        {availablePayment.map((item) => (
          <div
            key={item.id}
            className="flex flex-col items-center justify-center p-4 bg-white shadow-md gap-3 cursor-pointer"
          >
            <img
              src={item.thumbnail}
              alt={item.paymentMethod}
              className="w-40"
            />
            <p className="text-sm font-semibold text-black">
              {item.paymentMethod}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
}

export default AvailablePayment;
