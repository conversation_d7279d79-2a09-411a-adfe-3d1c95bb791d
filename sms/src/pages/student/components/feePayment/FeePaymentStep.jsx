import {
  AvailabePayment,
  PayFee,
  PaymentDetails,
  TransactionDetails,
} from "@pages/student/components/feePayment";
import PropTypes from "prop-types";

function FeePaymentStep({ step }) {
  switch (step) {
    case 0:
      return <PayFee />;
    case 1:
      return <AvailabePayment />;
    case 2:
      return <PaymentDetails />;
    case 3:
      return <TransactionDetails />;
    default:
      throw new Error("Process was not informed. Include in case list.");
  }
}

// Add prop types validation
FeePaymentStep.propTypes = {
  step: PropTypes.number.isRequired,
};

export default FeePaymentStep;
