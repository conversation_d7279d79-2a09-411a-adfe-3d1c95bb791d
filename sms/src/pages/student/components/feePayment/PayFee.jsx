import { useGetFeeStatusMutation } from "@app";
import { Button } from "@components";
import { RequestInterceptor } from "@lib/util";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PaymentIcon from "@mui/icons-material/Payment";
import { useStudentContext } from "@pages/student/hooks";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

function PayFee() {
  const { user: student } = useSelector((state) => state.auth);
  const [studentFeeStatus, setStudentFeeStatus] = useState(null);

  const { academicYear } = useStudentContext();
  const [checkFeeStatus, { isLoading: isCheckingFeestatus }] =
    useGetFeeStatusMutation("fee status");

  useEffect(() => {
    checkFeeStatusonComponentMount();
  }, [academicYear]);

  async function checkFeeStatusonComponentMount() {
    try {
      const data = await RequestInterceptor.handleRequest(
        () =>
          checkFeeStatus({
            academic_year_id: academicYear,
            student_id: student.student_id,
          }),
        { shouldAlert: false },
        PayFee.name
      );
      setStudentFeeStatus(data);
    } catch (error) {
      console.log("Error CHECKing fee: " + error);
    }
  }
  const onSubmit = async (e) => {
    e.preventDefault();
    checkFeeStatusonComponentMount();
  };
  return (
    <section className="min-h-[60vh] w-full flex flex-col items-center justify-center py-8 px-2 bg-gradient-to-br from-green-50 via-white to-blue-50">
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-9xl bg-white/80 backdrop-blur-md rounded-2xl shadow-2xl px-8 py-8 flex flex-col items-center gap-6 border border-gray-100"
      >
        <h2 className="text-2xl font-extrabold text-green text-center mb-1 tracking-tight">
          Fee Payment Status
        </h2>
        <p className="text-gray-600 text-center text-sm mb-2">
          Check your registration and tuition fee status for the current
          academic year.
        </p>
        <div className="w-full flex flex-col gap-4">
          {/* Registration Fee Status */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl shadow-sm border ${studentFeeStatus?.registration_fee ? "bg-green-50 border-green/40" : "bg-red-50/60 border-red/30"} transition-all`}
          >
            {studentFeeStatus?.registration_fee ? (
              <CheckCircleIcon className="text-green" />
            ) : (
              <CancelIcon className="text-red-400" />
            )}
            <div className="flex flex-col">
              <span className="font-bold text-md text-gray-800">
                Registration Fee
              </span>
              <span
                className={`text-sm ${studentFeeStatus?.registration_fee ? "text-green" : "text-red-500"}`}
              >
                {studentFeeStatus?.registration_fee ? "Paid" : "Not Paid"}
              </span>
              <span className="text-xs text-gray-500">
                {studentFeeStatus?.registration_fee
                  ? "You have completed your registration fees."
                  : "You have not completed your registration fees."}
              </span>
            </div>
          </motion.div>
          {/* Tuition Fee Status */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl shadow-sm border ${studentFeeStatus?.tuition_fee ? "bg-green-50 border-green/40" : "bg-orange-50/60 border-orange-300"} transition-all`}
          >
            {studentFeeStatus?.tuition_fee ? (
              <CheckCircleIcon className="text-green" />
            ) : (
              <CancelIcon className="text-orange-400" />
            )}
            <div className="flex flex-col">
              <span className="font-bold text-md text-gray-800">
                Tuition Fee
              </span>
              <span
                className={`text-sm ${studentFeeStatus?.tuition_fee ? "text-green" : "text-orange-500"}`}
              >
                {studentFeeStatus?.tuition_fee ? "Paid" : "Not Paid"}
              </span>
              <span className="text-xs text-gray-500">
                {studentFeeStatus?.tuition_fee
                  ? "You have completed your tuition fees."
                  : "You have not completed your tuition fees."}
              </span>
            </div>
          </motion.div>
        </div>
        <div className="w-full flex flex-col md:flex-row items-center justify-center gap-4 mt-4">
          <Button
            title={isCheckingFeestatus ? "Checking..." : "Check Fee Status"}
            type={"primary"}
            onClick={onSubmit}
            className="w-full md:w-auto px-6 py-2 rounded-lg font-bold shadow hover:bg-green-700 transition-colors duration-200"
            disabled={isCheckingFeestatus}
          />
          <Button
            title={"Make Payment"}
            type={"secondary"}
            onClick={() => {
              /* TODO: implement payment navigation */
            }}
            className="w-full md:w-auto px-6 py-2 rounded-lg font-bold shadow bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
            icon={<PaymentIcon />}
          />
        </div>
      </motion.div>
      {/* Deadline Alert */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
        className="w-full max-w-8xl mt-6 flex items-center justify-center"
      >
        <div className="w-full flex items-center gap-3 bg-gradient-to-r from-orange-100 via-yellow-50 to-red-100 border-l-4 border-orange-400 shadow-md rounded-xl px-6 py-4 animate-pulse">
          <span className="text-2xl text-orange-500">⚠️</span>
          <p className="text-md font-semibold text-orange-700">
            Fee Payment ends on{" "}
            <span className="underline">Friday 23rd, June 2024!</span>
          </p>
        </div>
      </motion.div>
    </section>
  );
}

export default PayFee;
