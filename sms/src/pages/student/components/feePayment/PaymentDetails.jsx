import React, { useState } from "react";
import MTNMoMo from "@assets/images/mtnMoney.svg";
import { feeInstallments } from "@pages/student/components/feePayment/feepament";
import { Button, MenuItem, TextField } from "@mui/material";

function PaymentDetails() {
  const [form, setForm] = useState({
    name: "",
    phoneNumber: "",
    contact: "",
  });

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  return (
    <section className="w-full flex flex-col items-center justify-between gap-5 mt-4">
      <div className="flex flex-col w-full md:px-20 my-2">
        <div className="w-full p-4 flex  flex-col items-center bg-white justify-center border border-grey shadow-sm gap-2">
          <p className="text-sm font-semibold text-red">
            You have not paid your Platform charges for this semester{" "}
          </p>
          <p className="font-normal text-xs text-black">
            Make a payment of 1000 FCFA in order to continue
          </p>
        </div>
      </div>

      <div className="w-full flex md:flex-row flex-col items-start justify-between gap-4 bg-white rounded-sm">
        {/* Input Payment Details Section */}
        <div className="md:w-3/5 w-full flex flex-col items-start p-4 gap-4">
          <p className="text-sm font-semibold text-black">Payment Details</p>
          <div className="w-full flex items-start justify-between">
            <div className="w-full flex gap-2">
              <div className="bg-green h-4 w-4 rounded-full"></div>
              <div className="w-full flex flex-col items-start gap-2">
                <p className="text-green font-semibold text-md">
                  MTN MOBILE MONEY
                </p>
                <p className="text-black font-normal text-xs">
                  You selected this as your Payment method
                </p>
              </div>
            </div>
            <div className="w-full flex justify-end">
              <img src={MTNMoMo} alt="Mtn mobile money logo" className="w-20" />
            </div>
          </div>
          <div className="w-full bg-grey h-0.5"></div>

          {/* Payment Details form */}
          <form className="w-full flex flex-col items-center justify-between gap-3">
            <TextField
              placeholder="Enter your momo name"
              onChange={onChange}
              type="text"
              name="name"
              id="name"
              label=" Name"
              value={form.name}
              fullWidth
              required
            />
            <TextField
              onChange={onChange}
              type="contact"
              name="phoneNumber"
              id="phoneNumber"
              label="Phone Number"
              value={form.courseCode}
              fullWidth
              required
            />

            <TextField
              onChange={onChange}
              name="amount"
              id="amount"
              label="Amount"
              value={form.amount}
              fullWidth
              required
              select
            >
              {feeInstallments.map((option) => (
                <MenuItem key={option.id} value={option.amount}>
                  {option.amount}
                </MenuItem>
              ))}
            </TextField>
            <Button
              fullWidth
              variant="contained"
              color="success"
              style={{ textTransform: "capitalize" }}
            >
              Pay Charges
            </Button>
          </form>
        </div>

        {/* Available Installments */}
        <div className="md:w-2/5 w-full flex flex-col items-center justify-center md:py-10 py-4 px-6 bg-greenDark text-white rounded-md gap-6">
          <div className="w-full flex flex-col gap-2">
            <p className="text-md font-semibold text-center">
              Available Installments
            </p>
            <p className="text-xs font-normal text-center">
              Below is an outline of various installments that can be paid for
              this program
            </p>
          </div>
          <div className="w-full grid grid-cols-2 gap-4">
            <div className="w-full flex flex-col text-center gap-4">
              <p className="p-1 bg-white text-md text-green text-center">
                Amount
              </p>
              <ul className="w-full flex flex-col gap-4">
                {feeInstallments.map((option) => (
                  <li key={option.id} className="text-sm font-normal">
                    {option.amount}
                  </li>
                ))}
              </ul>
              <p className="text-sm font-normal">Total Amount</p>
            </div>
            <div className="w-full flex flex-col text-center gap-4">
              <p className="p-1 bg-white text-md text-green text-center">
                Due Date
              </p>
              <ul className="w-full flex flex-col gap-4">
                {feeInstallments.map((option) => (
                  <li key={option.id} className="text-sm font-semibold">
                    {option.dueDate}
                  </li>
                ))}
              </ul>
              <p className="text-sm font-semibold">450,000 fCFA</p>
            </div>
          </div>
          <div className="w-full flex items-center justify-center">
            <p className="text-xs font-normal ">
              Platform charges per installment:{" "}
              <span className="text-md font-semibold text-white">
                1000 FCFA
              </span>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default PaymentDetails;
