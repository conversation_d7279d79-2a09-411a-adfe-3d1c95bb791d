import {
  FeePaymentStep,
  FeePaymentWrapper,
} from "@pages/student/components/feePayment";
import { useStudentContext } from "@pages/student/hooks";
import { Outlet } from "react-router-dom";

function MiniWrapper() {
  const { feePaymentStep } = useStudentContext();
  return <FeePaymentStep step={feePaymentStep} />;
}

function FeePayment() {
  return (
    <>
      <FeePaymentWrapper>
        <Outlet>
          <MiniWrapper />
        </Outlet>
      </FeePaymentWrapper>
    </>
  );
}

export default FeePayment;
