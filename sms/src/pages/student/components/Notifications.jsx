import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  TextField,
  MenuItem,
  Divider,
  Alert,
  CircularProgress,
  Badge,
} from '@mui/material';
import {
  NotificationsOutlined,
  AssignmentOutlined,
  GradeOutlined,
  AnnouncementOutlined,
  InfoOutlined,
  MarkEmailReadOutlined,
  DeleteOutlined,
  FilterListOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useGetNotificationsQuery, useGetUnreadNotificationCountQuery, useMarkNotificationAsReadMutation, useMarkAllNotificationsAsReadMutation, useDeleteNotificationMutation } from '@app';

const Notifications = () => {
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Get notifications from API
  const { data: notificationsData, isLoading: isNotificationsLoading } = useGetNotificationsQuery({
    type: typeFilter !== 'all' ? typeFilter : undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
  });

  const { data: unreadCountData } = useGetUnreadNotificationCountQuery();

  // Use real data from API or fallback to empty array
  const notifications = notificationsData?.data || [];

  // Mock data for demonstration (remove when API is working)
  const mockNotifications = [
    {
      id: 1,
      type: 'assignment',
      title: 'New Assignment Posted',
      message: 'Programming Assignment 1: Basic Algorithms has been posted for Computer Science 101.',
      isRead: false,
      priority: 'medium',
      createdAt: '2024-01-19 10:30',
      actionUrl: '/student/assignments',
    },
    {
      id: 2,
      type: 'grade',
      title: 'Grade Released',
      message: 'Your grade for Database Design Project has been released. Score: 135/150',
      isRead: false,
      priority: 'high',
      createdAt: '2024-01-19 09:15',
      actionUrl: '/student/results',
    },
    {
      id: 3,
      type: 'announcement',
      title: 'System Maintenance Notice',
      message: 'The learning management system will undergo maintenance on January 25th from 2:00 AM to 4:00 AM.',
      isRead: true,
      priority: 'low',
      createdAt: '2024-01-18 16:45',
      actionUrl: null,
    },
    {
      id: 4,
      type: 'assessment',
      title: 'Quiz Available',
      message: 'Quiz 1 - Database Concepts is now available. Due: February 15th, 3:00 PM',
      isRead: false,
      priority: 'high',
      createdAt: '2024-01-18 14:20',
      actionUrl: '/student/assessments',
    },
    {
      id: 5,
      type: 'material',
      title: 'New Course Material',
      message: 'New lecture slides for Algorithm Analysis have been uploaded to Data Structures course.',
      isRead: true,
      priority: 'low',
      createdAt: '2024-01-17 11:30',
      actionUrl: '/student/materials',
    },
  ];

  // Use real notifications if available, otherwise use mock data for demonstration
  const displayNotifications = notifications.length > 0 ? notifications : mockNotifications;

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'assignment':
        return <AssignmentOutlined />;
      case 'grade':
        return <GradeOutlined />;
      case 'announcement':
        return <AnnouncementOutlined />;
      case 'assessment':
        return <NotificationsOutlined />;
      case 'material':
        return <InfoOutlined />;
      default:
        return <NotificationsOutlined />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'assignment':
        return 'primary';
      case 'grade':
        return 'success';
      case 'announcement':
        return 'info';
      case 'assessment':
        return 'warning';
      case 'material':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const filteredNotifications = displayNotifications.filter(notification => {
    const matchesType = typeFilter === 'all' || notification.type === typeFilter;
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'unread' && !notification.isRead) ||
      (statusFilter === 'read' && notification.isRead);
    return matchesType && matchesStatus;
  });

  const unreadCount = unreadCountData?.count || displayNotifications.filter(n => !n.isRead).length;

  // Mutation hooks for notification actions
  const [markAsRead] = useMarkNotificationAsReadMutation();
  const [deleteNotification] = useDeleteNotificationMutation();
  const [markAllAsRead] = useMarkAllNotificationsAsReadMutation();

  const handleMarkAsRead = async (notificationId) => {
    try {
      await markAsRead(notificationId).unwrap();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleDelete = async (notificationId) => {
    try {
      await deleteNotification(notificationId).unwrap();
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead().unwrap();
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    }
  };

  const handleNotificationClick = (notification) => {
    if (notification.actionUrl) {
      // Navigate to the action URL
      console.log('Navigating to:', notification.actionUrl);
    }
    if (!notification.isRead) {
      handleMarkAsRead(notification.id);
    }
  };

  if (isNotificationsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading notifications...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" alignItems="center" justifyContent="between" mb={3}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Notifications
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Stay updated with your course activities and announcements.
            </Typography>
          </Box>
          <Badge badgeContent={unreadCount} color="error">
            <NotificationsOutlined sx={{ fontSize: 32 }} />
          </Badge>
        </Box>
      </motion.div>

      {/* Filters and Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="between" flexWrap="wrap" gap={2}>
              <Box display="flex" gap={2} flexWrap="wrap">
                <TextField
                  select
                  label="Filter by Type"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  size="small"
                  sx={{ minWidth: 150 }}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="assignment">Assignments</MenuItem>
                  <MenuItem value="grade">Grades</MenuItem>
                  <MenuItem value="announcement">Announcements</MenuItem>
                  <MenuItem value="assessment">Assessments</MenuItem>
                  <MenuItem value="material">Materials</MenuItem>
                </TextField>
                <TextField
                  select
                  label="Filter by Status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  size="small"
                  sx={{ minWidth: 150 }}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="unread">Unread</MenuItem>
                  <MenuItem value="read">Read</MenuItem>
                </TextField>
              </Box>
              <Button
                variant="outlined"
                startIcon={<MarkEmailReadOutlined />}
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
              >
                Mark All as Read
              </Button>
            </Box>
          </CardContent>
        </Card>
      </motion.div>

      {/* Notifications List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {filteredNotifications.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No notifications found matching your criteria.
          </Alert>
        ) : (
          <Card>
            <List>
              {filteredNotifications.map((notification, index) => (
                <React.Fragment key={notification.id}>
                  <ListItem
                    sx={{
                      py: 2,
                      backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: 'action.selected',
                      },
                    }}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <ListItemIcon>
                      <Badge
                        variant="dot"
                        color="error"
                        invisible={notification.isRead}
                      >
                        {getNotificationIcon(notification.type)}
                      </Badge>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography
                            variant="subtitle1"
                            fontWeight={notification.isRead ? 'normal' : 'bold'}
                          >
                            {notification.title}
                          </Typography>
                          <Chip
                            label={notification.type}
                            size="small"
                            color={getTypeColor(notification.type)}
                            variant="outlined"
                          />
                          <Chip
                            label={notification.priority}
                            size="small"
                            color={getPriorityColor(notification.priority)}
                            variant="filled"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              fontWeight: notification.isRead ? 'normal' : 'medium',
                            }}
                          >
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" mt={1}>
                            {new Date(notification.createdAt).toLocaleString()}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box display="flex" gap={1}>
                        {!notification.isRead && (
                          <IconButton
                            edge="end"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMarkAsRead(notification.id);
                            }}
                            size="small"
                          >
                            <MarkEmailReadOutlined />
                          </IconButton>
                        )}
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(notification.id);
                          }}
                          size="small"
                          color="error"
                        >
                          <DeleteOutlined />
                        </IconButton>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < filteredNotifications.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Card>
        )}
      </motion.div>
    </Box>
  );
};

export default Notifications;
