import {
  BorderColorRounded,
  FmdGoodOutlined,
  InsertPhotoOutlined,
  LogoutRounded,
  MailOutlineRounded,
  PersonRounded,
  PhoneInTalkRounded,
  VpnKeyOutlined,
} from "@mui/icons-material";
import { Button, capitalize } from "@mui/material";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";

function Profile() {
  const {
    user: { userInfo, program, ...user },
  } = useSelector((state) => state.auth);
  return (
    <div className="bg-white w-full flex flex-col lg:flex-row justify-center md:items-start items-center gap-4">
      <div className="m-5 w-full py-5 px-5 rounded-lg border border-slate-200">
        <div className="flex w-[80%] justify-center items-center">
          <img
            src={
              userInfo?.profile_image ??
              "https://aui.atlassian.com/aui/9.3/docs/images/avatar-person.svg"
            }
            alt=""
          />
          <div className="bg-[#E9F4E9] cursor-not-allowed absolute w-[30px] mt-[180px] ml-40 h-[30px] rounded-[50%] flex justify-center items-center">
            <InsertPhotoOutlined className="text-green" />
          </div>
        </div>
        <div className="w-full mt-5">
          <h3 className="font-semibold">Contact Information</h3>
          <div className="mt-5 flex gap-9">
            <h3 className="font-semibold">
              <MailOutlineRounded /> Email Address:
            </h3>
            <p>{userInfo?.email}</p>
          </div>
          <div className="mt-5 flex gap-6">
            <h3 className="font-semibold">
              <PhoneInTalkRounded /> Phone Number:
            </h3>
            <p>{userInfo?.phone_number ?? "N/A"}</p>
          </div>
          <div className="my-5 flex gap-20">
            <h3 className="font-semibold">
              <FmdGoodOutlined /> Address:
            </h3>
            <p>{userInfo?.address ?? "N/A"}</p>
          </div>
          <Link to={"/student/profile/edit-profile"}>
            <Button
              variant="contained"
              className="w-[392px]"
              color="success"
              style={{ textTransform: capitalize, gap: "4px" }}
            >
              <BorderColorRounded />
              Edit Profile
            </Button>
          </Link>
        </div>
        <div className="my-5">
          <h3 className=" text-red font-semibold">Settings</h3>
          <div className="my-5 flex gap-4">
            <div className="bg-[#E9F4E9] w-[30px] h-[30px] flex justify-center items-center rounded-[50%]">
              <VpnKeyOutlined className="text-green" />
            </div>
            <p>Change Password</p>
          </div>
          <div className="my-5 flex gap-4">
            <div className="bg-[#E9F4E9] w-[30px] h-[30px] flex justify-center items-center rounded-[50%]">
              <LogoutRounded className="text-green" />
            </div>
            <p>Sign Out</p>
          </div>
        </div>
      </div>
      <div className="m-5 w-full border border-slate-300 rounded-lg p-5 w-[600px]">
        <h3 className="text-2xl font-semibold">
          <PersonRounded /> Basic Information
        </h3>
        <div className="m-5 flex gap-[205px]">
          <h3 className="font-semibold">Full Name:</h3>
          <p>
            {userInfo?.first_name} {userInfo?.last_name}
          </p>
        </div>
        <div className="m-5 flex gap-40">
          <h3 className="font-semibold">Degree Program:</h3>
          <p>
            {program?.name ?? "N/A"} ({program?.abbreviation})
          </p>
        </div>
        <div className="m-5 flex gap-[211px]">
          <h3 className="font-semibold">Matricule:</h3>
          <p>{user?.matricule ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[165px]">
          <h3 className="font-semibold">Admission Date:</h3>
          <p>{user?.date_of_admission ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[226px]">
          <h3 className="font-semibold">Gender:</h3>
          <p>{userInfo?.gender ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[183px]">
          <h3 className="font-semibold">Date Of Birth:</h3>
          <p>{userInfo?.dob ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[157px]">
          <h3 className="font-semibold">Region Of Origin:</h3>
          <p>{user?.region_of_origin ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[178px]">
          <h3 className="font-semibold">Marital Status:</h3>
          <p>{user?.marital_status ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[196px]">
          <h3 className="font-semibold">Nationality:</h3>
          <p>{user?.nationality ?? "N/A"}</p>
        </div>
        <div className="m-5 flex gap-[183px]">
          <h3 className="font-semibold">NID Number:</h3>
          <p>{user?.nid ?? "N/A"}</p>
        </div>
        <h3 className="m-5 text-2xl font-semibold">
          Guardian&apos;s Information
        </h3>
        <div className="m-5 flex gap-[232px]">
          <h3 className="font-semibold">Name:</h3>
          <p>N/A</p>
        </div>
        <div className="m-5 flex gap-[243px]">
          <h3 className="font-semibold">Role:</h3>
          <p>N/A</p>
        </div>
        <div className="m-5 flex gap-[165px]">
          <h3 className="font-semibold">Phone Number:</h3>
          <p>N/A</p>
        </div>
        <div className="m-5 flex gap-[217px]">
          <h3 className="font-semibold">Address:</h3>
          <p>N/A</p>
        </div>
      </div>
    </div>
  );
}

export default Profile;
