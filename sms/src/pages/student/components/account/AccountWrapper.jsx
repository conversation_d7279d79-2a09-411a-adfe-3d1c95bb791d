import PropTypes from "prop-types"; // Import PropTypes

function AccountWrapper({ children }) {
  return (
    <section className="min-h-screen w-full flex flex-col items-start">
      <div className="w-full items-start mb-3">
        <p className="text-black text-md font-semibold">Profile</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>

      {children}
    </section>
  );
}

// Add propTypes for validation
AccountWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Validate 'children' prop
};

export default AccountWrapper;
