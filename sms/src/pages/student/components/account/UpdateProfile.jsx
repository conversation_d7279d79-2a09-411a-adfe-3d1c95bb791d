import React from "react";
import student_image from "@assets/images/Student.png";
import { FileUploadOutlined, InsertPhotoOutlined } from "@mui/icons-material";
import { Button, capitalize } from "@mui/material";

function UpdateProfile() {
  return (
    <div className="bg-white w-[83vw]">
      <div className="w-[578px] m-5 p-5">
        <div className="w-full rounded-2xl p-5 flex flex-col justify-center items-center bg-[#F0F1F3]">
          <img src={student_image} alt="" />
          <div className="bg-[#E9F4E9] absolute w-[30px] mt-[150px] ml-40 h-[30px] rounded-[50%] flex justify-center items-center">
            <InsertPhotoOutlined className="text-green" />
          </div>
          <h1 className="mt-2 font-bold text-2xl"><PERSON><PERSON><PERSON> Fiona</h1>
        </div>
        <form className="mt-5" action="" method="post">
          <div className="mt-4">
            <label htmlFor="">Email Address</label>
            <div className="mt-2">
              <input
                type="text"
                className="w-full h-16 border outline-none px-5 rounded-xl border-slate-300"
                name=""
                id=""
              />
            </div>
          </div>
          <div className="mt-4">
            <label htmlFor="">Phone Number</label>
            <div className="mt-2">
              <input
                type="text"
                className="w-full h-16 border outline-none px-5 rounded-xl border-slate-300"
                name=""
                id=""
              />
            </div>
          </div>
          <div className="my-4">
            <label htmlFor="">Address</label>
            <div className="mt-2">
              <input
                type="text"
                className="w-full h-16 border outline-none px-5 rounded-xl border-slate-300"
                name=""
                id=""
              />
            </div>
          </div>
          <Button
            type="submit"
            variant="contained"
            className="w-full mt-2 h-14 rounded-lg"
            color="success"
            style={{ textTransform: capitalize }}
          >
            <FileUploadOutlined /> Update Profile
          </Button>
        </form>
      </div>
    </div>
  );
}

export default UpdateProfile;
