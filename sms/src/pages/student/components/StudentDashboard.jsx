import {
  useGetAllAcademicYearsQuery,
  useGetRegisteredCoursesQuery,
} from "@app";
import Illustration from "@assets/images/student-Dashboard.svg";
import { Spinner } from "@components";
import { logout } from "@features";
import { setFilter } from "@features/filter/filterSlice";
import { setSystemSettings } from "@features/system/systemSlice";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { stringAvatar } from "@lib/util";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import TodayOutlinedIcon from "@mui/icons-material/TodayOutlined";
import WatchLaterOutlinedIcon from "@mui/icons-material/WatchLaterOutlined";
import {
  Avatar,
  Button,
  Card,
  Checkbox,
  FormControlLabel,
  Grid,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import Fade from "@mui/material/Fade";
import { DateCalendar, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import useGetCalendarEvents from "@pages/student/hooks/useGetCalendarEvents";
import useGetFeeStatus from "@pages/student/hooks/useGetFeeStatus";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const StudentDashboard = () => {
  const system = useGetSystemSettings();
  const [anchorEl, setAnchorEl] = useState(null);
  const { user, data: student, role } = useSelector((state) => state.auth);
  const [visibleEvents, setVisibleEvents] = useState(2);
  const [query] = useState(
    `student_id=${user?.student_id}&academic_year_id=${user?.student?.academic_year?.id ?? 2}&level_id=${user?.student?.level ?? 2}&semester=${user?.student?.semester ?? 1}`
  );

  const { data: registeredCourses, isLoading: isRegisteredCoursesLoading } =
    useGetRegisteredCoursesQuery(query, "registered courses");

  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("get all academic years");

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const dispatch = useDispatch();
  const handleYearSelect = (year) => {
    // Update system settings with the selected year
    dispatch(
      setSystemSettings({ ...system, current_academic_year_name: year })
    );
    dispatch(setFilter({ ...system, academic_year: year }));
    handleClose();
  };

  const navigate = useNavigate();
  useEffect(() => {
    if (!user) {
      navigate("/student/login", { replace: true });
    } else if (role === "admin") {
      dispatch(logout());
      navigate("/admin", { replace: true });
    }
  }, [user, role]);

  const { isCheckingFeestatus, studentFeeStatus } = useGetFeeStatus();
  const hasPayedRegistrationFee = studentFeeStatus?.registration_fee;
  const hasPayedTutionFee = studentFeeStatus?.tuition_fee;
  const hasPayedTutionAndRegistrationFee =
    studentFeeStatus?.registration_fee && studentFeeStatus?.tuition_fee;

  const { calendarEvents, isLoading: isCalendarEventsLoading } =
    useGetCalendarEvents();

  const handleLoadMore = () => {
    setVisibleEvents((prev) => {
      const nextValue = prev + 2;
      return Math.min(nextValue, calendarEvents?.length || 0);
    });
  };

  return (
    <div className="space-y-6">
      {/* Welcome Banner */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          sx={{
            background: hasPayedTutionAndRegistrationFee
              ? "linear-gradient(135deg, #22c55e 0%, #16a34a 100%)" // vibrant green
              : hasPayedRegistrationFee || hasPayedTutionFee
                ? "linear-gradient(135deg, #fbbf24 0%, #f59e42 100%)" // vibrant orange
                : "linear-gradient(135deg, #ef4444 0%, #b91c1c 100%)", // vibrant red
            boxShadow: "0 4px 24px rgba(0,0,0,0.10)",
            color: "white",
            position: "relative",
            overflow: "hidden",
            p: 6,
            minHeight: 260,
            borderRadius: 4,
          }}
        >
          {/* Animated SVG Icon */}
          {hasPayedTutionAndRegistrationFee ? (
            <motion.svg
              initial={{ y: 0, opacity: 0.22 }}
              animate={{ y: [0, 18, 0], opacity: 0.22 }}
              transition={{ duration: 7, repeat: Infinity, ease: "easeInOut" }}
              style={{
                position: "absolute",
                right: 40,
                top: 30,
                width: 160,
                height: 160,
                zIndex: 1,
                pointerEvents: "none",
                userSelect: "none",
              }}
              viewBox="0 0 48 48"
              fill="none"
            >
              <circle
                cx="24"
                cy="24"
                r="22"
                fill="#16a34a"
                fillOpacity="0.18"
              />
              <path
                d="M16 24l6 6 10-12"
                stroke="#16a34a"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </motion.svg>
          ) : hasPayedRegistrationFee || hasPayedTutionFee ? (
            <motion.svg
              initial={{ y: 0, opacity: 0.22 }}
              animate={{ y: [0, 18, 0], opacity: 0.22 }}
              transition={{ duration: 7, repeat: Infinity, ease: "easeInOut" }}
              style={{
                position: "absolute",
                right: 40,
                top: 30,
                width: 160,
                height: 160,
                zIndex: 1,
                pointerEvents: "none",
                userSelect: "none",
              }}
              viewBox="0 0 48 48"
              fill="none"
            >
              <circle
                cx="24"
                cy="24"
                r="22"
                fill="#fbbf24"
                fillOpacity="0.18"
              />
              <path
                d="M24 14v10"
                stroke="#fbbf24"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <circle cx="24" cy="32" r="2" fill="#fbbf24" />
            </motion.svg>
          ) : (
            <motion.svg
              initial={{ y: 0, opacity: 0.22 }}
              animate={{ y: [0, 18, 0], opacity: 0.22 }}
              transition={{ duration: 7, repeat: Infinity, ease: "easeInOut" }}
              style={{
                position: "absolute",
                right: 40,
                top: 30,
                width: 160,
                height: 160,
                zIndex: 1,
                pointerEvents: "none",
                userSelect: "none",
              }}
              viewBox="0 0 48 48"
              fill="none"
            >
              <circle
                cx="24"
                cy="24"
                r="22"
                fill="#ef4444"
                fillOpacity="0.18"
              />
              <path
                d="M24 16v10"
                stroke="#ef4444"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <circle cx="24" cy="32" r="2" fill="#ef4444" />
            </motion.svg>
          )}
          <Grid
            container
            spacing={3}
            alignItems="center"
            className="relative z-10"
          >
            <Grid item xs={12} md={8}>
              <Typography
                variant="h4"
                className="font-bold mb-2"
                sx={{
                  fontFamily: "'Poppins', sans-serif",
                  textShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  color: "white",
                }}
              >
                Welcome {user?.userInfo?.first_name} {user?.userInfo?.last_name}
                !
              </Typography>
              <Typography
                variant="body1"
                className="mb-4"
                sx={{
                  fontFamily: "'Inter', sans-serif",
                  textShadow: "0 1px 2px rgba(0,0,0,0.1)",
                  color: "white",
                  fontWeight: 500,
                  fontSize: 20,
                }}
              >
                {hasPayedTutionAndRegistrationFee
                  ? "All fees complete! You're good to go."
                  : hasPayedRegistrationFee
                    ? "Registration fee paid. Please complete tuition fee."
                    : hasPayedTutionFee
                      ? "Tuition fee paid. Please complete registration fee."
                      : "You have outstanding fees. Please meet the finance team to register courses."}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4} className="hidden md:block">
              <motion.img
                src={Illustration}
                alt=""
                className="w-full max-w-xs mx-auto"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              />
            </Grid>
          </Grid>
        </Card>
      </motion.div>

      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Courses Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Card className="p-6 mb-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <Typography
                    variant="h6"
                    className="text-green font-semibold"
                    sx={{ fontFamily: "'Poppins', sans-serif" }}
                  >
                    Courses
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontFamily: "'Inter', sans-serif" }}
                  >
                    {system?.current_semester == 1
                      ? "First Semester"
                      : "Second Semester"}
                  </Typography>
                </div>
                <Button
                  color="success"
                  endIcon={<KeyboardArrowDownIcon />}
                  onClick={handleClick}
                  sx={{
                    textTransform: "none",
                    fontFamily: "'Inter', sans-serif",
                    background: "linear-gradient(to right, #22c55e, #16a34a)",
                    "&:hover": {
                      background: "linear-gradient(to right, #16a34a, #15803d)",
                    },
                  }}
                >
                  {student?.student?.academic_year?.name}
                </Button>
              </div>

              {isRegisteredCoursesLoading ? (
                <div className="flex justify-center py-8">
                  <Spinner />
                </div>
              ) : (
                <TableContainer
                  component={Paper}
                  elevation={0}
                  className="border rounded-lg overflow-hidden"
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Course Code</TableCell>
                        <TableCell>Course Name</TableCell>
                        <TableCell>Credits</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {registeredCourses?.data?.map((course) => (
                        <TableRow key={course.id}>
                          <TableCell>{course.code}</TableCell>
                          <TableCell>{course.name}</TableCell>
                          <TableCell>{course.credits}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Card>
          </motion.div>

          {/* Results Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <Card className="p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <Typography
                    variant="h6"
                    className="text-green font-semibold"
                    sx={{ fontFamily: "'Poppins', sans-serif" }}
                  >
                    Results
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontFamily: "'Inter', sans-serif" }}
                  >
                    {system?.current_semester == 1
                      ? "First Semester"
                      : "Second Semester"}
                  </Typography>
                </div>
                <Button
                  color="success"
                  endIcon={<KeyboardArrowDownIcon />}
                  onClick={handleClick}
                  sx={{
                    textTransform: "none",
                    fontFamily: "'Inter', sans-serif",
                    background: "linear-gradient(to right, #22c55e, #16a34a)",
                    "&:hover": {
                      background: "linear-gradient(to right, #16a34a, #15803d)",
                    },
                  }}
                >
                  {student?.student?.academic_year?.name}
                </Button>
              </div>

              <div className="mt-4 p-4 border rounded-lg bg-gradient-to-br from-gray-50 to-white">
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center"
                  sx={{ fontFamily: "'Inter', sans-serif" }}
                >
                  Results for this semester are not yet available!
                </Typography>
              </div>
            </Card>
          </motion.div>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <div className="space-y-6">
            {/* Attendance Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-gray-50 to-white">
                <Typography
                  variant="subtitle1"
                  className="font-medium mb-3"
                  sx={{ fontFamily: "'Poppins', sans-serif" }}
                >
                  Today's Attendance
                </Typography>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <WatchLaterOutlinedIcon color="action" />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ fontFamily: "'Inter', sans-serif" }}
                    >
                      Check-in 9:00 am
                    </Typography>
                  </div>
                  <Button
                    variant="contained"
                    color="success"
                    disabled
                    sx={{
                      textTransform: "none",
                      fontFamily: "'Inter', sans-serif",
                      background: "linear-gradient(to right, #22c55e, #16a34a)",
                      "&:hover": {
                        background:
                          "linear-gradient(to right, #16a34a, #15803d)",
                      },
                    }}
                  >
                    Present
                  </Button>
                </div>
              </Card>
            </motion.div>

            {/* Upcoming Events Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-gray-50 to-white">
                <div className="flex items-center gap-2 mb-3">
                  <TodayOutlinedIcon color="action" />
                  <Typography
                    variant="subtitle1"
                    className="font-medium"
                    sx={{ fontFamily: "'Poppins', sans-serif" }}
                  >
                    Upcoming
                  </Typography>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <FormControlLabel
                      control={<Checkbox size="small" />}
                      label={
                        <Typography sx={{ fontFamily: "'Inter', sans-serif" }}>
                          Assignments
                        </Typography>
                      }
                    />
                    <Typography
                      variant="subtitle2"
                      color="error"
                      sx={{ fontFamily: "'Inter', sans-serif" }}
                    >
                      2
                    </Typography>
                  </div>
                  <div className="flex items-center justify-between">
                    <FormControlLabel
                      control={<Checkbox size="small" />}
                      label={
                        <Typography sx={{ fontFamily: "'Inter', sans-serif" }}>
                          CA
                        </Typography>
                      }
                    />
                    <Typography
                      variant="subtitle2"
                      color="error"
                      sx={{ fontFamily: "'Inter', sans-serif" }}
                    >
                      5
                    </Typography>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Calendar Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-gray-50 to-white">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateCalendar />
                </LocalizationProvider>
              </Card>
            </motion.div>

            {/* School Calendar Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-gray-50 to-white">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <Typography
                      variant="subtitle1"
                      className="font-medium"
                      sx={{ fontFamily: "'Poppins', sans-serif" }}
                    >
                      School Calendar
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ fontFamily: "'Inter', sans-serif" }}
                    >
                      {system?.current_semester == 1
                        ? "First Semester"
                        : "Second Semester"}
                    </Typography>
                  </div>
                  <Button
                    color="success"
                    endIcon={<KeyboardArrowDownIcon />}
                    onClick={handleClick}
                    sx={{
                      textTransform: "none",
                      fontFamily: "'Inter', sans-serif",
                      background:
                        "linear-gradient(to right, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05))",
                      "&:hover": {
                        background:
                          "linear-gradient(to right, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.1))",
                      },
                    }}
                  >
                    {system?.current_academic_year_name}
                  </Button>
                </div>

                <div
                  className="space-y-3 max-h-[400px] overflow-y-auto pr-2 scroll-smooth"
                  style={{ scrollbarWidth: "thin" }}
                >
                  {isCalendarEventsLoading ? (
                    <div className="flex justify-center py-4">
                      <Spinner />
                    </div>
                  ) : calendarEvents?.length === 0 ? (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                      sx={{ fontFamily: "'Inter', sans-serif" }}
                    >
                      No events scheduled for this period
                    </Typography>
                  ) : (
                    <>
                      {calendarEvents.slice(0, visibleEvents).map((item) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                          className="flex items-start gap-3 p-2 rounded-lg hover:bg-gradient-to-r hover:from-gray-50 hover:to-white transition-colors duration-200"
                        >
                          <Avatar
                            {...stringAvatar(item.title)}
                            variant="square"
                          />
                          <div>
                            <Typography
                              variant="subtitle2"
                              className="font-medium"
                              sx={{ fontFamily: "'Poppins', sans-serif" }}
                            >
                              {item.title}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ fontFamily: "'Inter', sans-serif" }}
                            >
                              {item.description}
                            </Typography>
                            <div className="flex items-center gap-1 mt-1">
                              <TodayOutlinedIcon
                                sx={{ fontSize: 14 }}
                                color="action"
                              />
                              <Typography
                                variant="caption"
                                color="text.secondary"
                                sx={{ fontFamily: "'Inter', sans-serif" }}
                              >
                                {new Date(item.event_date).toLocaleDateString(
                                  "en-US",
                                  {
                                    day: "numeric",
                                    month: "long",
                                    year: "numeric",
                                  }
                                )}
                              </Typography>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                      {visibleEvents < (calendarEvents?.length || 0) && (
                        <div className="flex justify-center py-2">
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={handleLoadMore}
                            sx={{
                              textTransform: "none",
                              fontFamily: "'Inter', sans-serif",
                              borderColor: "rgba(34, 197, 94, 0.5)",
                              color: "rgba(34, 197, 94, 0.8)",
                              "&:hover": {
                                borderColor: "rgba(34, 197, 94, 0.8)",
                                backgroundColor: "rgba(34, 197, 94, 0.05)",
                              },
                            }}
                          >
                            Load More Events
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </Card>
            </motion.div>
          </div>
        </Grid>
      </Grid>

      {/* Year Selection Menu */}
      <Menu
        id="fade-menu"
        MenuListProps={{
          "aria-labelledby": "fade-button",
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
      >
        {isAcademicYearsLoading ? (
          <MenuItem disabled>
            <Spinner size="14px" />
          </MenuItem>
        ) : (
          academicYears?.data?.map((year) => (
            <MenuItem key={year.id} onClick={() => handleYearSelect(year.name)}>
              {year.name}
            </MenuItem>
          ))
        )}
      </Menu>
    </div>
  );
};

export default StudentDashboard;
