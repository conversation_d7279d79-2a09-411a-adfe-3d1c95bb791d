import { stringAvatar } from "@lib/util";
import NotificationsNoneIcon from "@mui/icons-material/NotificationsNone";
import { Avatar, Badge, IconButton, MenuItem, Select } from "@mui/material";
import { useState } from "react";
import { useSelector } from "react-redux";

export function Header() {
  const { user } = useSelector((state) => state.auth);
  const [lang, setLang] = useState("en");
  const handleLangChange = (e) => setLang(e.target.value);

  return (
    <div className="flex flex-row items-center justify-between w-full px-2 py-1">
      {/* Left: Welcome message */}
      <div className="flex flex-col">
        <span className="text-gray-700 text-base font-medium">
          Welcome back
        </span>
        <span className="font-bold text-green text-lg">
          {user?.userInfo?.first_name} {user?.userInfo?.last_name}
        </span>
      </div>
      {/* Right: Controls */}
      <div className="flex flex-row items-center gap-4">
        {/* Language Select */}
        <Select
          value={lang}
          onChange={handleLangChange}
          size="small"
          variant="outlined"
          sx={{
            minWidth: 80,
            fontSize: 14,
            background: "#f3f4f6",
            borderRadius: 2,
            height: 36,
            ".MuiOutlinedInput-notchedOutline": { border: "none" },
            "& .MuiSelect-icon": { color: "#16a34a" },
          }}
        >
          <MenuItem value="en">EN</MenuItem>
          <MenuItem value="fr">FR</MenuItem>
        </Select>
        {/* Notification Icon */}
        <IconButton sx={{ color: "#16a34a" }}>
          <Badge badgeContent={2} color="error" overlap="circular">
            <NotificationsNoneIcon fontSize="medium" />
          </Badge>
        </IconButton>
        {/* Profile */}
        <div className="flex flex-row items-center gap-2">
          <Avatar
            {...stringAvatar(
              `${user?.userInfo?.first_name ?? ""} ${user?.userInfo?.last_name ?? ""}`
            )}
            sx={{ width: 36, height: 36, fontSize: 18, bgcolor: "#16a34a" }}
          />
          <span className="text-gray-700 font-medium text-base">
            {user?.userInfo?.first_name}
          </span>
        </div>
      </div>
    </div>
  );
}
