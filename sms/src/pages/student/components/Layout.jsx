import useAuth from "@hooks/useAuth";
import AuthGuard from "@pages/auth/AuthGaurd";
import { DashboardLayout } from "@pages/student/components";
import { useEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";

function Layout() {
  const { token } = useAuth();
  const navigate = useNavigate();
  useEffect(() => {
    if (!token) {
      navigate("/student/login", { replace: true });
    }
  }, [token]);
  return (
    <>
      <DashboardLayout>
        <AuthGuard allowedRoles={["student"]}>
          <Outlet />
        </AuthGuard>
      </DashboardLayout>
    </>
  );
}

export default Layout;
