import PropTypes from "prop-types";

function TranscriptWrapper({ children }) {
  return (
    <section className="min-h-screen w-full flex flex-col items-center justify-between gap-6">
      <div className="w-full flex flex-col items-start">
        <p className="text-black text-md font-semibold">Transcript</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>
      {children}

      <div className="w-full flex items-center justify-center mt-6">
        <p className="text-sm text-black">
          &copy; {new Date().getFullYear()} Copyright:{" "}
          <span className="font-semibold text-green">Skye8</span>
        </p>
      </div>
    </section>
  );
}

TranscriptWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default TranscriptWrapper;
