export const CaInformation = [
  {
    id: "1",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "2",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "3",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "4",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "5",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "6",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "7",
    noOfCourses: "12",
    studentName: "<PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "8",
    noOfCourses: "12",
    studentName: "<PERSON> <PERSON>",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "9",
    noOfCourses: "12",
    studentName: "Mary Jones",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
  {
    id: "10",
    noOfCourses: "12",
    studentName: "Mary Jones",
    CasTaken: "07",
    matricule: "EHIST1345",
  },
];

export const transcriptResults = [
  {
    id: "1",
    noOfCourses: "10",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "2",
    noOfCourses: "4",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "3",
    noOfCourses: "12",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "4",
    noOfCourses: "2",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "5",
    noOfCourses: "7",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "6",
    noOfCourses: "12",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "7",
    noOfCourses: "6",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "8",
    noOfCourses: "9",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "9",
    noOfCourses: "13",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
  {
    id: "10",
    noOfCourses: "12",
    studentName: "Mary Jones",
    CasTaken: "07",
    examsTaken: "2",
    matricule: "EHIST1345",
    gpa: "3.55",
  },
];

export const finaResults = [
  {
    id: "1",
    matricule: "EHIST1345",
    studentName: "Mary Jones",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "2",
    matricule: "EHIST1345",
    studentName: "Swirri Nadia",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "3",
    matricule: "EHIST1345",
    studentName: "Nfon William",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "4",
    matricule: "EHIST1345",
    studentName: "Stephenie Soo",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "5",
    matricule: "EHIST1345",
    studentName: "Mary Jones",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "6",
    matricule: "EHIST1345",
    studentName: "Swirri Nadia",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "7",
    matricule: "EHIST1345",
    studentName: "Nfon William",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "8",
    matricule: "EHIST1345",
    studentName: "Stephenie Soo",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "9",
    matricule: "EHIST1345",
    studentName: "Mary Jones",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
  {
    id: "10",
    matricule: "EHIST1345",
    studentName: "Swirri Nadia",
    courses: "12",
    caTaken: "12",
    examTaken: "2",
    grade: "B+",
    gpa: "4.4",
  },
];

export const firstSemesterResults = [
  {
    id: "1",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "2",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "3",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "4",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "5",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "6",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "7",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "8",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "9",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
  {
    id: "10",
    courseTitle: "Business & Finance",
    courseCode: "COME638",
    creditValue: "04",
    ca: "25",
    exam: "50",
    total: "75",
    grade: "B+",
    gadePoint: "4.4",
  },
];

export const gradeSystem = [
  {
    id: "1",
    grade: "A",
    mark: "80-100",
    gradePoint: "4.0",
  },
  {
    id: "2",
    grade: "B+",
    mark: "70-79",
    gradePoint: "3.5",
  },
  {
    id: "3",
    grade: "B",
    mark: "60-69",
    gradePoint: "3.0",
  },
  {
    id: "4",
    grade: "C+",
    mark: "55-59",
    gradePoint: "2.5",
  },
  {
    id: "5",
    grade: "C",
    mark: "50-54",
    gradePoint: "2.0",
  },
  {
    id: "6",
    grade: "D+",
    mark: "45-49",
    gradePoint: "1.5",
  },
  {
    id: "7",
    grade: "D",
    mark: "40-44",
    gradePoint: "1.1",
  },
  {
    id: "8",
    grade: "F",
    mark: "0-39",
    gradePoint: "0",
  },
];
