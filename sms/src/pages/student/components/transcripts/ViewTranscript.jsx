import Logo from "@assets/images/logo.jpg";
import { StyledTableCell } from "@components";
import DownloadIcon from "@mui/icons-material/Download";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import {
  firstSemesterResults,
  gradeSystem,
} from "@pages/student/components/transcripts/transcript/data";
import { motion } from "framer-motion";

import { useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function ViewTranscript() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(10);
  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="w-full flex flex-col items-center justify-center py-4 gap-4 bg-gradient-to-br from-green-50 via-white to-blue-50 min-h-screen"
    >
      {/* Header Card (three-column layout: school name, logo, contact info, and student info below) */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="w-full max-w-9xl bg-white/80 backdrop-blur-md rounded-2xl shadow-lg px-8 py-6 flex flex-col gap-2 border border-gray-100 relative"
      >
        <div className="w-full flex flex-row items-center justify-between gap-2">
          {/* Left: School Name */}
          <div className="flex-1 flex flex-col items-start justify-center text-black text-lg font-semibold leading-tight pl-4">
            <span>EBENEZER HIGHER</span>
            <span>INSTITUTE OF SCIENCE</span>
            <span>AND TECHNOLOGY (EHIST)</span>
          </div>
          {/* Center: Logo */}
          <div className="flex-1 flex flex-col items-center justify-center">
            <img src={Logo} alt="logo" className="w-20 h-20 object-contain" />
          </div>
          {/* Right: Contact Info */}
          <div className="flex-1 flex flex-col items-end justify-center text-black text-lg font-semibold leading-tight pr-4">
            <span>BAMENDA - SONAC STREET</span>
            <span className="text-base font-normal"><EMAIL></span>
            <span className="text-base font-normal">+237 678332504</span>
          </div>
        </div>
        {/* Student Info (now inside the same card) */}
        <div className="w-full flex flex-col md:flex-row items-start justify-between gap-6 mt-2">
          <div className="flex-1 flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">Name:</span>
              <span className="text-base font-bold text-black">Mary Jones</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">Matricule:</span>
              <span className="text-base font-bold text-black">EHIST3030</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">School:</span>
              <span className="text-sm font-medium text-black">
                SCHOOL OF HOME ECONOMICS TOURISM AND HOSPITALITY MANAGEMENT
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">Department:</span>
              <span className="text-sm font-medium text-black">
                HOTEL MANAGEMENT AND CATERING
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">
                Degree in view:
              </span>
              <span className="text-sm font-medium text-black">
                DEGREE IN HOTEL MANAGEMENT AND CATERING
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">Reference No:</span>
              <span className="text-sm font-medium text-black">
                .....................
              </span>
            </div>
          </div>
          <div className="flex-1 flex flex-col gap-1 md:items-end">
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">
                Date/place of Birth:
              </span>
              <span className="text-sm font-medium text-black">
                07/07/1992 , Bamenda
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-700">Sex:</span>
              <span className="text-sm font-medium text-black">Female</span>
            </div>
          </div>
        </div>
      </motion.div>
      {/* Academic Years Section */}
      <div className="w-full max-w-9xl flex flex-col gap-8 mt-8">
        {/* Example for one academic year, repeat for others as needed */}
        {["2021/2022", "2022/2023", "2023/2024"].map((year, i) => (
          <motion.div
            key={year}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 + i * 0.1, duration: 0.5 }}
            className="bg-white/90 rounded-2xl shadow-lg border border-gray-100 p-6 flex flex-col gap-6 relative"
          >
            <div className="flex items-center justify-between mb-2 w-full">
              <div className="flex items-center gap-3">
                <span className="text-green font-bold text-lg">
                  Academic year:
                </span>
                <span className="bg-green/10 text-green font-semibold px-3 py-1 rounded-full text-md">
                  {year}
                </span>
              </div>
              <button
                className="flex items-center gap-2 bg-green text-white px-4 py-2 rounded-lg shadow hover:bg-green-700 transition-colors duration-200"
                onClick={() => {
                  /* TODO: implement download functionality for this year */
                }}
              >
                <DownloadIcon fontSize="small" />
                <span className="font-semibold">Download</span>
              </button>
            </div>
            {/* Semesters */}
            {["First Semester", "Second Semester"].map((semester, j) => (
              <motion.div
                key={semester}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + j * 0.1, duration: 0.4 }}
                className="flex flex-col gap-2"
              >
                <div className={`flex items-center gap-2 mb-2`}>
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-semibold ${j === 0 ? "bg-blue-100 text-blue-700" : "bg-orange-100 text-orange-700"}`}
                  >
                    {semester}
                  </span>
                </div>
                <ThemeProvider theme={theme}>
                  <div className="flex flex-col w-full my-2">
                    <TableContainer
                      component={Paper}
                      className="rounded-xl shadow-md"
                    >
                      <Table
                        sx={{
                          maxWidth: "100%",
                          fontFamily: "Inter, sans-serif",
                        }}
                      >
                        <TableHead>
                          <TableRow sx={{ background: "#f9fafb" }}>
                            <StyledTableCell>S/N</StyledTableCell>
                            <StyledTableCell>Course Title</StyledTableCell>
                            <StyledTableCell>Course Code</StyledTableCell>
                            <StyledTableCell>Credit Value</StyledTableCell>
                            <StyledTableCell>CA Mark</StyledTableCell>
                            <StyledTableCell>Exam Mark</StyledTableCell>
                            <StyledTableCell>Total</StyledTableCell>
                            <StyledTableCell>Grade</StyledTableCell>
                            <StyledTableCell>Grade Point</StyledTableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {firstSemesterResults.map((item, idx) => (
                            <motion.tr
                              key={item.id + semester}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{
                                delay: 0.4 + idx * 0.03,
                                duration: 0.3,
                              }}
                              className={
                                idx % 2 === 0
                                  ? "bg-white transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                                  : "bg-gray-50 transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                              }
                            >
                              <StyledTableCell>{item.id}</StyledTableCell>
                              <StyledTableCell>
                                {item.courseTitle}
                              </StyledTableCell>
                              <StyledTableCell>
                                {item.courseCode}
                              </StyledTableCell>
                              <StyledTableCell>
                                {item.creditValue}
                              </StyledTableCell>
                              <StyledTableCell>{item.ca}</StyledTableCell>
                              <StyledTableCell>{item.exam}</StyledTableCell>
                              <StyledTableCell>{item.total}</StyledTableCell>
                              <StyledTableCell>
                                <span
                                  className={`px-2 py-1 rounded-full text-xs font-bold ${item.grade === "A" ? "bg-green-100 text-green-700" : item.grade === "B" ? "bg-orange-100 text-orange-700" : "bg-gray-200 text-gray-700"}`}
                                >
                                  {item.grade}
                                </span>
                              </StyledTableCell>
                              <StyledTableCell>
                                {item.gadePoint}
                              </StyledTableCell>
                            </motion.tr>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </div>
                </ThemeProvider>
                {/* GPA & Summary */}
                <div className="flex flex-wrap items-center gap-4 mt-2">
                  <div className="flex items-center gap-2 bg-green/10 rounded-full px-4 py-2">
                    <span className="text-green font-bold text-sm">GPA:</span>
                    <span className="font-semibold text-green">
                      {j === 0 ? "3.75" : "3.45"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 bg-blue-50 rounded-full px-4 py-2">
                    <span className="text-blue-700 font-bold text-sm">
                      Credits:
                    </span>
                    <span className="font-semibold text-blue-700">21</span>
                  </div>
                  <div className="flex items-center gap-2 bg-orange-50 rounded-full px-4 py-2">
                    <span className="text-orange-700 font-bold text-sm">
                      Courses:
                    </span>
                    <span className="font-semibold text-orange-700">8</span>
                  </div>
                </div>
              </motion.div>
            ))}
            {/* Academic Year GPA */}
            <div className="w-full flex items-start bg-green/10 border-l-4 border-l-green p-2 mt-4 rounded-lg">
              <span className="font-semibold text-green">
                Academic Year GPA:
              </span>
              <span className="ml-2 px-3 py-1 bg-green text-white rounded-full font-bold">
                3.25
              </span>
            </div>
          </motion.div>
        ))}
      </div>
      {/* Final Summary & Grading System */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
        className="w-full max-w-6xl flex flex-col gap-8 mt-8"
      >
        {/* Final grading section */}
        <div className="flex flex-col bg-white/90 shadow-lg border p-6 gap-4 border-gray-100 rounded-2xl">
          <div className="flex flex-wrap items-center justify-between gap-6">
            <div className="flex items-center gap-2 bg-blue-50 rounded-full px-4 py-2">
              <span className="text-blue-700 font-bold text-sm">
                Total Attempted Credits:
              </span>
              <span className="font-semibold text-blue-700">160</span>
            </div>
            <div className="flex items-center gap-2 bg-green/10 rounded-full px-4 py-2">
              <span className="text-green font-bold text-sm">
                Total Credits Earned:
              </span>
              <span className="font-semibold text-green">160</span>
            </div>
            <div className="flex items-center gap-2 bg-orange-50 rounded-full px-4 py-2">
              <span className="text-orange-700 font-bold text-sm">
                Cumulative GPA:
              </span>
              <span className="font-semibold text-orange-700">2.58</span>
            </div>
          </div>
        </div>
        {/* Grading System and Degree Classification */}
        <div className="flex flex-col md:flex-row gap-6">
          {/* Grade System */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="flex-1 bg-white/90 shadow-lg border p-6 rounded-2xl border-gray-100"
          >
            <div className="mb-3">
              <p className="text-black text-lg font-bold mb-1">Grade System</p>
              <div className="w-[35px] h-[2px] bg-red mb-2"></div>
            </div>
            <TableContainer component={Paper} className="rounded-xl">
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <TableCell>Grade</TableCell>
                    <TableCell>Mark</TableCell>
                    <TableCell>Grade Point GP</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {gradeSystem.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.grade}</TableCell>
                      <TableCell>{item.mark}</TableCell>
                      <TableCell>{item.gradePoint}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>
          {/* Degree Classification */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="flex-1 bg-white/90 shadow-lg border p-6 rounded-2xl border-gray-100"
          >
            <div className="mb-3">
              <p className="text-black text-lg font-bold mb-1">
                Degree Classification
              </p>
              <div className="w-[35px] h-[2px] bg-red mb-2"></div>
            </div>
            <div className="flex flex-col gap-3">
              <div className="flex items-center justify-between">
                <span className="text-black font-normal">First Class:</span>
                <span className="text-green font-bold">3.60-4.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-black font-normal">
                  Second Class Upper Division:
                </span>
                <span className="text-blue-700 font-bold">3.00-3.56</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-black font-normal">
                  Second Class Lower Division:
                </span>
                <span className="text-orange-700 font-bold">2.50-2.99</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-black font-normal">Third Class:</span>
                <span className="text-yellow-600 font-bold">2.25-2.49</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-black font-normal">Pass:</span>
                <span className="text-red font-bold">2.00-2.44</span>
              </div>
            </div>
          </motion.div>
        </div>
        {/* Footer */}
        <div className="w-full flex items-center justify-between mt-8">
          <div className="flex flex-col bg-white border-2 p-2 gap-3 border-gray-100 rounded-md">
            <p className="text-black font-semibold text-md">Date Issued:</p>
            <span className="font-semibold text-black">--/--/----</span>
          </div>
          <div className="flex flex-col bg-white border-2 p-2 gap-3 border-gray-100 rounded-md">
            <p className="text-black font-semibold text-md">
              Registrar&apos;s Office
            </p>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}

export default ViewTranscript;
