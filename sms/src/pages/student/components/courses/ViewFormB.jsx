import {
  useGetAllAcademicYearsQuery,
  useGetAllLevelsQuery,
  useGetDepartmentByIdQuery,
  useGetRegisteredCoursesQuery,
  useGetSingleSchoolQuery,
} from "@app";
import Logo from "@assets/images/logo.jpg";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { useDownload } from "@hooks/useDownload";
import AddIcon from "@mui/icons-material/Add";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Button,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

function ViewFormB() {
  const theme = createTheme({
    palette: {
      green: {
        main: "#1F7F1F",
      },
    },
  });

  const [filter, setFilter] = useState({
    school: "",
    dept: "",
    level: "",
    semester: "First Semester",
    year: "2023/2024",
  });

  const { user, data: student } = useSelector((state) => state.auth);
  const [query, setQuery] = useState(
    `student_id=${student?.student?.student_id}&academic_year_id=${student?.student?.academic_year?.id ?? 2}&level_id=${student?.student?.level?.level_id ?? 2}&semester=${student?.student?.semester ?? 1}`
  );
  const { data: department, isLoading: isDeparmentLoading } =
    useGetDepartmentByIdQuery(student?.student?.department_id);
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("get all levels");
  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("get allademic years");
  const { data: school, isLoading: isSchoolLoading } = useGetSingleSchoolQuery(
    department?.data?.school_id
  );
  const { data, isLoading, isError, status } = useGetRegisteredCoursesQuery(
    query,
    "registered courses"
  );
  const [isFormBDownloading, setIsFormBDownloading] = useState(false);

  const handleChange = (e) => {
    setFilter((filter) => ({ ...filter, [e.target.name]: e.target.value }));
    setQuery(
      `student_id=${student?.student?.student_id}&academic_year_id=${filter.year}&level_id=${filter?.level}&semester=${filter?.semester}`
    );
  };

  const navigate = useNavigate();
  const [totalCreditValue, setTotalCreditValue] = useState();

  useEffect(() => {
    switch (status) {
      case "fulfilled": {
        let creditValue;
        creditValue = data?.data?.courses?.reduce(
          (acc, course) => acc + course.credit_value,
          0
        );
        setTotalCreditValue(creditValue);
        break;
      }
    }
  }, [status]);
  const dowload = useDownload();

  const handleDownloadFormB = async () => {
    try {
      setIsFormBDownloading(true);
      const url = await dowload(`results/formb?${query}`, "application/pdf");
      const a = document.createElement("a");
      a.href = url;
      a.download = `${student?.student?.matricule}-formb`;
      a.click();
      setIsFormBDownloading(false);
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.log("Error Downloading date", error);
    }
  };

  return (
    <section className="w-full flex flex-col items-start justify-between gap-6">
      <div className="w-full flex items-center gap-2">
        <div className="hover:bg-white p-2 rounded-full">
          <ArrowBackIcon onClick={() => navigate("/student/courses")} />
        </div>
        <p className="text-black font-semibold text-md">Form B</p>
      </div>
      <div className="w-full flex items-center justify-between bg-white shadow-md p-5 rounded-md gap-2">
        <p className="text-black text-sm font-normal">Select: </p>
        <TextField
          select
          id="year"
          size="small"
          value={filter.year}
          label="select year"
          name="year"
          onChange={handleChange}
          fullWidth
        >
          {isAcademicYearsLoading ? (
            <Spinner size="14px" />
          ) : (
            academicYears?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name}
              </MenuItem>
            ))
          )}
        </TextField>
        <TextField
          select
          id="level"
          size="small"
          value={filter.level}
          label="select level"
          name="level"
          onChange={handleChange}
          fullWidth
        >
          {isLevelsLoading ? (
            <Spinner size="14px" />
          ) : (
            levels?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name}
              </MenuItem>
            ))
          )}
        </TextField>
        <TextField
          select
          id="semester"
          size="small"
          value={filter.semester}
          label="select semester"
          name="semester"
          onChange={handleChange}
          fullWidth
        >
          {[1, 2].map((item) => (
            <MenuItem key={item} value={item}>
              {item}
            </MenuItem>
          ))}
        </TextField>

        <Button
          variant="contained"
          color="success"
          startIcon={<AddIcon />}
          style={{ textTransform: "capitalize" }}
          onClick={() => navigate("/student/courses/add")}
        >
          Add
        </Button>
      </div>

      <div className="w-full flex flex-col items-start justify-between gap-4 mb-6">
        <div className="w-full flex items-start justify-between">
          <p className="font-semibold text-black text-sm w-1/5">
            EBENZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
          </p>
          <img src={Logo} alt="" className="w-[50px]" />
          <div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                BAMENDA - SONAC STREET
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                {isSchoolLoading ? (
                  <Spinner size="18px" />
                ) : (
                  school?.data?.school_email
                )}
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                {isSchoolLoading ? (
                  <Spinner size="18px" />
                ) : (
                  school?.data?.phone_number
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="w-full h-[2px] bg-grey_300" />
        <div className="w-full flex items-start justify-between">
          <div className="w-full flex flex-col items-start gap-2">
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">School:</p>
              <h2 className="text-black font-semibold text-md">
                {isSchoolLoading ? <Spinner size="18px" /> : school?.data?.name}
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Department:</p>
              <h2 className="text-black font-semibold text-md">
                {isDeparmentLoading ? (
                  <Spinner size="18px" />
                ) : (
                  department?.data?.name
                )}
              </h2>
            </div>
            <div className="w-full flex items-end gap-3">
              <p className="text-black font-normal text-sm">Name:</p>
              <h2 className="text-black font-semibold text-md">
                {user?.userInfo?.first_name} {user?.userInfo?.last_name}
              </h2>
            </div>
            <div className="w-full flex items-end gap-3">
              <p className="text-black font-normal text-sm">Matricule:</p>
              <h2 className="text-black font-semibold text-md">
                {student?.student?.matricule}
              </h2>
            </div>
          </div>
          <div className="w-full flex flex-col items-start gap-2">
            <div className="w-full flex items-center justify-end gap-3">
              <p className="text-black font-normal text-sm">Level:</p>
              <h2 className="text-black font-semibold text-md">
                {" "}
                {student?.student?.level?.name}
              </h2>
            </div>
            <div className="w-full flex items-center justify-end gap-3">
              <p className="text-black font-normal text-sm">Academic Year:</p>
              <h2 className="text-black font-semibold text-md">
                {" "}
                {student?.student?.academic_year?.name}
              </h2>
            </div>
            <div className="w-full flex items-start justify-end gap-3">
              <p className="text-black font-normal text-sm">Semester:</p>
              <h2 className="text-black font-semibold text-md">
                {filter.semester}
              </h2>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full flex flex-col items-center justify-between gap-3">
        <p className="text-green font-semibold text-">Registered Courses</p>
        <ThemeProvider theme={theme}>
          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Course</StyledTableCell>
                    <StyledTableCell>Course Code</StyledTableCell>
                    <StyledTableCell>Credit Value</StyledTableCell>
                    <StyledTableCell>Lecturer</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    <Spinner />
                  ) : isError ? (
                    <code>
                      Error getting coureses. Try refetching this page
                    </code>
                  ) : data?.data?.courses?.length < 1 ||
                    data?.data?.length < 1 ? (
                    <>No course available.</>
                  ) : (
                    data?.data?.courses?.map((course, idx) => (
                      <StyledTableRow key={course.id}>
                        <StyledTableCell>{idx + 1}</StyledTableCell>
                        <StyledTableCell>{course.name}</StyledTableCell>
                        <StyledTableCell>
                          {course?.code ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {course?.credit_value ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {course?.lecturer ?? "N/A"}
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </ThemeProvider>
      </div>
      <div className="w-full flex items-start gap-4">
        <p className="text-black font-normal text-sm">
          No of Registered Courses:{" "}
          <span className="font-bold text-green text-lg">
            {data?.data?.courses?.length}
          </span>
        </p>
        <p className="text-black font-normal text-sm">
          Total Credit Value:{" "}
          <span className="font-bold text-green text-lg">
            {totalCreditValue}
          </span>
        </p>
      </div>
      <div className="w-full flex items-center justify-center">
        {isFormBDownloading ? (
          <Spinner />
        ) : (
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={handleDownloadFormB}
          >
            Download
          </Button>
        )}
      </div>
    </section>
  );
}

export default ViewFormB;
