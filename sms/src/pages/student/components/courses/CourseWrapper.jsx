import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { Button, Chip, Fade, Menu, MenuItem } from "@mui/material";
import PropTypes from "prop-types";
import { useState } from "react";

function CourseWrapper({ children }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const system = useGetSystemSettings();
  return (
    <section className="min-h-screen w-full flex flex-col items-start mb-5">
      {/* Header Card */}
      <div className="w-full flex flex-col items-start justify-between gap-2 bg-white shadow-md rounded-b-xl p-6 border-b border-gray-100 mb-5">
        <div className="w-full flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex flex-col items-start gap-1">
            <p className="text-2xl font-bold text-green tracking-tight">
              Courses
            </p>
            <div className="w-12 h-1 bg-green rounded-full mb-1" />
            <div className="flex gap-2 mt-2">
              <Chip
                label={system?.current_academic_year_name || "Year"}
                color="success"
                variant="outlined"
                sx={{ fontWeight: 600, fontSize: 14 }}
              />
              <Chip
                label={
                  system?.current_semester == 1
                    ? "First Semester"
                    : "Second Semester"
                }
                color="primary"
                variant="outlined"
                sx={{ fontWeight: 600, fontSize: 14 }}
              />
              {system?.current_level && (
                <Chip
                  label={`Level ${system?.current_level}`}
                  color="info"
                  variant="outlined"
                  sx={{ fontWeight: 600, fontSize: 14 }}
                />
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              id="fade-button"
              aria-controls={open ? "fade-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={open ? "true" : undefined}
              onClick={handleClick}
              endIcon={<KeyboardArrowDownIcon />}
              color="success"
              variant="outlined"
              sx={{ borderRadius: 2, fontWeight: 600 }}
            >
              {system?.current_academic_year_name || "Select Year"}
            </Button>
            <Menu
              id="fade-menu"
              MenuListProps={{
                "aria-labelledby": "fade-button",
              }}
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              TransitionComponent={Fade}
            >
              <MenuItem onClick={handleClose} disabled>
                2022/2023
              </MenuItem>
              <MenuItem onClick={handleClose} disabled>
                2021/2022
              </MenuItem>
              <MenuItem onClick={handleClose} disabled>
                2020/2021
              </MenuItem>
            </Menu>
          </div>
        </div>
        {/* Registration Alert */}
        <div className="w-full mt-4">
          <div className="w-full bg-gradient-to-r from-green-100 to-green-50 border border-green-200 rounded-xl shadow-sm flex items-center px-6 py-4 gap-3">
            <span className="text-green-700 text-xl font-bold">⏰</span>
            <span className="text-green-800 font-medium text-base">
              Course Registration ends on{" "}
              <span className="font-bold">Friday 23rd, June 2024!</span>
            </span>
          </div>
        </div>
      </div>
      <div className="w-full flex-1">{children}</div>
      <div className="w-full flex items-center justify-center mt-6">
        <p className="text-sm text-gray-500">
          &copy; {new Date().getFullYear()} Copyright:{" "}
          <span className="font-semibold text-green">Skye8</span>
        </p>
      </div>
    </section>
  );
}

CourseWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default CourseWrapper;
