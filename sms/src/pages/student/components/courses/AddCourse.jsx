import {
  useGetAllAcademicYearsQuery,
  useGetAllCoursesQuery,
  useGetAllLevelsQuery,
  useRegisterCourseMutation,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AddCourse() {
  const { user } = useSelector((state) => state.auth);
  const { filter: stateFilter } = useSelector((state) => state.filter);

  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("all-academic-years");
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("all-levels");
  const [
    registerCourses,
    {
      isLoading: isRegisteringCourses,
      isError: isRegisteringCoursesError,
      error: registerCoursesError,
    },
  ] = useRegisterCourseMutation("register-course");

  const [query, setQuery] = useState(
    `student_id=${user?.student_id}&academic_year_id=${user?.academic_year_id?.id ?? 2}&level_id=${user?.level?.id ?? 2}&semester=${user?.semester ?? 1}`
  );
  const {
    data: courses,
    isLoading: isCoursesLoading,
    isError: isCourseError,
    isFetching: isCoursesFetching,
  } = useGetAllCoursesQuery(query);
  // const { data: registeredCourses, status } = useGetRegisteredCoursesQuery(
  //   query,
  //   "registered courses"
  // );

  const semesters = [1, 2];
  const [filter, setFilter] = useState({
    school: "",
    dept: "",
    level: "",
    semester: stateFilter?.semester,
    year: stateFilter?.academic_year,
  });
  const [form, setForm] = useState({
    academicYear: stateFilter?.academic_year,
    semester: stateFilter?.semester,
    level: "",
    courses: [],
    student_id: user?.student_id,
  });

  const handleChange = (e) => {
    console.log(e.target.value, e.target.name);
    setFilter((filter) => ({ ...filter, [e.target.name]: e.target.value }));
  };

  const handleFormChange = (e) => {
    setForm((prevForm) => ({ ...prevForm, [e.target.name]: e.target.value }));
    setQuery(
      `student_id=${user?.student_id}&academic_year_id=${form.academicYear}&level_id=${form.level}&semester=${form.semester}`
    );
  };

  const handleCourseSelection = (courseId) => {
    setForm((prevForm) => {
      const updatedCourses = prevForm.courses.includes(courseId)
        ? prevForm.courses.filter((id) => id !== courseId)
        : [...prevForm.courses, courseId];
      return { ...prevForm, courses: updatedCourses };
    });
  };

  const totalCreditValue = useMemo(() => {
    return (
      courses?.data
        ?.filter((course) => form.courses.includes(course.id))
        .reduce((total, course) => total + course.credit_value, 0) || 0
    );
  }, [courses?.data, form.courses]);
  const navigate = useNavigate();

  const registerCourse = async (e) => {
    e.preventDefault();
    try {
      const data = {
        student_id: form.student_id,
        course_ids: form.courses,
        academic_year_id: form.academicYear,
        level_id: form.level,
        semester: `${form.semester}`,
      };
      const { success, message } = await registerCourses(data).unwrap();
      alert(message);
      if (success) {
        navigate(-1);
      }
    } catch (error) {
      console.log(error);
      alert("An error occurred while registering the course");
    }
  };

  return (
    <section className="w-full flex flex-col items-center justify-between gap-4">
      <div className="w-full flex md:flex-row flex-col items-center justify-between bg-white shadow-md p-5 rounded-md gap-3">
        <p className="text-black text-sm font-normal">Select: </p>
        <TextField
          select
          id="year"
          size="small"
          value={filter.year}
          label="select year"
          name="year"
          onChange={handleChange}
          fullWidth
        >
          {academicYears?.data?.map((item) => (
            <MenuItem key={item.id} value={item.id}>
              {item.name}
            </MenuItem>
          ))}
        </TextField>
        <TextField
          select
          id="semester"
          size="small"
          value={filter.semester}
          label="select semester"
          name="semester"
          onChange={handleChange}
          fullWidth
        >
          {semesters.map((item) => (
            <MenuItem key={item} value={item}>
              {item}
            </MenuItem>
          ))}
        </TextField>

        <Button
          variant="outlined"
          color="success"
          style={{ textTransform: "capitalize" }}
          disabled
        >
          Sample Form B
        </Button>
      </div>

      <div className="w-full flex items-center gap-2">
        <div className="hover:bg-white p-2 rounded-full">
          <ArrowBackIcon onClick={() => navigate(-1, { replace: true })} />
        </div>
        <p className="text-black font-semibold text-md">Select Course</p>
      </div>
      <div className="w-full flex md:flex-row flex-col items-start gap-3">
        <TextField
          select
          id="academicYear"
          size="small"
          value={form.academicYear}
          label="Academic Year"
          name="academicYear"
          onChange={handleFormChange}
          fullWidth
          required
        >
          {isAcademicYearsLoading ? (
            <Spinner size="14px" />
          ) : (
            academicYears?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name?.replace("_", "/")}
              </MenuItem>
            ))
          )}
        </TextField>
        <TextField
          select
          id="semester"
          size="small"
          value={form.semester}
          label="select Semester"
          name="semester"
          onChange={handleFormChange}
          fullWidth
          required
        >
          {semesters.map((item) => (
            <MenuItem key={item} value={item}>
              {item}
            </MenuItem>
          ))}
        </TextField>

        <TextField
          select
          id="level"
          size="small"
          value={form.level}
          label="select level"
          name="level"
          onChange={handleFormChange}
          fullWidth
          required
        >
          {isLevelsLoading ? (
            <Spinner size="14px" />
          ) : (
            levels?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name}
              </MenuItem>
            ))
          )}
        </TextField>
      </div>

      <ThemeProvider theme={theme}>
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Course</StyledTableCell>
                  <StyledTableCell>Course Code</StyledTableCell>
                  <StyledTableCell>Credit Value</StyledTableCell>
                  <StyledTableCell>Lecturer</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isCoursesLoading || isCoursesFetching ? (
                  <Spinner />
                ) : isCourseError ? (
                  <code>Error Loading Courses</code>
                ) : courses?.data?.length < 1 ? (
                  <div className="text-sm text-orange">
                    <p className="text-blue"> No course available for:</p>
                    <p>
                      1. Academic Year:{" "}
                      {
                        academicYears?.data?.find(
                          (ay) => ay.id == form.academicYear
                        )?.name
                      }
                    </p>
                    <p>
                      2. Level :{" "}
                      {
                        levels?.data?.find((level) => level.id == form.level)
                          ?.name
                      }
                    </p>
                    <p>3. Semester : {form.semester}</p>
                  </div>
                ) : (
                  courses?.data?.map((course, idx) => (
                    <StyledTableRow key={course.id}>
                      <StyledTableCell>
                        <FormGroup>
                          <FormControlLabel
                            control={
                              <Checkbox
                                onChange={() =>
                                  handleCourseSelection(course.id)
                                }
                                checked={form.courses.includes(course.id)}
                              />
                            }
                            label={idx + 1}
                          />
                        </FormGroup>
                      </StyledTableCell>
                      <StyledTableCell>{course?.name}</StyledTableCell>
                      <StyledTableCell>{course?.code}</StyledTableCell>
                      <StyledTableCell>{course?.credit_value}</StyledTableCell>
                      <StyledTableCell>
                        {course?.lecturer?.first_name}{" "}
                        {course?.lecturer?.last_name}
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
      </ThemeProvider>

      <div className="w-full flex items-start gap-4">
        <p className="text-black font-normal text-sm">
          No of Selected Courses:{" "}
          <span className="font-bold text-red text-lg">
            {form.courses.length}
          </span>
        </p>
        <p className="text-black font-normal text-sm">
          Total Credit Value:{" "}
          <span className="font-bold text-red text-lg">{totalCreditValue}</span>
        </p>
      </div>
      <div className="w-full flex flex-col items-center justify-center">
        {isRegisteringCoursesError && (
          <code>
            Error Registering courses:{" "}
            {JSON.stringify(registerCoursesError.message)}
          </code>
        )}
        {isRegisteringCourses ? (
          <Spinner />
        ) : (
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={registerCourse}
          >
            Save Courses{" "}
          </Button>
        )}
      </div>
    </section>
  );
}

export default AddCourse;
