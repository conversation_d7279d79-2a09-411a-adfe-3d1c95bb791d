import {
  useGetAllAcademicYearsQuery,
  useGetAllLevelsQuery,
  useGetRegisteredCoursesQuery,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllCourses() {
  const { data: student } = useSelector((state) => state.auth);
  const { filter: stateFilter } = useSelector((state) => state.filter);
  const [query, setQuery] = useState(
    `student_id=${student?.student?.student_id}&academic_year_id=${student?.student?.academic_year?.id ?? 2}&level_id=${student?.student?.level?.level_id ?? 2}&semester=${student?.student?.semester ?? 1}`
  );
  const {
    data,
    isLoading,
    isError,
    isFetching: isCoursesFetching,
  } = useGetRegisteredCoursesQuery(query, "registered courses");
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(10);

  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("get all levels");
  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("get allademic years");

  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };

  const [filter, setFilter] = useState({
    school: "",
    dept: "",
    level: 1,
    semester: stateFilter.semester,
    year: stateFilter.academic_year,
  });

  const handleChange = (e) => {
    setFilter((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    setQuery(
      `student_id=${student?.student?.student_id}&academic_year_id=${filter.year}&level_id=${filter?.level}&semester=${filter?.semester}`
    );
  };

  const navigate = useNavigate();

  return (
    <section className="w-full flex flex-col items-center justify-between gap-8">
      {/* Registration Alert */}
      
      {/* Filter Bar */}
      <div className="w-full flex flex-col md:flex-row items-center justify-between bg-white shadow-lg p-6 rounded-xl gap-4 border border-gray-100 mb-">
        <div className="flex flex-row gap-4 w-full md:w-auto justify-between md:justify-start mt-5">
          <TextField
            select
            id="year"
            size="small"
            value={filter.year}
            label="Year"
            name="year"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {isAcademicYearsLoading ? (
              <Spinner size="14px" />
            ) : (
              academicYears?.data?.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name?.replace("_", "/")}
                </MenuItem>
              ))
            )}
          </TextField>
          <TextField
            select
            id="semester"
            size="small"
            value={filter.semester}
            label="Semester"
            name="semester"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {[1, 2].map((item) => (
              <MenuItem key={item} value={item}>
                {item}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            id="level"
            size="small"
            value={filter.level}
            label="Level"
            name="level"
            onChange={handleChange}
            sx={{ minWidth: 120, background: "#f3f4f6", borderRadius: 2 }}
          >
            {isLevelsLoading ? (
              <Spinner size="14px" />
            ) : (
              levels?.data.map((item) => (
                <MenuItem key={item.id} value={+item.id}>
                  {item.name}
                </MenuItem>
              ))
            )}
          </TextField>
        </div>
        <Button
          variant="contained"
          color="success"
          startIcon={<AddIcon />}
          sx={{
            textTransform: "capitalize",
            borderRadius: 2,
            fontWeight: 600,
            px: 3,
            boxShadow: "0 2px 8px rgba(34,197,94,0.08)",
          }}
          onClick={() => navigate("/student/courses/add")}
        >
          Add Course
        </Button>
      </div>
      {/* Registered Courses Header */}
      <div className="w-full flex items-center justify-between">
        <p className="text-gray-800 text-lg font-semibold">
          Registered Courses:{" "}
          {isLoading || isCoursesFetching ? (
            <Spinner size="20px" />
          ) : (
            data?.data?.courses?.length
          )}
        </p>
        <div className="flex gap-2">
          <span className="px-4 py-2 bg-green text-white rounded-full text-sm font-medium cursor-pointer transition-all duration-200 hover:shadow-md">
            All
          </span>
          <span className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full text-sm font-medium cursor-not-allowed">
            Dept
          </span>
          <span className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full text-sm font-medium cursor-not-allowed">
            Elect.
          </span>
        </div>
      </div>
      {/* Courses Table */}
      <ThemeProvider theme={theme}>
        <div className="flex flex-col w-full my-2">
          <TableContainer
            component={Paper}
            elevation={2}
            className="rounded-xl shadow-md"
          >
            <Table sx={{ maxWidth: "100%", fontFamily: "Inter, sans-serif" }}>
              <TableHead>
                <TableRow sx={{ background: "#f9fafb" }}>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Course</StyledTableCell>
                  <StyledTableCell>Course Code</StyledTableCell>
                  <StyledTableCell>Credit Value</StyledTableCell>
                  <StyledTableCell>Lecturer</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : isError ? (
                  <code>Error getting courses. Try refetching this page</code>
                ) : data?.data?.courses?.length < 1 ||
                  data?.data?.length < 1 ? (
                  <tr>
                    <td
                      colSpan={6}
                      className="text-center py-6 text-gray-400 text-lg"
                    >
                      No course available.
                    </td>
                  </tr>
                ) : (
                  data?.data?.courses?.map((course, idx) => (
                    <StyledTableRow
                      key={course.id}
                      className={
                        idx % 2 === 0
                          ? "bg-white transition-all duration-200 hover:bg-green/5"
                          : "bg-gray-50 transition-all duration-200 hover:bg-green/5"
                      }
                    >
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell className="font-semibold text-gray-800">
                        {course.name}
                      </StyledTableCell>
                      <StyledTableCell>{course?.code ?? "N/A"}</StyledTableCell>
                      <StyledTableCell>
                        {course?.credit_value ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {course?.lecturer?.first_name ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            sx={{
                              minWidth: 0,
                              px: 1.5,
                              borderRadius: 2,
                              fontWeight: 500,
                            }}
                            startIcon={
                              <DeleteOutlineOutlinedIcon fontSize="small" />
                            }
                            onClick={() => {}}
                          >
                            Remove
                          </Button>
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={count}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </ThemeProvider>
      {/* View Form B Button */}
      <div className="w-full flex items-center justify-center mt-4">
        <Button
          variant="contained"
          color="success"
          sx={{
            textTransform: "capitalize",
            borderRadius: 2,
            fontWeight: 600,
            px: 4,
            py: 1.5,
            fontSize: 16,
            boxShadow: "0 2px 8px rgba(34,197,94,0.08)",
          }}
          onClick={() => navigate("/student/courses/form-B")}
        >
          View Form B
        </Button>
      </div>
    </section>
  );
}

export default AllCourses;
