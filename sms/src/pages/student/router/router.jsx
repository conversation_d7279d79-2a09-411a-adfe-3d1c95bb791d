import { ErrorPageNotFound } from "@pages/error";
import {
  Account,
  AddCourse,
  AllCourses,
  CaResults,
  Courses,
  Feepayment,
  FinalResults,
  PayFee,
  Profile,
  Results,
  StudentDashboard,
  StudentForgotPassword,
  StudentLogin,
  StudentResetPassword,
  TransactionDetails,
  Transactions,
  Transcript,
  UpdateProfile,
  ViewFormB,
  ViewTranscript,
} from "@pages/student/components";

export const studentRoutes = [
  {
    index: true,
    element: <StudentDashboard />,
    errorElement: <ErrorPageNotFound />,
  },
  {
    element: <Courses />,
    path: "/student/courses",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllCourses />,
      },
      {
        element: <AddCourse />,
        path: "/student/courses/add",
      },
      {
        element: <ViewFormB />,
        path: "/student/courses/form-B",
      },
    ],
  },
  {
    element: <Feepayment />,
    path: "/student/fee-payment",
    children: [
      {
        index: true,
        element: <PayFee />,
      },
      {
        element: <Transactions />,
        path: "/student/fee-payment/transactions",
      },
      {
        element: <TransactionDetails />,
        path: "/student/fee-payment/transactions/:id",
      },
    ],
  },

  {
    element: <Results />,
    path: "/student/results",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <CaResults />,
      },
      {
        element: <CaResults />,
        path: "/student/results/ca-results",
      },
      {
        element: <FinalResults />,
        path: "/student/results/final-results",
      },
    ],
  },

  {
    element: <Transcript />,
    path: "/student/transcript",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <ViewTranscript />,
      },
      {
        element: <CaResults />,
        path: "/student/transcript/results/ca",
      },
      {
        element: <FinalResults />,
        path: "/student/transcript/results/final-results",
      },
    ],
  },

  {
    element: <Account />,
    path: "/student/profile",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <Profile />,
      },
      {
        element: <UpdateProfile />,
        path: "/student/profile/edit-profile",
      },
    ],
  },
];

export const studentAuthRoutes = [
  {
    element: <StudentLogin />,
    path: "/student/login",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <StudentForgotPassword />,
    path: "/student/forgot-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <StudentResetPassword />,
    path: "/student/reset-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
];
