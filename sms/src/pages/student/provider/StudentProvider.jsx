import PropTypes from "prop-types"; // Add this import
import { createContext, useReducer, useState } from "react";

export const StudentContext = createContext({});

const stepReducer = (state, action) => {
  switch (action.type) {
    case "makePayment":
      return { ...state, feePaymentStep: 0 };
    case "paymentMethod":
      return { ...state, feePaymentStep: 1 };
  }
};

function StudentProvider({ children }) {
  const [state, dispatch] = useReducer(stepReducer, {
    step: 0,
    feePaymentStep: 0,
  });

  const navToMakePayment = () => dispatch({ type: "makePayment" });
  const navToPaymentMethod = () => dispatch({ type: "paymentMethod" });
  const [academicYear, setAcademicYear] = useState(1);

  const value = {
    step: state.step,
    feePaymentStep: state.feePaymentStep,

    navToMakePayment,
    navToPaymentMethod,
    setAcademicYear,
    academicYear,
    // Add more state and actions as needed
  };
  return (
    <StudentContext.Provider value={value}>{children}</StudentContext.Provider>
  );
}

// Add PropTypes validation
StudentProvider.propTypes = {
  children: PropTypes.node.isRequired, // Add this line
};

export default StudentProvider;
