import { useGetFeeStatusMutation } from "@app";
import { useLoadSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import { useStudentContext } from "@pages/student/hooks/useStudentContext";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

function useGetFeeStatus() {
  const [studentFeeStatus, setStudentFeeStatus] = useState(null);
  useLoadSystemSettings();
  const { academicYear } = useStudentContext();
  const { user: student } = useSelector((state) => state.auth);
  const [checkFeeStatus, { isLoading: isCheckingFeestatus }] =
    useGetFeeStatusMutation("fee status");
  useEffect(() => {
    checkFeeStatusonComponentMount();
  }, [academicYear]);

  async function checkFeeStatusonComponentMount() {
    try {
      const data = await RequestInterceptor.handleRequest(
        () =>
          checkFeeStatus({
            academic_year_id: academicYear,
            student_id: student.student_id,
          }),
        { shouldAlert: false }
      );
      setStudentFeeStatus(data);
    } catch (error) {
      console.log("Error CHECKing fee: " + error);
    }
  }

  return { isCheckingFeestatus, studentFeeStatus };
}

export default useGetFeeStatus;
