import { useGetAllCalendarEventsQuery } from "@app";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";

function useGetCalendarEvents() {
  const system = useGetSystemSettings();
  const query = `academic_year=${system?.current_academic_year_name}`;

  const { data, isLoading, refetch } = useGetAllCalendarEventsQuery(query, {
    skip: !system?.current_academic_year_name,
    refetchOnMountOrArgChange: true,
  });

  return {
    calendarEvents: data?.data || [],
    isLoading,
    refetch,
  };
}

export default useGetCalendarEvents;
