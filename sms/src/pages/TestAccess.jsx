import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  But<PERSON>,
  Container,
  <PERSON>rid,
  Al<PERSON>,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const TestAccess = () => {
  const navigate = useNavigate();

  const testRoutes = [
    {
      title: 'Student LMS Features',
      routes: [
        { name: 'Course Materials', path: '/student/materials' },
        { name: 'Assignments', path: '/student/assignments' },
        { name: 'Assessments', path: '/student/assessments' },
        { name: 'Notifications', path: '/student/notifications' },
        { name: 'Student Dashboard', path: '/student' },
      ]
    },
    {
      title: 'Lecturer Portal',
      routes: [
        { name: 'Lecturer Dashboard', path: '/lecturer' },
        { name: 'My Courses', path: '/lecturer/courses' },
        { name: 'Manage Assignments', path: '/lecturer/assignments' },
        { name: 'Manage Assessments', path: '/lecturer/assessments' },
        { name: 'Student Analytics', path: '/lecturer/analytics' },
      ]
    },
    {
      title: 'Admin Portal',
      routes: [
        { name: 'Admin Dashboard', path: '/admin' },
        { name: 'User Management', path: '/admin/users' },
        { name: 'Course Management', path: '/admin/courses' },
      ]
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" fontWeight="bold" gutterBottom textAlign="center">
        🧪 Test Access Portal
      </Typography>
      
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="body1">
          <strong>Direct access to all LMS features for testing purposes.</strong><br/>
          Click any button below to navigate directly to that feature without authentication.
        </Typography>
      </Alert>

      <Grid container spacing={4}>
        {testRoutes.map((section, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h5" fontWeight="bold" gutterBottom color="primary">
                  {section.title}
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  {section.routes.map((route, routeIndex) => (
                    <Button
                      key={routeIndex}
                      variant="outlined"
                      fullWidth
                      onClick={() => navigate(route.path)}
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      {route.name}
                    </Button>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box textAlign="center" mt={4}>
        <Button
          variant="contained"
          size="large"
          onClick={() => navigate('/')}
        >
          ← Back to Home
        </Button>
      </Box>

      <Alert severity="warning" sx={{ mt: 4 }}>
        <Typography variant="body2">
          <strong>Note:</strong> This is a testing page. In production, all routes should be protected by authentication.
          Some features may not work properly without being logged in as the appropriate user type.
        </Typography>
      </Alert>
    </Container>
  );
};

export default TestAccess;
