import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import { useEffect, useState } from "react";

function useGetCalendarEvents() {
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const system = useGetSystemSettings();

  useEffect(() => {
    fetchCalendarEvents();
  }, [system?.current_academic_year_name, system?.current_semester]);

  async function fetchCalendarEvents() {
    try {
      setIsLoading(true);
      const response = await RequestInterceptor.handleRequest(
        () =>
          fetch(
            `/api/calendar-events?academic_year=${system?.current_academic_year_name}&semester=${
              system?.current_semester === 1 ? "First" : "Second"
            }`
          ).then((res) => res.json()),
        { shouldAlert: false }
      );
      setCalendarEvents(response.data || []);
    } catch (error) {
      console.error("Error fetching calendar events:", error);
    } finally {
      setIsLoading(false);
    }
  }

  return { calendarEvents, isLoading, refetch: fetchCalendarEvents };
}

export default useGetCalendarEvents;
