import {
  useGetAllCoursesQuery,
  useGetAllDepartmentsQuery,
  useGetAllLecturersQuery,
  useGetAllSchoolsQuery,
  useGetAllStudentsQuery,
} from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import {
  AccountBalanceOutlined,
  Add,
  Book,
  BookOutlined,
  MoreHorizOutlined,
  MoreHorizRounded,
  Person3Sharp,
  SchoolOutlined,
  SupervisorAccountOutlined,
} from "@mui/icons-material";
import { Box, Grid } from "@mui/material";
import Props from "@pages/Home/Props";
import BestStudents from "@pages/admin/components/charts/BestStudents";
import Calendar from "@pages/admin/components/charts/Calendar";
import Pie from "@pages/admin/components/charts/Pie";
import PropTypes from "prop-types"; // Add this import
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";

const StatCard = ({ icon, count, title, color }) => {
  const navigate = useNavigate();
  return (
    <div
      className={`w-full flex justify-center items-center gap-4 bg-${color} text-white py-8 rounded-[10px] cursor-pointer`}
      onClick={() => navigate(`/admin/${title}`)}
    >
      <div
        className={`flex items-center p-2 rounded-full bg-white text-${color}`}
      >
        {icon}
      </div>
      <div>
        <p className="text-lg font-bold">{count}</p>
        <p className="text-[10.5px]">{title}</p>
      </div>
    </div>
  );
};

StatCard.propTypes = {
  // Add prop types validation
  icon: PropTypes.element.isRequired,
  count: PropTypes.number.isRequired,
  title: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const Summary = () => {
  const currentYear = new Date().getFullYear();
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  const {
    data: departments,
    isLoading: isDepartmentLoading,
    status: departmentStatus,
  } = useGetAllDepartmentsQuery("departments");
  const {
    data: students,
    isLoading: isStudentLoading,
    status: studentStatus,
  } = useGetAllStudentsQuery("students");
  const {
    data: lecturers,
    isLoading: isLecturerLoading,
    status: lecturerStatus,
  } = useGetAllLecturersQuery("lecturers");
  const {
    data: courses,
    isLoading: isCourseLoading,
    status: courseStatus,
  } = useGetAllCoursesQuery("courses");

  const { data: schools, status: schoolstatus } =
    useGetAllSchoolsQuery("schools");

  const [stats, setStats] = useState([
    {
      icon: <SupervisorAccountOutlined />,
      color: "purple",
      count: students?.data?.length,
      title: "Students",
    },
    {
      icon: <SchoolOutlined />,
      color: "orange",
      count: lecturers?.data?.length,
      title: "Lecturers",
    },
    {
      icon: <AccountBalanceOutlined />,
      color: "blue",
      count: departments?.data?.length,
      title: "Departments",
    },
    {
      icon: <BookOutlined className="text-pink" />,
      color: "pink",
      count: courses?.data?.length,
      title: "Courses",
    },
    {
      icon: <SchoolOutlined />,
      color: "green",
      count: schools?.data?.length,
      title: "Schools",
    },
  ]);

  // this is bad; fix it
  useEffect(() => {
    setStats([
      {
        icon: <SupervisorAccountOutlined />,
        color: "purple",
        count: students?.data?.length,
        title: "Students",
      },
      {
        icon: <Person3Sharp />,
        color: "orange",
        count: lecturers?.data?.length,
        title: "Lecturers",
      },
      {
        icon: <SchoolOutlined />,
        color: "blue",
        count: departments?.data?.length,
        title: "Departments",
      },
      {
        icon: <Book />,
        color: "pink",
        count: courses?.data?.length,
        title: "Courses",
      },
      {
        icon: <SchoolOutlined />,
        color: "green",
        count: schools?.data?.length,
        title: "Schools",
      },
    ]);
  }, [
    departmentStatus,
    studentStatus,
    lecturerStatus,
    courseStatus,
    schoolstatus,
  ]);

  return (
    <div className="w-full">
      <Box className="w-full mt-2 flex px-2 items-center justify-between">
        <Grid>
          <Props header="Admin Dashboard" />
          <div
            className="bg-[#D60A0B] mb-3"
            style={{ width: "35px", height: "2px" }}
          ></div>
          <h6 className="font-bold">
            Welcome,{" "}
            <b className="text-[#186318]">
              {" "}
              {user?.first_name} {user?.last_name},
            </b>
          </h6>
          <span>Here&apos;s an overview of your institution.</span>
        </Grid>
        <Button
          type={"primary"}
          title={"New Admission"}
          onClick={() => navigate("/admin/students/add")}
        >
          <Add />
        </Button>
      </Box>
      {isCourseLoading ||
      isDepartmentLoading ||
      isLecturerLoading ||
      isStudentLoading ? (
        <Spinner />
      ) : (
        <div className="w-full grid md:grid-cols-5 grid-cols-2  gap-4 mt-5">
          {stats.map((stat) => (
            <StatCard {...stat} key={stat.title} />
          ))}
        </div>
      )}

      <div className="w-full mt-5 grid md:grid-cols-2 grid-cols-1 gap-4">
        <div className="bg-white drop-shadow-md w-full flex flex-col justify-center p-5 rounded-[10px]">
          <div className="w-full flex items-center justify-between gap-4">
            <select className="w-20 border" name="" id="">
              <option value="">{currentYear}</option>
            </select>
            <span className="text-[16px] font-semibold">Events Calendar</span>
            <MoreHorizRounded />
          </div>
          <Calendar />
        </div>

        <div className="bg-white w-full drop-shadow-md text-center rounded-[10px] p-5">
          <div>
            <div className="flex justify-between px-3">
              <h5 className="font-semibold">Top Performer</h5>
              <MoreHorizOutlined />
            </div>
          </div>
          <BestStudents />
          <Link
            to="/admin/results/statistics"
            className="text-sm font-semibold text-center text-blue cursor-pointer mt-5"
          >
            View Full Information
          </Link>
        </div>
        <div className="bg-white w-full drop-shadow-md rounded-[10px] p-5">
          <span className="text-[16px] font-semibold">Students</span>
          <div className="w-full flex justify-center items-center">
            <Pie />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Summary;
