import {
  useCreateBulkCalendarEventsMutation,
  useGetAllAcademicYearsQuery,
} from "@app";
import { Spinner } from "@components";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from "dayjs";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

const EVENT_TYPES = {
  CA: "Continuous Assessment",
  EO: "End of Lectures",
  SE: "Semester Examination",
  EX: "Extra-curricular Event",
  OT: "Other",
  PR: "Project/Presentation",
  OR: "Orientation",
  RE: "Resumption",
  GR: "Graduation",
  HO: "Holiday",
  ME: "Meeting",
};

function EventForm() {
  const navigate = useNavigate();
  const { id } = useParams();
  const system = useGetSystemSettings();
  const isEditMode = Boolean(id);
  const { data: academicYearsData, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("academicYears");
  const [createBulkEvents, { isLoading: isSaving, isError, error }] =
    useCreateBulkCalendarEventsMutation();

  const [events, setEvents] = useState([]);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    type: "CA",
    event_date: dayjs(),
    academic_year: system?.current_academic_year_name || "",
    semester: system?.current_semester === 1 ? "First" : "Second",
  });

  useEffect(() => {
    if (isEditMode) {
      fetchEvent();
    }
  }, [id]);

  const fetchEvent = async () => {
    try {
      const response = await RequestInterceptor.handleRequest(
        () => fetch(`/api/calendar-events/${id}`).then((res) => res.json()),
        { shouldAlert: false }
      );
      if (response.data) {
        setFormData({
          ...response.data,
          event_date: dayjs(response.data.event_date),
        });
      }
    } catch (error) {
      console.error("Error fetching event:", error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date) => {
    setFormData((prev) => ({ ...prev, event_date: date }));
  };

  const handleAddEvent = () => {
    if (!formData.title) return;
    setEvents((prev) => [
      ...prev,
      {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        event_date: formData.event_date.format("YYYY-MM-DD"),
        academic_year: formData.academic_year,
        semester: formData.semester,
      },
    ]);
    setFormData({
      title: "",
      description: "",
      type: "CA",
      event_date: dayjs(),
      academic_year: system?.current_academic_year_name || "",
      semester: system?.current_semester === 1 ? "First" : "Second",
    });
  };

  const handleRemoveEvent = (index) => {
    setEvents((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (events.length === 0) return;

    try {
      console.log("Submitting events:", events);
      const result = await createBulkEvents(events).unwrap();
      console.log("Save result:", result);
      navigate("/admin/calendar");
    } catch (error) {
      console.error("Error saving events:", error);
      if (error?.data?.errors) {
        // Show validation errors
        const errorMessages = Object.entries(error.data.errors)
          .map(([field, messages]) => `${field}: ${messages.join(", ")}`)
          .join("\n");
        alert(`Validation errors:\n${errorMessages}`);
      } else {
        alert(
          error?.data?.message || "Failed to save events. Please try again."
        );
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper className="p-6">
        <Typography variant="h5" className="mb-6 font-semibold">
          Add New Events
        </Typography>

        {isSaving && (
          <Box className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Box className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
              <Spinner size="24px" className="mb-4" />
              <Typography>Saving Events...</Typography>
            </Box>
          </Box>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <TextField
            fullWidth
            label="Event Title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
          />

          <TextField
            fullWidth
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            multiline
            rows={3}
          />

          <FormControl fullWidth>
            <InputLabel>Event Type</InputLabel>
            <Select
              name="type"
              value={formData.type}
              onChange={handleChange}
              label="Event Type"
              required
            >
              {Object.entries(EVENT_TYPES).map(([value, label]) => (
                <MenuItem key={value} value={value}>
                  {label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label="Event Date"
              value={formData.event_date}
              onChange={handleDateChange}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>

          <FormControl fullWidth>
            <InputLabel>Academic Year</InputLabel>
            <Select
              name="academic_year"
              value={formData.academic_year}
              onChange={handleChange}
              label="Academic Year"
              required
            >
              {isAcademicYearsLoading ? (
                <MenuItem value="">
                  <Spinner size="14px" />
                </MenuItem>
              ) : (
                academicYearsData?.data?.map((year) => (
                  <MenuItem key={year.id} value={year.name}>
                    {year.name}
                  </MenuItem>
                ))
              )}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Semester</InputLabel>
            <Select
              name="semester"
              value={formData.semester}
              onChange={handleChange}
              label="Semester"
              required
            >
              <MenuItem value="First">First Semester</MenuItem>
              <MenuItem value="Second">Second Semester</MenuItem>
              <MenuItem value="Third">Third Semester</MenuItem>
            </Select>
          </FormControl>

          <Box className="flex justify-end gap-4">
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddEvent}
              disabled={!formData.title || isSaving}
            >
              Add to List
            </Button>
          </Box>
        </form>

        {/* Events List */}
        {events.length > 0 && (
          <Box className="mt-8">
            <Typography variant="h6" className="mb-4">
              Events to be Added ({events.length})
            </Typography>
            <List>
              {events.map((event, index) => (
                <ListItem key={index} className="bg-gray-50 rounded-lg mb-2">
                  <ListItemText
                    primary={event.title}
                    secondary={`${EVENT_TYPES[event.type]} - ${dayjs(event.event_date).format("MMM D, YYYY")}`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleRemoveEvent(index)}
                      disabled={isSaving}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>

            <Box className="flex justify-end gap-4 mt-6">
              <Button
                variant="outlined"
                onClick={() => navigate("/admin/calendar")}
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={isSaving || events.length === 0}
                sx={{
                  background: "linear-gradient(to right, #22c55e, #16a34a)",
                  "&:hover": {
                    background: "linear-gradient(to right, #16a34a, #15803d)",
                  },
                }}
              >
                {isSaving ? (
                  <>
                    <Spinner size="14px" className="mr-2" />
                    Saving Events...
                  </>
                ) : (
                  "Save All Events"
                )}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </motion.div>
  );
}

export default EventForm;
