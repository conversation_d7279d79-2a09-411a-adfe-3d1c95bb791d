import Add from "@mui/icons-material/Add";
import { Button } from "@mui/material";
import { motion } from "framer-motion";
import { Outlet, useNavigate } from "react-router-dom";

function CalendarWrapper() {
  const navigate = useNavigate();

  return (
    <section className="min-h-screen w-full flex flex-col items-start mb-5">
      {/* Header Card */}
      <div className="w-full flex flex-col items-start justify-between gap-2 bg-white shadow-md rounded-b-xl p-6 border-b border-gray-100 mb-5">
        <div className="w-full flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              Calendar Events
            </h1>
            <p className="text-gray-500">Manage school calendar events</p>
          </div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate("/admin/calendar/add")}
              sx={{
                background: "linear-gradient(to right, #22c55e, #16a34a)",
                "&:hover": {
                  background: "linear-gradient(to right, #16a34a, #15803d)",
                },
              }}
            >
              Add Event
            </Button>
          </motion.div>
        </div>
      </div>
      <div className="w-full flex-1">
        <Outlet />
      </div>
    </section>
  );
}

export default CalendarWrapper;
