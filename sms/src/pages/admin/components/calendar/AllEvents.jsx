import {
  useDeleteCalendarEventMutation,
  useGetAllCalendarEventsQuery,
} from "@app";
import { Spinner } from "@components";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import SearchIcon from "@mui/icons-material/Search";
import {
  Button,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { motion } from "framer-motion";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AllEvents() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  const { data: calendarEventsData, isLoading } =
    useGetAllCalendarEventsQuery();
  const [deleteEvent] = useDeleteCalendarEventMutation();

  const calendarEvents = calendarEventsData?.data || [];

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleFilterChange = (event) => {
    setFilterType(event.target.value);
  };

  const handleDeleteClick = (event) => {
    setSelectedEvent(event);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteEvent(selectedEvent.id).unwrap();
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting event:", error);
    }
  };

  const filteredEvents = calendarEvents.filter((event) => {
    const matchesSearch =
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      new Date(event.event_date)
        .toLocaleDateString()
        .includes(searchQuery.toLowerCase());

    const matchesFilter =
      filterType === "all" ||
      event.type.toLowerCase() === filterType.toLowerCase();

    return matchesSearch && matchesFilter;
  });

  return (
    <div className="w-full space-y-6">
      {/* Search and Filter Bar */}
      <Card className="p-4">
        <Stack direction={{ xs: "column", sm: "row" }} spacing={2}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search events..."
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <TextField
              select
              label="Filter by Type"
              value={filterType}
              onChange={handleFilterChange}
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="CA">Continuous Assessment</MenuItem>
              <MenuItem value="EO">End of Lectures</MenuItem>
              <MenuItem value="SE">Semester Examination</MenuItem>
              <MenuItem value="EX">Extra-curricular Event</MenuItem>
              <MenuItem value="OT">Other</MenuItem>
              <MenuItem value="PR">Project/Presentation</MenuItem>
              <MenuItem value="OR">Orientation</MenuItem>
              <MenuItem value="RE">Resumption</MenuItem>
              <MenuItem value="GR">Graduation</MenuItem>
              <MenuItem value="HO">Holiday</MenuItem>
              <MenuItem value="ME">Meeting</MenuItem>
            </TextField>
          </FormControl>
        </Stack>
      </Card>

      {/* Events List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : filteredEvents.length === 0 ? (
          <Card className="p-8 text-center">
            <Typography color="text.secondary">
              No events found. Try adjusting your search or filters.
            </Typography>
          </Card>
        ) : (
          filteredEvents.map((event) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <Typography variant="h6" className="font-semibold">
                      {event.title}
                    </Typography>
                    <Typography color="text.secondary" variant="body2">
                      {event.description}
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      <Chip
                        label={event.type}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={new Date(event.event_date).toLocaleDateString()}
                        size="small"
                        color="success"
                        variant="outlined"
                      />
                      <Chip
                        label={event.academic_year}
                        size="small"
                        color="info"
                        variant="outlined"
                      />
                      <Chip
                        label={event.semester}
                        size="small"
                        color="warning"
                        variant="outlined"
                      />
                    </Stack>
                  </div>
                  <div className="flex gap-2">
                    <IconButton
                      size="small"
                      onClick={() =>
                        navigate(`/admin/calendar/edit/${event.id}`)
                      }
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteClick(event)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Event</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the event "{selectedEvent?.title}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default AllEvents;
