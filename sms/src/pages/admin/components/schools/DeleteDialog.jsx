import { useDeleteSchoolMutation } from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack } from "@mui/material";
import { useAdminContext } from "@pages/admin/hooks";
import PropTypes from "prop-types"; // Add this import

function DeleteDialog({ openModal, closeModal, id }) {
  const { navToAllSchools } = useAdminContext();
  const [deleteThisSchool, { isLoading }] =
    useDeleteSchoolMutation("delete-school");

  const deleteSchool = async () => {
    try {
      await RequestInterceptor.handleRequest(
        () => deleteThisSchool(id),
        {
          onSuccess: () => {
            closeModal();
            navToAllSchools();
          },
        },
        "Delete School"
      );
    } catch (error) {
      console.error("Error deleting school:", error);
    }
  };
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Are you sure you want to delete this School?
        </h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          by deleting this school, every information attatched to this school
          will be removed from the system
        </p>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"primary"} onClick={closeModal} />
          {isLoading ? (
            <Spinner size="20px" />
          ) : (
            <Button title={"Delete"} type={"danger"} onClick={deleteSchool} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add PropTypes validation
DeleteDialog.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  id: PropTypes.number.isRequired,
};

export default DeleteDialog;
