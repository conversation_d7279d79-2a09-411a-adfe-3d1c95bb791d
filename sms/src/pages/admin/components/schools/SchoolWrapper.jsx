import { AdminProvider } from "@pages/admin/providers";
import PropTypes from "prop-types"; // Add this import

const SchoolWrapper = ({ children }) => {
  return (
    <AdminProvider>
      <section className="min-h-screen w-full py-5 flex flex-col items-start">
        <div className="w-full items-start mb-3">
          <p className="text-black text-md font-semibold">Schools</p>
          <div className="w-[35px] h-[2px] bg-red"></div>
        </div>
        {children}
      </section>
    </AdminProvider>
  );
};

// Add prop types validation
SchoolWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Specify that children is required
};

export default SchoolWrapper;
