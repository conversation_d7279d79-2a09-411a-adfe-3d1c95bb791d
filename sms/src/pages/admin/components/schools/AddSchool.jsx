import { useCreateSchoolMutation, useGetAllProgramsQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Button,
  Checkbox,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { useAdminContext } from "@pages/admin/hooks";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddSchool() {
  const { navToUploadSchool } = useAdminContext();
  const { data: schoolPrograms = [], isLoading } =
    useGetAllProgramsQuery("progams");
  const [createSchool, { isLoading: isCreatingSchool }] =
    useCreateSchoolMutation("create-school");

  const [form, setForm] = useState({
    school: "",
    program: [],
    directorName: "",
    schoolEmail: "",
    contact: "",
    location: "",
  });

  const [logo, setLogo] = useState();
  const onChange = (e) => {
    const { name, value } = e.target;
    setForm((prevForm) => ({
      ...prevForm,
      [name]: value,
    }));
  };

  const navigate = useNavigate();

  /**
   * 
   *     {

    "name": "School Z",
    "school_email": "<EMAIL>",
    "director_name": null,
    "logo": "path/to/logo_a.png",
    "address": "123 School A Street",
    "programs": [
        "BSCS"
    ],
    "admin_id": 1
    } 
   */
  const addSchoolToDatabase = async (e) => {
    e.preventDefault();
    console.log(form);
    try {
      const formData = new FormData();
      formData.append("name", form.school);
      formData.append("school_email", form.schoolEmail);
      formData.append("director_name", form.directorName);
      formData.append("logo", logo);
      formData.append("address", form.location);
      form.program.forEach((program) => {
        formData.append("programs[]", program);
      });

      const { success, data } = await createSchool(formData).unwrap();
      console.log(data);
      if (success) {
        alert("School Created Successfully");
        setForm({
          school: "",
          program: [],
          directorName: "",
          schoolEmail: "",
          contact: "",
          location: "",
        });
        setLogo(null);
      }
    } catch (error) {
      console.log(error);

      alert("Error creating school");
    }
  };

  return (
    <div className="w-full flex flex-col  items-start md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col p-10 bg-white">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate("/admin/schools")} />
            </div>
            <p className="text-black font-semibold text-lg">Add School</p>
          </div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={navToUploadSchool}
          >
            Import Csv
          </Button>
        </div>
        <div className="w-full flex flex-col items-start">
          <form className="w-full" onSubmit={addSchoolToDatabase}>
            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="school"
                id="schoolName"
                label="School Name"
                value={form.school}
                fullWidth
                required
              />
              <FormControl fullWidth>
                <InputLabel id="program-label">Program</InputLabel>
                <Select
                  labelId="program-label"
                  id="program"
                  multiple
                  value={form.program}
                  onChange={onChange}
                  name="program"
                  renderValue={(selected) => selected.join(", ")}
                >
                  {isLoading ? (
                    <Spinner size="20px" />
                  ) : (
                    schoolPrograms?.data?.map((option) => (
                      <MenuItem key={option.id} value={option.abbreviation}>
                        <Checkbox
                          checked={
                            form.program.indexOf(option.abbreviation) > -1
                          }
                        />
                        <ListItemText primary={option.name} />
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Stack>

            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="directorName"
                id="directorName"
                label="Director Name"
                value={form.directorName}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                name="schoolEmail"
                id="schoolEmail"
                label="School Email"
                value={form.schoolEmail}
                fullWidth
                required
              />
            </Stack>

            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="contact"
                id="contact"
                label="Contact"
                value={form.contact}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                name="location"
                id="location"
                label="Location"
                value={form.location}
                fullWidth
                required
              />
            </Stack>
            <Stack
              direction={"row"}
              alignItems={"center"}
              sx={{ marginBottom: 2 }}
              gap={2}
            >
              <p className="">
                Add Logo <span className="text-green">(optional)</span>
              </p>
              <input
                type="file"
                placeholder="Add Logo"
                name="logo"
                accept="image/*"
                onChange={(e) => setLogo(e.target.files[0])}
              />
            </Stack>
            <div className="w-full flex">
              {isCreatingSchool ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  type="submit"
                >
                  Add School
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default AddSchool;
