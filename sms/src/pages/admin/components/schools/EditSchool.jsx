import { useGetSingleSchoolQuery, useUpdateSchoolMutation } from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function EditSchool() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [updateSchool, { isLoading: isUpdatingSchool }] =
    useUpdateSchoolMutation();
  const {
    data,
    isLoading: isSchoolLoading,
    status: schoolQueryStatus,
  } = useGetSingleSchoolQuery(id, "Get single school");
  const [form, setForm] = useState({
    school: "",
    directorName: "",
    schoolEmail: "",
    contact: "",
    location: "",
  });

  useEffect(() => {
    switch (schoolQueryStatus) {
      case "fulfilled":
        setForm({
          school: data?.data?.name,
          directorName: data?.data?.director_name,
          schoolEmail: data?.data?.school_email,
          contact: data?.data?.contact ?? "N/A",
          location: data?.data?.address,
        });
        break;
      case "pending":
        setForm(
          Object.fromEntries(
            Object.keys(form).map((key) => [key, "loading..."])
          )
        );
        break;
      default:
        break;
    }
  }, [schoolQueryStatus]);

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const submitForm = async (e) => {
    e.preventDefault();
    try {
      await RequestInterceptor.handleRequest(
        () =>
          updateSchool({
            id,
            body: {
              name: form.school,
              school_email: form.schoolEmail,
              address: form.location,
              director_name: form.directorName,
            },
          }),
        {},
        "Update School"
      );
    } catch (error) {
      console.error("Error updating school:", error);
    }
  };

  return (
    <div className="w-full flex flex-col items-start p-6 md:px-16 md:py-10">
      <div className="w-full bg-white shadow rounded-lg p-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate("/admin/schools")}
            className="p-2 bg-gray-100 rounded-full hover:bg-gray-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-700"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800">
            Edit School Information
          </h1>
        </div>

        {/* Form */}
        <form onSubmit={submitForm} className="space-y-6">
          {[
            { label: "School Name", name: "school", type: "text" },
            { label: "Director Name", name: "directorName", type: "text" },
            { label: "School Email", name: "schoolEmail", type: "email" },
            { label: "Contact", name: "contact", type: "text" },
            { label: "Location", name: "location", type: "text" },
          ].map(({ label, name, type }) => (
            <div key={name} className="flex items-center">
              <label
                htmlFor={name}
                className="w-1/4 text-sm font-medium text-gray-700"
              >
                {label}
              </label>
              <input
                type={type}
                name={name}
                id={name}
                value={form[name]}
                onChange={onChange}
                disabled={isSchoolLoading}
                className="w-3/4 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                required
              />
            </div>
          ))}

          {/* Submit Button */}
          <div className="flex justify-end">
            {isUpdatingSchool ? (
              <div className="w-full flex justify-center items-center">
                {" "}
                <Spinner />
              </div>
            ) : (
              <button
                type="submit"
                disabled={isUpdatingSchool || isSchoolLoading}
                className="px-6 py-2 bg-primary text-white font-semibold rounded-lg shadow hover:bg-primary disabled:opacity-50 w-full"
              >
                Update School
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default EditSchool;
