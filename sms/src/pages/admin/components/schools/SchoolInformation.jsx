import { useGetSingleSchoolQuery } from "@app";
import { Spinner } from "@components";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import CallIcon from "@mui/icons-material/Call";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EmailIcon from "@mui/icons-material/Email";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PersonIcon from "@mui/icons-material/Person";
import SchoolIcon from "@mui/icons-material/School";
import { Button, Stack, Tooltip } from "@mui/material";
import { DeleteDialog } from "@pages/admin/components/schools";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function SchoolInformation() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data, isLoading } = useGetSingleSchoolQuery(id, "Get single school");

  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  return (
    <div className="md:px-16 md:py-10 w-full flex flex-col items-start justify-between">
      <div className="bg-white p-10 w-full flex flex-col items-start justify-between">
        <div className="w-full flex items-start justify-between  mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full cursor-pointer">
              <ArrowBackIcon onClick={() => navigate(-1, { replace: true })} />
            </div>
            <p className="text-black font-semibold text-lg">
              School Information
            </p>
          </div>
          <Stack direction="row" gap={2}>
            <Button
              variant="contained"
              startIcon={<EditOutlinedIcon />}
              color="success"
              style={{ textTransform: "capitalize" }}
              onClick={() => navigate(`/admin/schools/${id}/edit`)}
            >
              Edit
            </Button>
            <Button
              variant="contained"
              startIcon={<DeleteOutlineOutlinedIcon />}
              color="error"
              style={{ textTransform: "capitalize" }}
              onClick={() => setOpenSuccessModal(true)}
            >
              Delete
            </Button>
          </Stack>
        </div>

        {isLoading ? (
          <Spinner />
        ) : (
          <div className="w-full flex flex-col items-start">
            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-4">
                <AccountBalanceIcon />
                <p className="text-black font-medium text-sm">School Name</p>
              </div>
              <p className="text-black font-semibold text-xl">
                {data?.data?.name ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-2">
                <SchoolIcon />
                <p className="text-black font-medium text-sm">Programs</p>
              </div>
              <ul className="text-black font-semibold text-xl">
                {data?.data?.programs?.length > 0
                  ? data?.data?.programs?.map((program, idx) => (
                      <li key={program?.id}>
                        <Tooltip title={program?.description ?? "N/A"}>
                          {idx + 1}. {program?.abbreviation ?? "N/A"}{" "}
                          {`(${program?.name ?? "N/A"})`}
                        </Tooltip>
                      </li>
                    ))
                  : "N/A"}
              </ul>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-2">
                <PersonIcon />
                <p className="text-black font-medium text-sm">Director Name</p>
              </div>
              <p className="text-black font-semibold text-xl">
                {data?.data?.director_name ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-2">
                <EmailIcon />
                <p className="text-black font-medium text-sm">Email</p>
              </div>
              <p className="text-black font-semibold text-xl">
                {data?.data?.school_email ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-2">
                <CallIcon />
                <p className="text-black font-medium text-sm">Contact</p>
              </div>
              <p className="text-black font-semibold text-xl">
                {data?.data?.contact ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <div className="flex items-center gap-2 mb-2">
                <LocationOnIcon />
                <p className="text-black font-medium text-sm">Location</p>
              </div>
              <p className="text-black font-semibold text-xl">
                {data?.data?.address ?? "N/A"}
              </p>
            </div>
          </div>
        )}

        {openSuccessModal && (
          <DeleteDialog
            id={id}
            openModal={openSuccessModal}
            closeModal={() => setOpenSuccessModal(false)}
          />
        )}
      </div>
    </div>
  );
}

export default SchoolInformation;
