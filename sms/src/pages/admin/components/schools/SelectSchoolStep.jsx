import {
  AddSchool,
  AllSchools,
  EditSchool,
  UploadSchool,
} from "@pages/admin/components/schools";
import PropTypes from "prop-types"; // Import PropTypes

function SelectSchoolStep({ step }) {
  switch (step) {
    case 0:
      return <AllSchools />;
    case 1:
      return <AddSchool />;
    case 2:
      return <EditSchool />;
    case 3:
      return <UploadSchool />;

    default:
      throw new Error("Process was not informed. Include in case list.");
  }
}

// Add prop types validation
SelectSchoolStep.propTypes = {
  step: PropTypes.number.isRequired, // Validate that step is a required number
};

export default SelectSchoolStep;
