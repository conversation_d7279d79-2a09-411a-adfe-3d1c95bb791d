import { useGetAllSchoolsQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { DeleteDialog } from "@pages/admin/components/schools";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllSchools() {
  const [filter, setFilter] = useState("Director"); // Active filter
  const [searchQuery, setSearchQuery] = useState(""); // Search query
  const { data, isLoading } = useGetAllSchoolsQuery("all-schools");
  const [page, setPage] = useState(1);
  const [idToDelete, setIdToDelete] = useState(null);
  const [openSuccessModal, setOpenSuccessModal] = useState(false);

  const navigate = useNavigate();

  // Handle search input change
  const handleSearchChange = (e) => setSearchQuery(e.target.value);

  // Handle filter change
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery(""); // Clear search query when changing filters
  };

  // Filtered schools based on the active filter and search query
  const filteredSchools = data?.data?.filter((school) => {
    const valueToFilter =
      filter === "Director"
        ? school.director_name
        : filter === "Email"
          ? school.school_email
          : school.contact;
    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex flex-col items-start h-full">
        {/* Header */}
        <div className="w-full flex items-start justify-between mb-4">
          <p className="text-black text-lg font-semibold mb-4">All Schools</p>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate("/admin/schools/create")}
          >
            Add School
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            {/* Search Input */}
            <Box>
              <TextField
                fullWidth
                size="small"
                label={`Search by ${filter}`}
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment className="cursor-pointer hover:text-green">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            {/* Filter Dropdown */}
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  onChange={handleFilterChange}
                >
                  <MenuItem value="Director">Director</MenuItem>
                  <MenuItem value="Email">Email</MenuItem>
                  <MenuItem value="Contact">Contact</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>
          {/* Export and Import */}
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={() => console.log("Import CSV")}
              >
                Import CSV
              </Button>
            </Stack>
          </div>
        </div>

        {/* Table */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Name</StyledTableCell>
                  <StyledTableCell>Director</StyledTableCell>
                  <StyledTableCell>Email</StyledTableCell>
                  <StyledTableCell>Contact</StyledTableCell>
                  <StyledTableCell>Location</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : filteredSchools?.length < 1 ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={7} align="center">
                      No schools found
                    </StyledTableCell>
                  </StyledTableRow>
                ) : (
                  filteredSchools?.map((school, idx) => (
                    <StyledTableRow key={school.id}>
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell>{school?.name ?? "N/A"}</StyledTableCell>
                      <StyledTableCell>
                        {school?.director_name ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {school?.school_email ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {school?.contact ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {school?.address ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <Link to={`/admin/schools/${school.id}`}>
                            <VisibilityIcon
                              color="primary"
                              sx={{ cursor: "pointer" }}
                            />
                          </Link>
                          <EditOutlinedIcon
                            color="success"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(`/admin/schools/${school.id}/edit`)
                            }
                          />
                          <DeleteOutlineOutlinedIcon
                            color="error"
                            sx={{ cursor: "pointer" }}
                            onClick={() => {
                              setIdToDelete(school.id);
                              setOpenSuccessModal(true);
                            }}
                          />
                        </div>
                        {openSuccessModal && (
                          <DeleteDialog
                            openModal={openSuccessModal}
                            closeModal={() => setOpenSuccessModal(false)}
                            id={idToDelete}
                          />
                        )}
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={Math.ceil(data?.data?.length / 10) || 0}
                    page={page}
                    onChange={(event, value) => setPage(value)}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllSchools;
