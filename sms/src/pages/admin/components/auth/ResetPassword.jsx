import Image from "@assets/images/reset-password.svg";
import Logo from "@assets/images/logo.jpg";

import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import IconButton from "@mui/material/IconButton";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import VisibilityIcon from "@mui/icons-material/Visibility";

import Box from "@mui/material/Box";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Modal from "@mui/material/Modal";
import Button from "@mui/material/Button";
import { useState } from "react";

function ResetPassword() {
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    maxWidth: 500,
    bgcolor: "background.paper",
    borderRadius: "10px",
    boxShadow: 24,
    p: 4,
  };
  return (
    <section className="h-screen flex items-start">
      <div className="w-full h-full p-5 md:py-10 md:px-10  flex flex-col items-center justify-between">
        {/* Logo */}
        <div className="w-full flex items-center gap-2">
          <img src={Logo} alt="" className="w-[40px]" />
          <p className="font-semibold text-primary text-normal">EHIST</p>
        </div>
        {/* Forgot Password form and illustration */}
        <div className="w-full md:px-16 flex ">
          <div className="md:flex w-1/2 hidden h-full">
            <img src={Image} alt="" className="w-[460px] h-full object-cover" />
          </div>
          <div className="lg:w-1/2 w-full h-full p-10 md:px-16 md:py-10 flex flex-col gap-4 items-center justify-center ">
            <h2 className="text-black font-bold text-3xl">Reset Password?</h2>
            <p className="text-secondary text-sm text-center  ">
              Enter a new password. Make sure to use strong password containing
              special characters and not less than 8 words
            </p>

            <form className="w-full flex flex-col gap-4">
              <TextField
                fullWidth
                label="New Password"
                value={password}
                onChange={handlePasswordChange}
                type={showPassword ? "text" : "password"}
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                fullWidth
                label="Confirm Password"
                value={password}
                onChange={handlePasswordChange}
                type={showPassword ? "text" : "password"}
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </form>
            <Button
              variant="contained"
              fullWidth
              color="success"
              size="large"
              style={{ textTransform: "capitalize" }}
              onClick={handleOpen}
            >
              Reset Password
            </Button>
          </div>
        </div>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <div className="w-full flex flex-col items-center justify-evenly text-center gap-3">
              <CheckCircleIcon color="success" sx={{ fontSize: 60 }} />
              <p className="font-semibold">Password Reset Successful</p>
              <p className="text-sm text-secondary">
                Your new password has been changed successfully
              </p>
              <Button
                variant="contained"
                color="success"
                size="medium"
                style={{ textTransform: "capitalize" }}
              >
                Login
              </Button>
            </div>
          </Box>
        </Modal>

        {/* Footer */}
        <div>
          <p className="text-sm text-secondary">
            &copy; 2023 Copyright
            <span className="text-primary">Skye8.tech</span>
          </p>
        </div>
      </div>
    </section>
  );
}

export default ResetPassword;
