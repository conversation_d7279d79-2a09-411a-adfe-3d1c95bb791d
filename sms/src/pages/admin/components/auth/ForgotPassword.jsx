import React from 'react';
import ForgotPassword from '@components/auth/ForgotPassword';
import { useAdminForgotPasswordMutation } from '@app';

function AdminForgotPassword() {
  return (
    <ForgotPassword
      role="admin"
      useForgotPasswordMutation={useAdminForgotPasswordMutation}
      title="Admin Password Reset"
      subtitle="Enter your admin email address and we'll send you a secure link to reset your password."
      loginPath="/admin/login"
    />
  );
}

export default AdminForgotPassword;
