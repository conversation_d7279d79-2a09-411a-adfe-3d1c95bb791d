import Image from "@assets/images/forgot-password.svg";
import Logo from "@assets/images/logo.jpg";
import EmailIcon from "@mui/icons-material/Email";
import Button from "@mui/material/Button";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import { motion } from "framer-motion";
import { useState } from "react";
import { Link } from "react-router-dom";
import { toast } from "react-toastify";

function ForgotPassword() {
  const [email, setEmail] = useState("");

  const handleEmailChange = (event) => {
    setEmail(event.target.value);
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    // Handle password reset logic here
    toast.success("Password reset instructions sent to your email");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form Section */}
      <motion.div
        className="flex-1 flex items-center justify-center p-8"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-12">
            <motion.div
              className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </motion.div>
            <motion.h2
              className="text-3xl font-bold text-gray-900 mb-3"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              Forgot Password?
            </motion.h2>
            <motion.p
              className="text-base text-gray-600"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              Don't worry! We'll help you recover your account
            </motion.p>
          </div>

          {/* Reset Form */}
          <motion.div
            className="bg-white p-8 rounded-2xl shadow-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <TextField
                  fullWidth
                  type="email"
                  value={email}
                  onChange={handleEmailChange}
                  variant="outlined"
                  placeholder="Enter your email address"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon className="text-gray-400" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "12px",
                      "&:hover fieldset": {
                        borderColor: "#22c55e",
                      },
                    },
                  }}
                />
                <p className="text-sm text-gray-500 mt-2">
                  We'll send you instructions to reset your password
                </p>
              </div>

              <Button
                fullWidth
                size="large"
                variant="contained"
                type="submit"
                sx={{
                  bgcolor: "#22c55e",
                  textTransform: "none",
                  fontSize: "1rem",
                  py: 2,
                  borderRadius: "12px",
                  boxShadow: "0 4px 6px -1px rgb(34 197 94 / 0.2)",
                  "&:hover": {
                    bgcolor: "#16a34a",
                    boxShadow: "0 10px 15px -3px rgb(34 197 94 / 0.3)",
                  },
                }}
              >
                Send Reset Instructions
              </Button>

              <div className="text-center mt-6">
                <Link
                  to="/admin/login"
                  className="inline-flex items-center text-sm font-medium text-green hover:text-green/80 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Login
                </Link>
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>

      {/* Right Side - Image Section */}
      <motion.div
        className="hidden lg:block w-1/2 relative"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green/60 mix-blend-multiply" />
        <img
          src={Image}
          alt="Forgot Password"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex flex-col justify-center p-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-lg">
            <motion.h1
              className="text-4xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              Password Recovery
            </motion.h1>
            <motion.p
              className="text-lg text-white/90"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              Don't worry about forgetting your password. We've got you covered
              with our secure password recovery process.
            </motion.p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default ForgotPassword;
