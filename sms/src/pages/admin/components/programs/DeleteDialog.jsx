import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { Stack } from "@mui/material";
import PropTypes from "prop-types";

function DeleteDialog({ openModal, closeModal }) {
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Are you sure you want to delete this Program?
        </h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          by deleting this pogram, every information attatched to this program
          will be removed from the system
        </p>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"primary"} onClick={closeModal} />
          <Button title={"Delete"} type={"danger"} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add PropTypes validation
DeleteDialog.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
};

export default DeleteDialog;
