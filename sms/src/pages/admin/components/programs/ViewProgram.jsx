import { useGetProgramByIdQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import CollectionsBookmarkOutlinedIcon from "@mui/icons-material/CollectionsBookmarkOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { Button, Stack } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";

function ViewProgram() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data, isLoading, isError } = useGetProgramByIdQuery(id, "getProgram");
  return (
    <div className="w-full flex flex-col  items-start md:px-16 md:py-10 py-5 px-8 justify-betwee">
      <div className="w-full flex flex-col p-10 bg-white gap-4">
        <div className="w-full flex items-start justify-between  mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate("/admin/programs")} />
            </div>
            <p className="text-black font-semibold text-lg">View Program</p>
          </div>
          <Stack direction="row" gap={2}>
            <Button
              variant="contained"
              startIcon={<EditOutlinedIcon />}
              color="success"
              style={{ textTransform: "capitalize" }}
              onClick={() => navigate(`/admin/programs/${id}/edit`)}
            >
              Edit
            </Button>
            <Button
              variant="contained"
              startIcon={<DeleteOutlineOutlinedIcon />}
              color="error"
              style={{ textTransform: "capitalize" }}
              onClick={() => true}
            >
              Delete
            </Button>
          </Stack>
        </div>

        {isLoading ? (
          <Spinner />
        ) : isError ? (
          <code>Error Getting Program details</code>
        ) : (
          <div className="w-full flex flex-col items-start gap-3">
            <div className="w-full flex gap-2">
              <CollectionsBookmarkOutlinedIcon />
              <p className="text-md font-medium text-black">
                {data?.data?.name ?? "N/A"} ({data?.data?.abbreviation ?? "N/A"}
                )
              </p>
            </div>
            <p className="text-black text-md font-medium flex gap-1 items-center">
              <span className="text-xs font-normal text-black">Degree:</span>
              {data?.data?.degree ?? "N/A"}
            </p>
            <p className="text-black text-md font-medium flex gap-1 items-center">
              <span className="text-xs font-normal text-black mr-2">
                Duration:
              </span>{" "}
              {data?.data?.duration ?? "N/A"} {""}
              years
            </p>
            <p className="text-black text-md font-medium">
              <span className="text-xs font-normal text-black">Cost: </span>
              {data?.data?.cost ?? "N/A"} xaf
            </p>

            <div className="w-full flex flex-col gap-2">
              <p className="text-xs font-normal text-black">Description</p>
              <p className="text-md font-sm text-grey_500">
                {data?.data?.description ?? "N/A"}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ViewProgram;
