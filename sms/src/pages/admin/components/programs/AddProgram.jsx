import { useCreateProgramMutation, useGetAllSchoolsQuery } from "@app";
import { Spinner } from "@components";
import { Search } from "@mui/icons-material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddProgram() {
  const navigate = useNavigate();
  const [createProgram, { isLoading: isCreatingProgram }] =
    useCreateProgramMutation("add-program");
  const {
    data: schoolsData,
    isLoading: isSchoolsLoading,
    isError,
  } = useGetAllSchoolsQuery("all-schools");

  const [form, setForm] = useState({
    programName: "",
    degree: "",
    school: "",
    duration: "",
    cost: "",
    desc: "",
    abbreviation: "",
  });

  const [schoolSearch, setSchoolSearch] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const filteredSchools =
    schoolsData?.data?.filter((school) =>
      school.name.toLowerCase().includes(schoolSearch.toLowerCase())
    ) || [];

  const onChange = (e) => {
    const { name, value } = e.target;
    if (name === "school") {
      setSchoolSearch("");
      setIsSearching(false);
    }
    setForm({ ...form, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { success, message } = await createProgram({
        name: form.programName,
        degree: form.degree,
        school_id: form.school,
        duration: form.duration,
        cost: form.cost,
        description: form.desc,
        abbreviation: form.abbreviation,
      }).unwrap();
      if (success) {
        alert(message);
        navigate(-1);
      }
    } catch (error) {
      alert(error?.message ?? "Error creating program");
    }
  };

  return (
    <div className="w-full flex flex-col items-start px-8 py-5 md:px-16 md:py-10">
      <div className="w-full bg-white shadow-md rounded-lg p-6">
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2 bg-gray-100 rounded-full hover:bg-gray-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-700"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800">Add Program</h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="w-full relative">
            <label
              htmlFor="school"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              School
            </label>
            <div className="relative">
              {isSearching ? (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    value={schoolSearch}
                    onChange={(e) => setSchoolSearch(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    placeholder="Search schools..."
                    autoFocus
                  />
                </div>
              ) : (
                <div
                  className="relative cursor-text"
                  onClick={() => setIsSearching(true)}
                >
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <div className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md shadow-sm">
                    {form.school
                      ? schoolsData?.data?.find((s) => s.id === form.school)
                          ?.name
                      : "Select a school..."}
                  </div>
                </div>
              )}
              {isSearching && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                  {isSchoolsLoading ? (
                    <div className="p-2 text-gray-500">Loading schools...</div>
                  ) : isError ? (
                    <div className="p-2 text-red-500">
                      Error fetching schools
                    </div>
                  ) : filteredSchools.length === 0 ? (
                    <div className="p-2 text-gray-500">No schools found</div>
                  ) : (
                    filteredSchools.map((school) => (
                      <div
                        key={school.id}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          onChange({
                            target: { name: "school", value: school.id },
                          });
                        }}
                      >
                        {school.name}
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="w-full">
            <label
              htmlFor="programName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Program Name
            </label>
            <input
              type="text"
              id="programName"
              name="programName"
              value={form.programName}
              onChange={onChange}
              required
              className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
            />
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="abbreviation"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Abbreviation
              </label>
              <input
                type="text"
                id="abbreviation"
                name="abbreviation"
                value={form.abbreviation}
                onChange={onChange}
                placeholder="e.g., BSc"
                className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              />
            </div>
            <div>
              <label
                htmlFor="degree"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Degree
              </label>
              <input
                type="text"
                id="degree"
                name="degree"
                value={form.degree}
                onChange={onChange}
                required
                className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="duration"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Duration (Years)
              </label>
              <input
                type="number"
                id="duration"
                name="duration"
                value={form.duration}
                onChange={onChange}
                placeholder="e.g., 4"
                className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              />
            </div>
            <div>
              <label
                htmlFor="cost"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Cost
              </label>
              <input
                type="number"
                id="cost"
                name="cost"
                value={form.cost}
                onChange={onChange}
                required
                className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              />
            </div>
          </div>

          <div className="w-full">
            <label
              htmlFor="desc"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Description
            </label>
            <textarea
              id="desc"
              name="desc"
              rows="4"
              value={form.desc}
              onChange={onChange}
              placeholder="Program description..."
              className="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
            ></textarea>
          </div>

          <div className="w-full flex justify-end">
            {isCreatingProgram ? (
              <div className="w-full flex items-center justify-center">
                <Spinner />
              </div>
            ) : (
              <button
                type="submit"
                disabled={isCreatingProgram || isSchoolsLoading}
                className="px-6 py-2 bg-primary w-full text-white font-semibold rounded-lg shadow hover:bg-primary disabled:opacity-50"
              >
                Add Program
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default AddProgram;
