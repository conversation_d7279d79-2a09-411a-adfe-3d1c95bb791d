import { useGetAllProgramsQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { DeleteDialog } from "@pages/admin/components/programs";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllPrograms() {
  const [filter, setFilter] = useState("Name"); // Active filter (Name, Degree, Cost)
  const [searchQuery, setSearchQuery] = useState(""); // Search query
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const { data, isLoading, isError } = useGetAllProgramsQuery("allPrograms");

  const [page, setPage] = useState(1);
  const navigate = useNavigate();

  // Handle search input change
  const handleSearchChange = (e) => setSearchQuery(e.target.value);

  // Handle filter change
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery(""); // Clear search query when changing filters
  };

  // Handle page change
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
  };

  // Filtered programs based on the active filter and search query
  const filteredPrograms = data?.data?.filter((program) => {
    const valueToFilter =
      filter === "Name"
        ? program.name
        : filter === "Degree"
          ? program.abbreviation
          : program.cost.toString();
    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex flex-col items-center justify-center">
        <div className="w-full flex flex-col items-start h-full">
          <div className="flex justify-between w-full items-center my-2">
            <p className="text-black text-lg font-semibold mb-4">
              All Programs
            </p>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              color="success"
              style={{ textTransform: "capitalize" }}
              onClick={() => navigate("/admin/programs/upload")}
            >
              Upload CSV File
            </Button>
          </div>
          <div className="w-full flex items-start justify-between mb-4 gap-3">
            <div className="w-full flex items-start gap-2">
              {/* Search Input */}
              <Box>
                <TextField
                  fullWidth
                  size="small"
                  label={`Search by ${filter}`}
                  value={searchQuery}
                  onChange={handleSearchChange}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment>
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              {/* Filter Dropdown */}
              <Box sx={{ minWidth: 120 }}>
                <FormControl fullWidth>
                  <TextField
                    select
                    id="filter"
                    size="small"
                    value={filter}
                    label="Filter"
                    name="filter"
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="Name">Name</MenuItem>
                    <MenuItem value="Degree">Degree</MenuItem>
                    <MenuItem value="Cost">Cost</MenuItem>
                  </TextField>
                </FormControl>
              </Box>
            </div>
            <div>
              <div className="w-full flex gap-2">
                <Button
                  variant="outlined"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Export
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  onClick={() => navigate("/admin/programs/add")}
                >
                  Program
                </Button>
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Name</StyledTableCell>
                    <StyledTableCell>Degree</StyledTableCell>
                    <StyledTableCell>Cost</StyledTableCell>
                    <StyledTableCell>Duration</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    <Spinner />
                  ) : isError ? (
                    <code>Error Fetching Data</code>
                  ) : filteredPrograms?.length < 1 ? (
                    <StyledTableRow>
                      <StyledTableCell colSpan={6} align="center">
                        No programs found
                      </StyledTableCell>
                    </StyledTableRow>
                  ) : (
                    filteredPrograms?.map((program, idx) => (
                      <StyledTableRow key={program.id}>
                        <StyledTableCell>
                          {(page - 1) * 10 + idx + 1}
                        </StyledTableCell>
                        <StyledTableCell>
                          {program?.name ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {program?.abbreviation ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {program.cost ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {program.duration ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div className="flex items-center gap-3">
                            <Link to={`/admin/programs/${program.id}`}>
                              <VisibilityIcon
                                color="primary"
                                sx={{ cursor: "pointer" }}
                              />
                            </Link>
                            <EditOutlinedIcon
                              color="success"
                              sx={{ cursor: "pointer" }}
                              onClick={() =>
                                navigate(`/admin/programs/${program.id}/edit`)
                              }
                            />
                            <DeleteOutlineOutlinedIcon
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() => setOpenSuccessModal(true)}
                            />
                          </div>
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              <Grid
                display={"flex"}
                alignItems={"center"}
                justifyContent={"flex-start"}
                my={3}
              >
                <Box>
                  <Stack spacing={2}>
                    <Pagination
                      count={Math.ceil(data?.data?.length / 10) || 0}
                      page={page}
                      onChange={handleTableChange}
                      showFirstButton
                      showLastButton
                      color="green"
                    />
                  </Stack>
                </Box>
              </Grid>
            </TableContainer>
          </div>

          {/* Delete Dialog */}
          {openSuccessModal && (
            <DeleteDialog
              openModal={openSuccessModal}
              closeModal={() => setOpenSuccessModal(false)}
            />
          )}
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllPrograms;
