import PropTypes from "prop-types";

function ProgramWrapper({ children }) {
  return (
    <section className="min-h-screen w-full py-5 flex flex-col items-start">
      <div className="w-full items-start mb-3">
        <p className="text-black text-md font-semibold">Programs</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>

      {children}
    </section>
  );
}

ProgramWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ProgramWrapper;
