import {
  useGetAllAcademicYearsQuery,
  useGetAllLevelsQuery,
  useUpdateSystemSettingsMutation,
} from "@app";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import { useEffect, useState } from "react";

const SettingsPage = () => {
  const { systemSettings, isLoading: isSystemLoading } = useGetSystemSettings();
  const { data: academicYears } = useGetAllAcademicYearsQuery();
  const { data: levels } = useGetAllLevelsQuery();

  const [selectedKey, setSelectedKey] = useState("");
  const [availableValues, setAvailableValues] = useState([]);
  const [selectedValue, setSelectedValue] = useState("");
  const [updateSettings, { isLoading: isUpdating }] =
    useUpdateSystemSettingsMutation();

  const settingsKeys = [
    { key: "current_academic_year_id", label: "Academic Year" },
    { key: "current_level", label: "Level" },
    { key: "current_semester", label: "Semester" },
  ];

  useEffect(() => {
    if (selectedKey === "current_academic_year_id") {
      setAvailableValues(academicYears?.data || []);
    } else if (selectedKey === "current_level") {
      setAvailableValues(levels?.data || []);
    } else if (selectedKey === "current_semester") {
      setAvailableValues([
        { id: "1", name: "First Semester" },
        { id: "2", name: "Second Semester" },
      ]);
    }
    setSelectedValue("");
  }, [selectedKey, academicYears, levels]);

  const handleUpdateSettings = async () => {
    try {
      await RequestInterceptor.handleRequest(
        () => updateSettings({ key: selectedKey, value: selectedValue }),
        {},
        ""
      );
    } catch (error) {
      alert("Error updating system setting.");
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          System Settings
        </h1>

        <div className="space-y-6">
          {/* Select Setting Key */}
          <div className="space-y-2">
            <label
              htmlFor="settingKey"
              className="block text-sm font-medium text-gray-700"
            >
              Select Setting to Update
            </label>
            <select
              id="settingKey"
              value={selectedKey}
              onChange={(e) => setSelectedKey(e.target.value)}
              disabled={isSystemLoading || isUpdating}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="">Select a setting</option>
              {settingsKeys.map((setting) => (
                <option key={setting.key} value={setting.key}>
                  {setting.label}
                </option>
              ))}
            </select>
          </div>

          {/* Select Value */}
          <div className="space-y-2">
            <label
              htmlFor="settingValue"
              className="block text-sm font-medium text-gray-700"
            >
              Select Value for{" "}
              {settingsKeys.find((s) => s.key === selectedKey)?.label ||
                "Setting"}
            </label>
            <select
              id="settingValue"
              value={selectedValue}
              onChange={(e) => setSelectedValue(e.target.value)}
              disabled={isSystemLoading || isUpdating || !selectedKey}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="">Select a value</option>
              {availableValues.map((value) => (
                <option key={value.id} value={value.id}>
                  {value.name}
                </option>
              ))}
            </select>
          </div>

          {/* Update Button */}
          <div className="flex justify-end">
            <button
              onClick={handleUpdateSettings}
              disabled={
                isSystemLoading || isUpdating || !selectedKey || !selectedValue
              }
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {isUpdating ? (
                <span className="inline-flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Updating...
                </span>
              ) : (
                "Update Setting"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
