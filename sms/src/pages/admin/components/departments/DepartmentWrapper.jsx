import { AdminProvider } from "@pages/admin/providers";
import PropTypes from "prop-types";

function DepartmentWrapper({ children }) {
  return (
    <AdminProvider>
      <section className="min-h-screen w-full flex py-5 flex-col items-start">
        <div className="w-full items-start mb-2">
          <div className="w-full items-start mb-3">
            <p className="text-black text-md font-semibold">Departments</p>
            <div className="w-[35px] h-[2px] bg-red"></div>
          </div>
        </div>
        {children}
      </section>
    </AdminProvider>
  );
}

DepartmentWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default DepartmentWrapper;
