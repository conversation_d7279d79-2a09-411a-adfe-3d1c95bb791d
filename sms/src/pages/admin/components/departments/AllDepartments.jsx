import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";

import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";

import { Spinner, StyledTableCell, StyledTableRow } from "@components";

import { useDeleteDepartmentMutation, useGetAllDepartmentsQuery } from "@app";
import { RequestInterceptor } from "@lib/util";
import { useAdminContext } from "@pages/admin/hooks";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllDepartments() {
  const [filter, setFilter] = useState("Dept. Name"); // Active filter
  const [searchQuery, setSearchQuery] = useState(""); // Search query
  const [page, setPage] = useState(1);
  const { data = [], isLoading } = useGetAllDepartmentsQuery(page);

  const [deleteThisDepartment, { isLoading: isDepartmentDeleting }] =
    useDeleteDepartmentMutation("deleteDepartment");

  const {
    user: { first_name, last_name },
  } = useSelector((state) => state.auth);

  const { navToUploadDepartments } = useAdminContext();
  const navigate = useNavigate();

  // Handle filter change
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery(""); // Clear search query when filter changes
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  // Filtered departments based on the active filter and search query
  const filteredDepartments = data?.data?.filter((department) => {
    const valueToFilter =
      filter === "Dept. Name"
        ? department?.name
        : filter == "code"
          ? department?.code
          : department?.school?.name;
    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const deleteDepartment = async (id) => {
    try {
      await RequestInterceptor.handleRequest(
        () => deleteThisDepartment(id),
        { shouldConfirm: true },
        "Delete department"
      );
    } catch (err) {
      alert("Error deleting department.");
      console.error(err);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      {/* Header */}
      <div className="w-full flex items-start justify-between mb-4">
        <p className="text-green text-sm font-normal">
          Welcome {first_name} {last_name}!
        </p>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="success"
          style={{ textTransform: "capitalize" }}
          onClick={() => navigate("/admin/departments/add")}
        >
          Add Department
        </Button>
      </div>

      {/* Filter and Search */}
      <div className="w-full flex flex-col items-start h-full">
        <p className="text-black text-lg font-semibold mb-4">All Departments</p>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            {/* Search Input */}
            <Box>
              <TextField
                fullWidth
                size="small"
                id="search"
                label={`Search by ${filter}`}
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment className="cursor-pointer">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            {/* Filter Dropdown */}
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  onChange={handleFilterChange}
                >
                  <MenuItem value="Dept. Name">Department Name</MenuItem>
                  <MenuItem value="Code">Code</MenuItem>
                  <MenuItem value="school">school</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>
          {/* Import/Export Buttons */}
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={navToUploadDepartments}
              >
                Import CSV
              </Button>
            </Stack>
          </div>
        </div>

        {/* Table Section */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Department Name</StyledTableCell>
                  <StyledTableCell>Code</StyledTableCell>
                  <StyledTableCell>School</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : filteredDepartments?.length < 1 ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={4} align="center">
                      No departments found
                    </StyledTableCell>
                  </StyledTableRow>
                ) : (
                  filteredDepartments?.map((department, idx) => (
                    <StyledTableRow key={department.id}>
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell>
                        {department?.name ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {department?.code ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {department?.school?.name ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <Link to={`/admin/departments/${department?.id}`}>
                            <VisibilityIcon
                              color="primary"
                              sx={{ cursor: "pointer" }}
                            />
                          </Link>
                          <Link
                            to={`/admin/departments/${department?.id}/edit`}
                          >
                            <EditOutlinedIcon
                              color="success"
                              sx={{ cursor: "pointer" }}
                            />
                          </Link>
                          {isDepartmentDeleting ? (
                            <Spinner size="15px" />
                          ) : (
                            <DeleteOutlineOutlinedIcon
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() => deleteDepartment(department?.id)}
                            />
                          )}
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={10}
                    page={page}
                    onChange={(event, value) => setPage(value)}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllDepartments;
