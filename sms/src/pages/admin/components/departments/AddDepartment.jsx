import { useCreateDepartmentMutation, useGetAllSchoolsQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { useAdminContext } from "@pages/admin/hooks";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddDepartment() {
  const navigate = useNavigate();
  const { navToUploadDepartments } = useAdminContext();
  const { data, isLoading } = useGetAllSchoolsQuery("");
  const [createDepartment, { isLoading: isCreatingDepartment }] =
    useCreateDepartmentMutation("createDepartment");

  const [form, setForm] = useState({
    school: "",
    departmentCode: "",
    departmentName: "",
    hodName: "",
  });

  const addDepartment = async (e) => {
    e.preventDefault();
    try {
      const { message } = await createDepartment({
        name: form.departmentName,
        school_id: form.school,
        // "admin_id": 1,
        code: form.departmentCode,
        description: form.hodName,
      }).unwrap();

      alert(message);
      navigate(-1);
    } catch (error) {
      alert("An error occurred, while creating the department");
    }
  };

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  return (
    <div className="w-full flex flex-col items-start  md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col bg-white p-10">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">Add Department</p>
          </div>
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={navToUploadDepartments}
              >
                Import csv
              </Button>
            </Stack>
          </div>
        </div>

        <div className=" flex flex-col w-full items-center justify-between">
          <form className=" w-full " onSubmit={addDepartment}>
            <div className="mb-4 w-full">
              <TextField
                onChange={onChange}
                name="school"
                id="school"
                label="School"
                value={form.school}
                fullWidth
                required
                select
              >
                {isLoading ? (
                  <Spinner />
                ) : (
                  data?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="departmentCode"
                id="departmentCode"
                label="Department Code"
                value={form.departmentCode}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                label="Department Name"
                name="departmentName"
                id="departmentName"
                value={form.departmentName}
                fullWidth
                required
              />
            </Stack>
            <div className="mb-4 w-full">
              <TextField
                onChange={onChange}
                name="hodName"
                type="text"
                id="hodName"
                label="Description"
                value={form.hodName}
                fullWidth
                required
              />
            </div>
            <div className="w-full">
              {isCreatingDepartment ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  type="submit"
                >
                  Add Department
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default AddDepartment;
