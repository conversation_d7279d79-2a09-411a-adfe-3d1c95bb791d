import {
  AddDepartments,
  AllDepartments,
  UploadDepartment,
} from "@pages/admin/components/departments";
import PropTypes from "prop-types"; // Import PropTypes

function SelectDepartmentStep({ step }) {
  switch (step) {
    case 0:
      return <AllDepartments />;
    case 1:
      return <UploadDepartment />;
    case 2:
      return <AddDepartments />;
    default:
      throw new Error("Process was not informed. Include in case list.");
  }
}

// Add prop types validation
SelectDepartmentStep.propTypes = {
  step: PropTypes.number.isRequired, // Validate that step is a required number
};

export default SelectDepartmentStep;
