import {
  useGetAllSchoolsQuery,
  useGetDepartmentByIdQuery,
  useUpdateDepartmentMutation,
} from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function EditDepartment() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data: department, status } = useGetDepartmentByIdQuery(
    id,
    "Get single department"
  );
  const { data: schools, isLoading: isSchoolsLoading } =
    useGetAllSchoolsQuery("get all schools");
  const [updateDepartment, { isLoading: isUpdatingDepartment }] =
    useUpdateDepartmentMutation("update department");
  const [form, setForm] = useState({
    school: "EHIST",
    departmentCode: "0001",
    departmentName: "Computer Engineering and Network",
    description: "Abongwi Darren",
  });

  useEffect(() => {
    console.log(department?.data);
    switch (status) {
      case "fulfilled":
        setForm({
          school: department.data?.school?.name,
          departmentCode: department?.data?.code,
          departmentName: department?.data?.name,
          description: department?.data?.description,
        });
        break;
    }
  }, [status]);

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const onSubmit = async (e) => {
    e.preventDefault();
    try {
      await RequestInterceptor.handleRequest(
        () =>
          updateDepartment({
            id,
            body: {
              name: form.departmentName,
              school_id: form.school,
              code: form.departmentCode,
              description: form.description,
            },
          }),
        { onSuccess: () => navigate(-1) },
        EditDepartment.name
      );
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="w-full flex flex-col items-start  md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col bg-white p-10">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">Edit Department</p>
          </div>
        </div>

        <div className=" flex flex-col w-full items-center justify-between">
          <form className=" w-full " onSubmit={onSubmit}>
            <div className="mb-4 w-full">
              <TextField
                onChange={onChange}
                name="school"
                id="school"
                label="School"
                value={form.school}
                fullWidth
                required
                select
              >
                {isSchoolsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  schools?.data.map((option, idx) => (
                    <MenuItem key={option.id} value={option.id}>
                      {idx + 1}. {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="departmentCode"
                id="departmentCode"
                label="Department Code"
                value={form.departmentCode}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                label="Department Name"
                name="departmentName"
                id="departmentName"
                value={form.departmentName}
                fullWidth
                required
              />
            </Stack>
            <div className="mb-4 w-full">
              <TextField
                onChange={onChange}
                name="description"
                type="text"
                id="description"
                label="Description"
                value={form.description}
                fullWidth
                required
              />
            </div>
            <div className="w-full">
              {isUpdatingDepartment ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  type="submit"
                >
                  Edit Department
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default EditDepartment;
