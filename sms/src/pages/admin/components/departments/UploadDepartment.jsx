import { useGetAllSchoolsQuery, useImportDepartmentsMutation } from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, TextField } from "@mui/material";
import { useAdminContext } from "@pages/admin/hooks";
import { useState } from "react";

function UploadDepartment() {
  UploadDepartment.tag = "Upload Department";
  const { navToAllDepartments } = useAdminContext();
  const [uploadDepartment, { isLoading }] =
    useImportDepartmentsMutation("upload-departments");
  const { data, isLoading: isSchoolsLoading } = useGetAllSchoolsQuery("");

  const [form, setForm] = useState({
    school: "",
  });
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState();

  const handleFileDrop = (ev) => {
    ev.preventDefault();
    setIsDragging(false);
    if (ev.dataTransfer.items) {
      // Use DataTransferItemList interface to access the file(s). For modern
      [...ev.dataTransfer.items].forEach((item) => {
        // If dropped items aren't files, reject them
        // console.log(ite)
        if (item.kind === "file") {
          const file = item.getAsFile();
          setFile(file);
          return; // we only need a single file
        }
      });
    } else {
      [...ev.dataTransfer.files].forEach((file) => {
        if (file.type.startsWith("text")) {
          setFile(file);
          return;
        }
      });
    }
    // console.log(e);
  };

  const onDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };
  const onDragLeave = () => {
    setIsDragging(false);
  };

  const importDepartment = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("file", file);
    formData.append("school_id", form.school);
    await RequestInterceptor.handleRequest(
      () => uploadDepartment(formData),
      {
        onSuccess: () => {
          setIsDragging(false);
          setFile(null);
          window.location.reload();
        },
      },
      UploadDepartment.tag
    );
  };

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  return (
    <div className="w-full flex flex-col items-start px-16 py-10 justify-between">
      <div className="w-full flex flex-col items-start justify-between mb-4">
        <div className="flex items-center gap-2 mb-6">
          <div className="hover:bg-grey p-2 rounded-full">
            <ArrowBackIcon onClick={navToAllDepartments} />
          </div>
          <p className="text-black font-semibold text-lg">Upload Department</p>
        </div>
        <form className="w-full" onSubmit={importDepartment}>
          <div className="mb-4 w-full">
            <TextField
              onChange={onChange}
              name="school"
              id="school"
              label="School"
              value={form.school}
              fullWidth
              select
            >
              {isSchoolsLoading ? (
                <Spinner size="14px" />
              ) : (
                data?.data?.map((option) => (
                  <MenuItem key={option.id} value={option.id}>
                    {option.name}
                  </MenuItem>
                ))
              )}
            </TextField>
          </div>

          <div className="flex items-center justify-center w-full mb-4">
            <label
              htmlFor="dropzone-file"
              className="flex flex-col items-center justify-center w-full h-64 border-2 cursor-pointer border-dashed border-green text-green"
              onDrop={handleFileDrop}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
            >
              <div
                className={`flex flex-col items-center justify-center pt-5 pb-6 w-full h-full ${
                  isDragging
                    ? "bg-grey_500 bg-opacity-10 border-4 border-dashed border-green"
                    : ""
                }`}
              >
                <svg
                  className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 16"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                  />
                </svg>

                {file ? (
                  <span className="text-gray-500">{`${file?.name} (Click to Change)`}</span>
                ) : (
                  <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-semibold">Click to upload</span> or
                    drag and drop
                  </p>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  CSV file only
                </p>
              </div>
              <input
                id="dropzone-file"
                type="file"
                className="hidden"
                required
                onChange={(e) => setFile(e.target.files[0])}
              />
            </label>
          </div>
          <div className="w-full">
            {isLoading ? (
              <Spinner />
            ) : (
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                type="submit"
                disabled={isLoading}
              >
                Add Department
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default UploadDepartment;
