import { useGetDepartmentByIdQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useNavigate, useParams } from "react-router-dom";

function ViewDepartment() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data: department, isLoading: isSingleDepartmentLoading } =
    useGetDepartmentByIdQuery(id, "Get single department");

  return (
    <div className="w-full flex flex-col items-start  md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col bg-white p-10">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">
              Viewing Department
            </p>
          </div>
        </div>

        <div className=" flex flex-col w-full items-center justify-between">
          {isSingleDepartmentLoading ? (
            <Spinner />
          ) : (
            <div className="flex flex-col gap-4 w-full">
              <p className="text-black text-lg mb-2">
                <span className="font-bold">Department Name:</span>{" "}
                {department?.data?.name}
              </p>
              <p className="text-black text-lg  mb-2">
                <span className="font-bold">Department Code: </span>
                {department?.data?.code}
              </p>
              <p className="text-black text-lg mb-2">
                <span className="font-bold">Department Description: </span>
                {department?.data?.description}
              </p>
              <p className="text-black text-lg mb-2">
                <span className="font-bold">School Name: </span>
                {department?.data?.school?.name ?? "N/A"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ViewDepartment;
