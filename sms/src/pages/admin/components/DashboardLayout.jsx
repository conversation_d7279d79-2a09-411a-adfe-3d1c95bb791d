import {
  AccountBalanceOutlined,
  AccountCircleOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BookOutlined,
  CalendarMonthOutlined,
  GridViewRounded,
  LogoutOutlined,
  SchoolOutlined,
  SchoolTwoTone,
  Settings,
  SupervisorAccountOutlined,
} from "@mui/icons-material";

import LogOutModal from "@components/LogOutModal";
import ArrowOutwardIcon from "@mui/icons-material/ArrowOutward";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";
import LocalLibraryOutlinedIcon from "@mui/icons-material/LocalLibraryOutlined";
import MenuIcon from "@mui/icons-material/Menu";
import { Box, List, ListItem, ListItemIcon, ListItemText } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import CssBaseline from "@mui/material/CssBaseline";
import Divider from "@mui/material/Divider";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import Toolbar from "@mui/material/Toolbar";
import Head from "@pages/admin/components/partials/Header";
import { PropTypes } from "prop-types"; // Add PropTypes for prop validation
import * as React from "react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const drawerWidth = 240;

export default function DashboardLayout({ children }) {
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleDrawerClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerToggle = () => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };
  const role = "admin";
  const user = {
    role: "admin",
  };
  const { pathname } = useLocation();

  const [openLogoutModal, setOpenLogoutModal] = useState(false);
  const handleLogout = () => {
    setOpenLogoutModal(true);
  };

  const adminPaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Schools",
      link: `/${user.role}/schools`,
      icon: <AccountBalanceOutlined />,
    },
    {
      tabName: "Programs",
      link: `/${user.role}/programs`,
      icon: <LocalLibraryOutlinedIcon />,
    },
    {
      tabName: "Academic Year",
      link: `/${user.role}/academic-year`,
      icon: <AutoStoriesIcon />,
    },
    {
      tabName: "Levels",
      link: `/${user.role}/levels`,
      icon: <ArrowOutwardIcon />,
    },
    {
      tabName: "Departments",
      link: `/${user.role}/departments`,
      icon: <SchoolTwoTone />,
    },
    {
      tabName: "Students",
      link: `/${user.role}/students`,
      icon: <SupervisorAccountOutlined />,
    },
    {
      tabName: "Lecturers",
      link: `/${user.role}/lecturers`,
      icon: <SchoolOutlined />,
    },
    {
      tabName: "Courses",
      link: `/${user.role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Results",
      link: `/${user.role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Student Results",
      link: `/${user.role}/student-results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Calendar",
      link: `/${user.role}/calendar`,
      icon: <CalendarMonthOutlined />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/account`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Settings",
      link: `/${user.role}/settings`,
      icon: <Settings />,
    },
    {
      tabName: "Logout",
      link: "", // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  const sideBarItems = adminPaths;

  const drawer = (
    <div>
      <div>
        <Link to={`/${role}`} className="w-full flex items-center gap-3 p-5">
          <img src={logo} style={{ height: "44px", width: "40px" }} />
          <p className="text-green font-semibold text-md">EHIST</p>
        </Link>
      </div>
      <Divider />
      <List className="overflow-y-auto">
        {sideBarItems.map((item) => {
          return (
            <Link
              key={item.tabName}
              to={item?.link}
              onClick={
                item.tabName === "Logout" ? handleLogout : handleDrawerClose
              }
            >
              <ListItem
                id="active"
                className={`hover:bg-[#F0F1F3] hover:border-l-4 my-0 py-0 hover:border-l-[#186318] hover:text-[#186318] w-full ${
                  pathname === item.link
                    ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                    : ""
                }`}
              >
                <ListItemIcon className="hover:text-[#186318]">
                  {item?.icon}
                </ListItemIcon>
                <ListItemText className="text-[11px]">
                  {item?.tabName}
                </ListItemText>
              </ListItem>
            </Link>
          );
        })}
      </List>
      <Divider />
    </div>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: "white",
          color: "black",
          boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        }}
      >
        <Toolbar>
          <IconButton
            color="green"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: "none" } }}
          >
            <MenuIcon />
          </IconButton>
          <Head className="text-black" />
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onTransitionEnd={handleDrawerTransitionEnd}
          onClose={handleDrawerClose}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              maxWidth: "80vw",
              overflowY: "auto",
              backgroundColor: "white",
              boxShadow: "2px 0 8px rgba(0,0,0,0.1)",
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", sm: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              maxWidth: "100vw",
              overflowY: "auto",
              overflowX: "hidden",
              backgroundColor: "white",
              boxShadow: "2px 0 8px rgba(0,0,0,0.1)",
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          overflowX: "hidden",
          backgroundColor: "#f9fafb",
          minHeight: "100vh",
        }}
      >
        <Toolbar />
        <section className="w-full max-w-full flex flex-col items-center justify-center py-6 overflow-x-hidden">
          {children}
        </section>
      </Box>
      {openLogoutModal && (
        <LogOutModal
          openModal={openLogoutModal}
          closeModal={() => setOpenLogoutModal(false)}
        />
      )}
    </Box>
  );
}

// Add prop types validation
DashboardLayout.propTypes = {
  children: PropTypes.node.isRequired, // Validate children prop
};
