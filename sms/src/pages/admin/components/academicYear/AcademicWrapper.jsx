import PropTypes from "prop-types"; // Import PropTypes

function AcademicWrapper({ children }) {
  return (
    <section className="min-h-screen w-full py-5 flex flex-col items-start">
      <div className="w-full items-start mb-3">
        <p className="text-black text-md font-semibold">Academic Year</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>
      {children}
    </section>
  );
}

// Add prop types validation
AcademicWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Validate
};

export default AcademicWrapper;
