import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";

import {
  useDeleteAcademicYearMutation,
  useGetAllAcademicYearsQuery,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import {
  AddAcademicYearModal,
  EditAcademiYearDialog,
} from "@pages/admin/components/academicYear";
import PropTypes from "prop-types";
import { useState } from "react";
import { toast } from "react-toastify";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllAcademicYears() {
  const [filter, setFilter] = useState("Name");
  const { data, isError, isLoading } =
    useGetAllAcademicYearsQuery("academicYears");

  const handleChange = (event) => {
    setFilter(event.target.value);
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(10);

  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };
  const [openAcademicModal, setOpenAcademicModal] = useState(false);
  const system = useGetSystemSettings();
  const [openEditAcademicModal, setOpenEditAcademicModal] = useState(false);

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex flex-col items-start h-full">
        <div className="w-full flex items-start justify-between mb-4">
          <p className="text-black text-lg font-semibold mb-4">
            Current Academic year:{" "}
            <span className="text-gray-500">
              {system?.current_academic_year_name}
            </span>
          </p>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => setOpenAcademicModal(true)}
          >
            Start
          </Button>
        </div>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            <Box>
              <TextField
                fullWidth
                size="small"
                label="search for academic year"
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  name="filter"
                  onChange={handleChange}
                >
                  <MenuItem>academic year</MenuItem>
                  <MenuItem>start date</MenuItem>
                  <MenuItem>end date</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Export csv
              </Button>
            </Stack>
          </div>
        </div>
        {/* Table section */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Academic Year</StyledTableCell>
                  <StyledTableCell>Start Date</StyledTableCell>
                  <StyledTableCell>End Date</StyledTableCell>
                  <StyledTableCell>Status</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : isError ? (
                  <code>Error fetching academic years</code>
                ) : (
                  data?.data?.map((year, idx) => (
                    <TableData
                      key={year?.id}
                      year={year}
                      idx={idx + 1}
                      openEdit
                    />
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={count}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>

        {openAcademicModal && (
          <AddAcademicYearModal
            openModal={openAcademicModal}
            closeModal={() => setOpenAcademicModal(false)}
          />
        )}

        {openEditAcademicModal && (
          <EditAcademiYearDialog
            openModal={openEditAcademicModal}
            closeModal={() => setOpenEditAcademicModal(false)}
          />
        )}
      </div>
    </ThemeProvider>
  );
}

export function TableData({ year, idx }) {
  const [deleteAcademicYear, { isLoading }] =
    useDeleteAcademicYearMutation("deleteAcademicYear");

  const deleteThisAcademicYear = async () => {
    try {
      await RequestInterceptor.handleRequest(
        () => deleteAcademicYear(year?.id),
        { shouldConfirm: true },
        "Delete Academic Year"
      );
    } catch (error) {
      console.error("Error deleting academic year:", error);
      toast.error(error.message ?? "Error deleting academic year:");
    }
  };

  const [openEditAcademicModal, setOpenEditAcademicModal] = useState(false);

  return (
    <StyledTableRow>
      <StyledTableCell>{idx}</StyledTableCell>
      <StyledTableCell>
        {year?.name?.replace("_", "/") ?? "N/A"}
      </StyledTableCell>
      <StyledTableCell>{year?.start_date ?? "N/A"}</StyledTableCell>
      <StyledTableCell>{year?.end_date ?? "N/A"}</StyledTableCell>
      <StyledTableCell>{year?.status ?? "N/A"}</StyledTableCell>
      <StyledTableCell>
        <div className="flex items-center gap-3">
          <EditOutlinedIcon
            color="success"
            sx={{ cursor: "pointer" }}
            onClick={() => setOpenEditAcademicModal(true)}
          />
          {isLoading ? (
            <Spinner size="18px" />
          ) : (
            <DeleteOutlineOutlinedIcon
              color="error"
              sx={{ cursor: "pointer" }}
              onClick={deleteThisAcademicYear}
            />
          )}
        </div>
      </StyledTableCell>
      {openEditAcademicModal && (
        <EditAcademiYearDialog
          year={year}
          openModal={openEditAcademicModal}
          closeModal={() => setOpenEditAcademicModal(false)}
        />
      )}
    </StyledTableRow>
  );
}

TableData.propTypes = {
  year: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string,
    start_date: PropTypes.string,
    end_date: PropTypes.string,
    status: PropTypes.string,
  }).isRequired,
  idx: PropTypes.number.isRequired,
};

export default AllAcademicYears;
