import { useCreateAcademicYearMutation } from "@app";
import { <PERSON><PERSON>, Spin<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";
import { toast } from "react-toastify";

function AddAcademicYearModal({ openModal, closeModal }) {
  const [addAcademicYear, { isLoading }] =
    useCreateAcademicYearMutation("createAcademicYear");
  const [form, setForm] = useState({
    academicYear: "",
    start_date: "",
    end_date: "",
  });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await RequestInterceptor.handleRequest(
        () =>
          addAcademicYear({
            ...form,
            name: form.academicYear,
          }),
        { onSuccess: closeModal }
      );
    } catch (error) {
      toast.info(error.message ?? " Couldn't add a new academic year");
    }
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Start Academic Year
        </h2>
        <form className="w-full " onSubmit={handleSubmit}>
          <div className="w-full flex mb-2">
            <TextField
              fullWidth
              id="academicYear"
              size="small"
              value={form.academicYear}
              label="Academic Year"
              name="academicYear"
              onChange={handleChange}
            ></TextField>
          </div>
          <div className="w-full flex md:flex-row flex-col gap-2 mb-3">
            <TextField
              fullWidth
              type="date"
              id="start_date"
              size="small"
              value={form.start_date}
              label="select start date"
              name="start_date"
              onChange={handleChange}
            ></TextField>
            <TextField
              fullWidth
              type="date"
              id="end_date"
              size="small"
              value={form.end_date}
              label="select end date"
              name="end_date"
              onChange={handleChange}
            ></TextField>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <Stack direction={"row"} gap={2}>
              <Button
                className="w-full"
                title={"Add Academic Year.."}
                type={"primary"}
                onClick={handleSubmit}
              />
            </Stack>
          )}
        </form>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
AddAcademicYearModal.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default AddAcademicYearModal;
