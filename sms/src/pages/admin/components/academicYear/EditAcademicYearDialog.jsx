import { useUpdateAcademicYearMutation } from "@app";
import { <PERSON><PERSON>, Spin<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types";
import { useState } from "react";
import { toast } from "react-toastify";

function EditAcademicYearDialog({ openModal, closeModal, year }) {
  const [form, setForm] = useState({
    academicYear: year?.name ?? "N/A",
    startDate: year?.start_date,
    endDate: year?.end_date,
  });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });
  const [updateAcademicYear, { isError, isLoading }] =
    useUpdateAcademicYearMutation("updateAcademicYear");
  const handleSubmit = async (e) => {
    e.preventDefault();
    const body = {
      name: form.academicYear,
      start_date: form.startDate,
      end_date: form.endDate,
    };
    try {
      await RequestInterceptor.handleRequest(
        () =>
          updateAcademicYear({
            id: year?.id,
            body,
          }),
        { onSuccess: closeModal }
      );
    } catch (error) {
      toast.info(error.message ?? "Error updating academic year");
      closeModal();
    }
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Edit Academic Year <span className="text-green">{year?.name}</span>
        </h2>
        <form className="w-full " onSubmit={handleSubmit}>
          <div className="w-full flex mb-2">
            <TextField
              fullWidth
              id="academicYear"
              size="small"
              value={form.academicYear}
              label="Academic Year"
              name="academicYear"
              onChange={handleChange}
            ></TextField>
          </div>
          <div className="w-full flex md:flex-row flex-col gap-2 mb-3">
            <TextField
              fullWidth
              type="date"
              id="startDate"
              size="small"
              value={form.startDate}
              label="select start date"
              name="startDate"
              onChange={handleChange}
            ></TextField>
            <TextField
              fullWidth
              type="date"
              id="endDate"
              size="small"
              value={form.endDate}
              label="select end date"
              name="endDate"
              onChange={handleChange}
            ></TextField>
          </div>
          {isLoading ? (
            <Spinner />
          ) : isError ? (
            <code>Error update form</code>
          ) : (
            <Stack
              direction={"row"}
              display={"flex"}
              justifyContent={"center"}
              gap={2}
            >
              <Button
                title={"Update Academic year"}
                type={"primary"}
                onClick={handleSubmit}
              />
            </Stack>
          )}
        </form>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
EditAcademicYearDialog.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  year: PropTypes.shape({
    name: PropTypes.string,
    start_date: PropTypes.string,
    end_date: PropTypes.string,
    id: PropTypes.string.isRequired,
  }).isRequired,
};

export default EditAcademicYearDialog;
