import {
  AccountBalanceOutlined,
  AccountCircleOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  BookOutlined,
  Calendar<PERSON>onthOutlined,
  GridViewRounded,
  LogoutOutlined,
  SchoolOutlined,
  SchoolTwoTone,
  Settings,
  SupervisorAccountOutlined,
  SwapH<PERSON>zOutlined,
} from "@mui/icons-material";

import ArrowOutwardIcon from "@mui/icons-material/ArrowOutward";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";
import LocalLibraryOutlinedIcon from "@mui/icons-material/LocalLibraryOutlined";
import MenuIcon from "@mui/icons-material/Menu";
import {
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const Sidebar = () => {
  const user = {
    name: "",
    role: "admin",
  };
  const [display, setDisplay] = useState(false);
  const toggle = () => setDisplay(!display);
  const { pathname } = useLocation();

  const adminPaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Schools",
      link: `/${user.role}/schools`,
      icon: <AccountBalanceOutlined />,
    },
    {
      tabName: "Programs",
      link: `/${user.role}/programs`,
      icon: <LocalLibraryOutlinedIcon />,
    },
    {
      tabName: "Academic Year",
      link: `/${user.role}/academic-year`,
      icon: <AutoStoriesIcon />,
    },
    {
      tabName: "Levels",
      link: `/${user.role}/levels`,
      icon: <ArrowOutwardIcon />,
    },
    {
      tabName: "Departments",
      link: `/${user.role}/departments`,
      icon: <SchoolTwoTone />,
    },
    {
      tabName: "Students",
      link: `/${user.role}/students`,
      icon: <SupervisorAccountOutlined />,
    },
    {
      tabName: "Lecturers",
      link: `/${user.role}/lecturers`,
      icon: <SchoolOutlined />,
    },
    {
      tabName: "Courses",
      link: `/${user.role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Results",
      link: `/${user.role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Calendar",
      link: `/${user.role}/calendar`,
      icon: <CalendarMonthOutlined />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/account`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Settings",
      link: `/${user.role}/settings`,
      icon: <Settings />,
    },
    {
      tabName: "Logout",
      link: "", // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  const studentPaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Schools",
      link: `/${user.role}/schools`,
      icon: <AccountBalanceOutlined />,
    },
    {
      tabName: "Students",
      link: `/${user.role}/students`,
      icon: <SupervisorAccountOutlined />,
    },
    {
      tabName: "Lectures",
      link: `/${user.role}/lectures`,
      icon: <SchoolOutlined />,
    },
    {
      tabName: "Courses",
      link: `/${user.role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Results",
      link: `/${user.role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Finances",
      link: `/${user.role}/finances`,
      icon: <SwapHorizOutlined />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/account`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Logout",
      link: "", // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  const financePaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/account`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Transactions",
      link: `/${user.role}/finances`,
      icon: <SwapHorizOutlined />,
    },
    {
      tabName: "Report",
      link: `/${user.role}/results`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Logout",
      link: "", // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  let sideBarItems;
  switch (user.role) {
    case "admin":
      sideBarItems = adminPaths;
      break;
    case "student":
      sideBarItems = studentPaths;
      break;
    case "finance":
      sideBarItems = financePaths;
      break;
    default:
      sideBarItems = [];
  }

  return (
    <div className="fixed left-0 w-46 bg-white h-full">
      <>
        <Grid
          className="w-full items-center"
          justifyContent={"space-between"}
          alignItems={"center"}
        >
          <Grid
            justifyContent={"center"}
            className="py-6 px-11 cursor-pointer h-full"
            placeSelf={"center"}
            item
          >
            <Link to={"/admin"}>
              <img
                src={logo}
                style={{ height: "48px", width: "44px", color: "#186318" }}
                className="logo"
              />
            </Link>
            <div
              style={{
                height: "48px",
                width: "44px",
                background: "#f3f4f6",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
              }}
              onClick={toggle}
            >
              <MenuIcon style={{ color: "#222", fontSize: 32 }} />
            </div>
          </Grid>
          <hr className="text-grey_500" />
          {!display ? (
            <>
              <Grid className="py-5">
                <List>
                  {sideBarItems.map((item) => (
                    <Link key={item.tabName} to={item?.link}>
                      <ListItem
                        id="active"
                        className={`hover:bg-[#F0F1F3] hover:border-l-4 hover:border-l-[#186318] hover:text-[#186318] w-[90%] mx-1 ${
                          pathname === item.link
                            ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                            : ""
                        }`}
                      >
                        <ListItemIcon className="hover:text-[#186318]">
                          {item?.icon}
                        </ListItemIcon>
                        <ListItemText className="text-[11px]">
                          {item?.tabName}
                        </ListItemText>
                      </ListItem>
                    </Link>
                  ))}
                </List>
              </Grid>
            </>
          ) : (
            <></>
          )}
        </Grid>
      </>
    </div>
  );
};

export default Sidebar;
