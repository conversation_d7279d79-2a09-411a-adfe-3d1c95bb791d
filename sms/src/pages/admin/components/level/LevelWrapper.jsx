import PropTypes from "prop-types"; // Import PropTypes

function LevelWrapper({ children }) {
  return (
    <section className="min-h-screen w-full py-5 flex flex-col items-start">
      <div className="w-full items-start mb-3">
        <p className="text-black text-md font-semibold">Level</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>
      {children}
    </section>
  );
}

// Add prop types validation
LevelWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Validate children prop
};

export default LevelWrapper;
