import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function EditLevelDialog({ openModal, closeModal }) {
  const [form, setForm] = useState({
    name: "level 200",
    year: "1",
  });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Edit Level</h2>
        <form className="w-full ">
          <div className="w-full flex md:flex-row flex-col gap-2 mb-3">
            <TextField
              fullWidth
              type="text"
              id="name"
              size="small"
              value={form.name}
              label="Name"
              name="name"
              onChange={handleChange}
            ></TextField>
            <TextField
              fullWidth
              type="text"
              id="year"
              size="small"
              value={form.year}
              label="Year"
              name="year"
              onChange={handleChange}
            ></TextField>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"Done"} type={"primary"} onClick={closeModal} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
EditLevelDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default EditLevelDialog;
