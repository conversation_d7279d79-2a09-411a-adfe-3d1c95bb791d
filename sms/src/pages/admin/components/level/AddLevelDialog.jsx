import { useCreateLevelMutation } from "@app";
import { <PERSON><PERSON>, Spin<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function AddLevelDialog({ openModal, closeModal }) {
  AddLevelDialog.tag = "Add Level";
  const [form, setForm] = useState({
    name: "",
    year: "",
  });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });
  const [addLevel, { isLoading: isAddingLevel }] =
    useCreateLevelMutation("create level");

  const createLevel = async (e) => {
    e.preventDefault();
    await RequestInterceptor.handleRequest(
      () => addLevel(form),
      { onSuccess: () => window.location.reload() },
      AddLevelDialog.tag
    );
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Add Level</h2>
        <form className="w-full " onSubmit={createLevel}>
          <div className="w-full flex md:flex-row flex-col gap-2 mb-3">
            <TextField
              fullWidth
              type="text"
              id="name"
              size="small"
              value={form.name}
              label="Name"
              name="name"
              onChange={handleChange}
            ></TextField>
            <TextField
              fullWidth
              type="number"
              id="year"
              size="small"
              value={form.year}
              label="Year"
              name="year"
              onChange={handleChange}
            ></TextField>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          {isAddingLevel ? (
            <Spinner />
          ) : (
            <Button title={"Done"} type={"primary"} onClick={createLevel} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add PropTypes validation
AddLevelDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Add validation for openModal
  closeModal: PropTypes.func.isRequired, // Add validation for closeModal
};

export default AddLevelDialog;
