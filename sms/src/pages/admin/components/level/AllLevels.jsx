import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";

import { useDeleteLevelMutation, useGetAllLevelsQuery } from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { AddLevelDialog, EditLevelDialog } from "@pages/admin/components/level";
import { DeleteDialog } from "@pages/admin/components/schools";
import { useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllLevels() {
  const [filter, setFilter] = useState("Name");
  const { data, isLoading } = useGetAllLevelsQuery("get levels");
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [deleteLevel, { isLoading: isDeletingLevel }] =
    useDeleteLevelMutation("delete level");
  const handleChange = (event) => {
    setFilter(event.target.value);
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(10);
  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [openLevelModal, setOpenLevelModal] = useState(false);
  const [openEditLevelModal, setOpenEditLevelModal] = useState(false);

  const removeLevel = async (id) => {
    alert(
      `Can not delete this level. Contact System Admin with level ID: ${id}`
    );
    return;
    // await RequestInterceptor.handleRequest(
    //   () => deleteLevel(id),
    //   {},
    //   "Delete Level"
    // );
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex flex-col items-start h-full">
        <div className="w-full flex items-start justify-between mb-4">
          <p className="text-black text-lg font-semibold mb-4">All Levels</p>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => setOpenLevelModal(true)}
          >
            Add
          </Button>
        </div>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            <Box>
              <TextField
                fullWidth
                size="small"
                label="search for level"
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  name="filter"
                  onChange={handleChange}
                >
                  <MenuItem>name</MenuItem>
                  <MenuItem>years</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Export csv
              </Button>
            </Stack>
          </div>
        </div>
        {/* Table section */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Name</StyledTableCell>
                  <StyledTableCell>Year</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : (
                  data?.data?.map((level, idx) => (
                    <StyledTableRow key={level.id}>
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell>{level.name}</StyledTableCell>
                      <StyledTableCell>{level.year}</StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <EditOutlinedIcon
                            color="success"
                            sx={{ cursor: "pointer" }}
                            onClick={() => setOpenEditLevelModal(true)}
                          />
                          {isDeletingLevel ? (
                            <Spinner size="18px" />
                          ) : (
                            <DeleteOutlineOutlinedIcon
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() => removeLevel(level.id)}
                            />
                          )}
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={count}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
        {openSuccessModal && (
          <DeleteDialog
            openModal={openSuccessModal}
            closeModal={() => setOpenSuccessModal(false)}
          />
        )}

        {openLevelModal && (
          <AddLevelDialog
            openModal={openLevelModal}
            closeModal={() => setOpenLevelModal(false)}
          />
        )}

        {openEditLevelModal && (
          <EditLevelDialog
            openModal={openEditLevelModal}
            closeModal={() => setOpenEditLevelModal(false)}
          />
        )}
      </div>
    </ThemeProvider>
  );
}

export default AllLevels;
