import LogOutModal from "@components/LogOutModal";
import { stringAvatar } from "@lib/util";
import {
  LogoutOutlined,
  Menu,
  NotificationsOutlined,
  Search,
} from "@mui/icons-material";
import {
  Avatar,
  Badge,
  Box,
  Button,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Select,
} from "@mui/material";
import { useState } from "react";
import { useSelector } from "react-redux";

const Head = () => {
  const [openLogoutModal, setOpenLogoutModal] = useState(false);
  const { user, role } = useSelector((state) => state.auth);
  const [lang, setLang] = useState("en");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const handleLangChange = (e) => setLang(e.target.value);

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        className="w-full border-b border-gray-100 bg-white px-4 py-2 shadow-sm"
      >
        {/* Left: Search (hidden on mobile) */}
        <div className="hidden md:flex items-center gap-2">
          <input
            type="search"
            placeholder="Search"
            className="px-2 py-2 rounded-md border border-gray-200 text-black focus:outline-green"
            style={{ minWidth: 180 }}
          />
          <Button
            variant="contained"
            color="success"
            sx={{ minWidth: 40, px: 1.5 }}
          >
            <Search />
          </Button>
        </div>

        {/* Right: Controls */}
        <div className="flex flex-row items-center gap-4 ml-auto">
          {/* Language Select (hidden on mobile) */}
          <Select
            value={lang}
            onChange={handleLangChange}
            size="small"
            variant="outlined"
            sx={{
              minWidth: 80,
              fontSize: 14,
              background: "#f3f4f6",
              borderRadius: 2,
              height: 36,
              ".MuiOutlinedInput-notchedOutline": { border: "none" },
              "& .MuiSelect-icon": { color: "#16a34a" },
            }}
            className="hidden md:block"
          >
            <MenuItem value="en">EN</MenuItem>
            <MenuItem value="fr">FR</MenuItem>
          </Select>

          {/* Mobile Menu Button */}
          <IconButton
            onClick={handleMobileMenuToggle}
            sx={{ display: { xs: "flex", md: "none" }, color: "#16a34a" }}
          >
            <Menu />
          </IconButton>

          {/* Notification Icon (hidden on mobile) */}
          <IconButton
            sx={{ color: "#16a34a", display: { xs: "none", md: "flex" } }}
          >
            <Badge badgeContent={2} color="error" overlap="circular">
              <NotificationsOutlined fontSize="medium" />
            </Badge>
          </IconButton>

          {/* Profile (hidden on mobile) */}
          <div className="hidden md:flex flex-row items-center gap-2">
            <Avatar
              {...stringAvatar(
                role === "student"
                  ? `${user?.userInfo?.first_name} ${user?.userInfo?.last_name}`
                  : `${user?.first_name} ${user?.last_name}`
              )}
              sx={{ width: 36, height: 36, fontSize: 18, bgcolor: "#16a34a" }}
            />
            <div className="flex flex-col justify-center">
              <span className="text-[16px] font-bold text-gray-800 leading-tight">
                {role === "student"
                  ? `${user?.userInfo?.first_name} ${user?.userInfo?.last_name}`
                  : `${user?.first_name} ${user?.last_name}`}
              </span>
              <span className="text-[11px] text-gray-500 font-light capitalize">
                {role}
              </span>
            </div>
          </div>
        </div>
      </Box>

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={handleMobileMenuToggle}
        PaperProps={{
          sx: {
            width: "80%",
            maxWidth: 300,
            backgroundColor: "white",
            boxShadow: "2px 0 8px rgba(0,0,0,0.1)",
          },
        }}
      >
        <List>
          {/* Profile Section */}
          <ListItem className="flex flex-col items-start py-4">
            <Avatar
              {...stringAvatar(
                role === "student"
                  ? `${user?.userInfo?.first_name} ${user?.userInfo?.last_name}`
                  : `${user?.first_name} ${user?.last_name}`
              )}
              sx={{
                width: 48,
                height: 48,
                fontSize: 24,
                bgcolor: "#16a34a",
                mb: 2,
              }}
            />
            <span className="text-[18px] font-bold text-gray-800">
              {role === "student"
                ? `${user?.userInfo?.first_name} ${user?.userInfo?.last_name}`
                : `${user?.first_name} ${user?.last_name}`}
            </span>
            <span className="text-[12px] text-gray-500 font-light capitalize">
              {role}
            </span>
          </ListItem>
          <Divider />

          {/* Search Section */}
          <ListItem>
            <div className="w-full flex items-center gap-2">
              <input
                type="search"
                placeholder="Search"
                className="w-full px-3 py-2 rounded-md border border-gray-200 text-black focus:outline-green"
              />
              <Button
                variant="contained"
                color="success"
                sx={{ minWidth: 40, px: 1.5 }}
              >
                <Search />
              </Button>
            </div>
          </ListItem>
          <Divider />

          {/* Language Select */}
          <ListItem>
            <Select
              value={lang}
              onChange={handleLangChange}
              size="small"
              variant="outlined"
              fullWidth
              sx={{
                fontSize: 14,
                background: "#f3f4f6",
                borderRadius: 2,
                height: 36,
                ".MuiOutlinedInput-notchedOutline": { border: "none" },
                "& .MuiSelect-icon": { color: "#16a34a" },
              }}
            >
              <MenuItem value="en">English</MenuItem>
              <MenuItem value="fr">Français</MenuItem>
            </Select>
          </ListItem>
          <Divider />

          {/* Notifications */}
          <ListItem>
            <div className="w-full flex items-center justify-between">
              <span className="text-gray-700">Notifications</span>
              <Badge badgeContent={2} color="error">
                <NotificationsOutlined />
              </Badge>
            </div>
          </ListItem>
          <Divider />

          {/* Logout */}
          <ListItem button onClick={() => setOpenLogoutModal(true)}>
            <ListItemText primary="Logout" />
            <LogoutOutlined />
          </ListItem>
        </List>
      </Drawer>

      {openLogoutModal && (
        <LogOutModal
          openModal={openLogoutModal}
          closeModal={() => setOpenLogoutModal(false)}
        />
      )}
    </>
  );
};

export default Head;
