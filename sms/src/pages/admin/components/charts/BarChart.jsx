import { DateWizard } from "@lib/util";
import { LineChart } from "@mui/x-charts/LineChart";
import dayjs from "dayjs";
import PropTypes from "prop-types"; // Add this import

const uData = [40000, 30000, 20000, 27800, 18900, 23900, 34900];
const pData = [24000, 13980, 58000, 39800, 48000, 38000, 42000];
const xLabels = [
  "Page A",
  "Page B",
  "Page C",
  "Page D",
  "Page E",
  "Page F",
  "Page G",
];

const BarChart = ({ label1, label2 }) => {
  var date = dayjs("2024/05/25");

  return (
    <div className="w-full px-3">
      <div className="w-full flex py-3 justify-between">
        <h5 className="font-semibold">Earnings</h5>
        <select
          className="outline-none border border-slate-400 rounded-lg text-slate-400"
          name=""
          id=""
        >
          <option value="">{DateWizard.toLocaleDate(date)}</option>
        </select>
      </div>
      <LineChart
        className="w-full my-3"
        height={300}
        series={[
          { data: pData, label: label1 },
          { data: uData, label: label2 },
        ]}
        xAxis={[{ scaleType: "point", data: xLabels }]}
      />
    </div>
  );
};

// Add prop types validation
BarChart.propTypes = {
  label1: PropTypes.string.isRequired, // Validate label1
  label2: PropTypes.string.isRequired, // Validate label2
};

export default BarChart;
