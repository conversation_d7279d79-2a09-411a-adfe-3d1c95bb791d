import { useGetAllStudentsQuery } from "@app";
import { Spinner } from "@components";
import { PieChart } from "@mui/x-charts/PieChart";
import { useEffect, useState } from "react";

const size = {
  width: 400,
  height: 200,
};

const Pie = () => {
  const {
    data: students,
    isLoading: isStudentLoading,
    status: studentStatus,
  } = useGetAllStudentsQuery("students");
  const [data, setData] = useState([
    { value: 0, label: "Females" },
    { value: 0, label: "Males" },
  ]);

  useEffect(() => {
    switch (studentStatus) {
      case "fulfilled": {
        const genders = students?.data?.map(
          (student) => student?.userInfo?.gender
        );
        const totalMale = genders.filter((gender) => gender === "Male")?.length;
        const totalFemale = genders?.length - totalMale;
        setData([
          { value: totalFemale, label: "Females" },
          { value: totalMale, label: "Males" },
        ]);
        break;
      }
    }
  }, [studentStatus]);
  return (
    <div className="w-full flex justify-center my-16 items-center">
      {isStudentLoading ? (
        <Spinner />
      ) : (
        <PieChart series={[{ data, innerRadius: 80 }]} {...size} />
      )}
    </div>
  );
};

export default Pie;
