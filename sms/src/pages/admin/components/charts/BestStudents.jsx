import { useGetStatisticsQuery } from "@app";
import { Spinner } from "@components";
import {
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";

const BestStudents = () => {
  const { data: stats, isLoading: isStatsLoading } = useGetStatisticsQuery();
  return (
    <div className="flex justify-center items-center py-3">
      <TableContainer>
        <Table sx={{ Width: 600 }} size="medium">
          <TableHead>
            <TableCell></TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Matricule</TableCell>
            <TableCell>Department</TableCell>
            <TableCell>Level</TableCell>
            <TableCell>GPA</TableCell>
          </TableHead>
          <TableBody>
            {isStatsLoading ? (
              <Spinner />
            ) : (
              <TableRow sx={{ border: 0 }}>
                <TableCell align="center">
                  <Avatar sx={{ color: "white", background: "green" }}>
                    {stats?.data?.best_student?.student?.userInfo?.first_name ??
                      "N/A"}{" "}
                    {stats?.data?.best_student?.student?.userInfo?.last_name ??
                      "N/A"}
                  </Avatar>
                </TableCell>
                <TableCell align="center">
                  {stats?.data?.best_student?.student?.userInfo?.first_name ??
                    "N/A"}{" "}
                  {stats?.data?.best_student?.student?.userInfo?.last_name ??
                    "N/A"}
                </TableCell>
                <TableCell align="center">
                  {stats?.data?.best_student?.student?.matricule}
                </TableCell>
                <TableCell align="center">
                  {stats?.data?.best_student?.student?.department?.name ??
                    "N/A"}
                </TableCell>
                <TableCell align="center">
                  {stats?.data?.best_student?.student?.level?.name ?? "N/A"}
                </TableCell>
                <TableCell align="center">
                  {stats?.data?.best_student?.gpa}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

export default BestStudents;
