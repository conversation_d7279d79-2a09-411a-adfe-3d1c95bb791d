import {
  useGetAllCoursesQuery,
  useGetAllLevelsQuery,
  useGetLecturerByIdQuery,
  useUpdateLecturerMutation,
} from "@app";
import { Spinner } from "@components";
import { BASE_URL } from "@lib/config";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { lectureresGender } from "@pages/admin/components/lecturers/data";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function EditLecturer() {
  const { id } = useParams();
  const {
    data: lecturer,
    status,
    isLoading: isLecturerLoading,
  } = useGetLecturerByIdQuery(id);
  const { data: courses, isLoading: isCoursesLoading } =
    useGetAllCoursesQuery("get courses");
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("get levels");
  const [form, setForm] = useState({
    firstName: lecturer?.first_name,
    lastName: lecturer?.last_name,
    email: lecturer?.email,
    contact: lecturer?.phone_number,
    dob: lecturer?.dob,
    gender: lecturer?.gender,
    courses: lecturer?.courses,
    level: lecturer?.level,
    password: "",
    confirmPassword: "",
    profile_image: lecturer?.profile_image,
  });

  useEffect(() => {
    switch (status) {
      case "fulfilled":
        setForm({
          firstName: lecturer?.data?.first_name,
          lastName: lecturer?.data?.last_name,
          email: lecturer?.data?.email,
          contact: lecturer?.data?.phone_number,
          dob: lecturer?.data?.dob,
          gender: lecturer?.data?.gender,
          courses: lecturer?.data?.courses,
          level: lecturer?.data?.level,
          password: "",
          confirmPassword: "",
          profile_image: lecturer?.data?.profile_image,
        });

        break;
    }
  }, [status]);

  // const [showPassword, setShowPassword] = useState(false);
  const [image, setImage] = useState(null);
  const [updateLecturer, { isLoading: isUpdatingLecturer }] =
    useUpdateLecturerMutation();

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const navigate = useNavigate();

  const editLecturer = async (e) => {
    e.preventDefault();
    try {
      // if image is a file (from setImage), use it, else fetch it from the url
      const imageFile =
        image instanceof File
          ? image
          : await fetch(image).then((res) => res.blob());
      const formData = new FormData();
      formData.append("first_name", form.firstName);
      formData.append("last_name", form.lastName);
      formData.append("email", form.email);
      formData.append("phone_number", form.contact);
      formData.append("dob", form.dob);
      formData.append("gender", form.gender);
      formData.append(
        "profile_image",
        new File([imageFile], "profile_image.png", {
          type: imageFile.type || "image/jpeg",
        })
      );
      formData.append("course_id", form.courses);
      formData.append("level_id", form.level);
      console.log(formData);

      const { message, ...res } = await updateLecturer({
        id,
        body: formData,
      }).unwrap();
      console.log(res);
      alert(message);
    } catch (error) {
      console.log(error);
      alert("Could not create lecturer");
    }
  };

  return (
    <div className="w-full flex flex-col items-start px-16 py-10  justify-between">
      <div className="w-full flex flex-col p-10 bg-white">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-gray-300 p-2 rounded-full cursor-pointer">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">Edit Lecturer</p>
          </div>
        </div>
        <p className="text-green text-md font-semibold mb-4">
          Basic Information
        </p>

        {isLecturerLoading ? (
          <Spinner />
        ) : (
          <div className=" flex w-full items-center justify-center">
            <form className=" w-full " onSubmit={editLecturer}>
              <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
                <TextField
                  type="text"
                  onChange={onChange}
                  value={form.firstName}
                  name="firstName"
                  id="firstName"
                  label="First Name"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  required
                />
                <TextField
                  type="text"
                  onChange={onChange}
                  value={form.lastName}
                  label="Last Name"
                  name="lastName"
                  id="lastName"
                  InputLabelProps={{ shrink: true }}
                  fullWidth
                  required
                />
              </Stack>
              <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
                <TextField
                  type="email"
                  onChange={onChange}
                  label="Email"
                  value={form.email}
                  name="email"
                  InputLabelProps={{ shrink: true }}
                  id="email"
                  fullWidth
                  required
                />
                <TextField
                  type="text"
                  onChange={onChange}
                  value={form.contact}
                  name="contact"
                  id="contact"
                  InputLabelProps={{ shrink: true }}
                  label="Phone Number"
                  fullWidth
                  required
                />
              </Stack>
              <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
                <TextField
                  type="date"
                  onChange={onChange}
                  value={form.dob}
                  name="dob"
                  id="dob"
                  label="Date of Birth"
                  InputLabelProps={{ shrink: true }} // Keeps label visible when there's a value
                  inputProps={{
                    max: new Date().toISOString().split("T")[0], // Set max date to today
                  }}
                  fullWidth
                  required
                />
                <TextField
                  defaultValue="Female"
                  onChange={onChange}
                  value={form.gender}
                  name="gender"
                  id="gender"
                  label="Gender"
                  fullWidth
                  required
                  select
                >
                  {lectureresGender.map((option) => (
                    <MenuItem key={option.id} value={option.gender}>
                      {option.gender}
                    </MenuItem>
                  ))}
                </TextField>
              </Stack>

              <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
                <TextField
                  defaultValue=""
                  select
                  onChange={onChange}
                  value={form.courses}
                  name="courses"
                  id="courses"
                  label="Course"
                  fullWidth
                >
                  {isCoursesLoading ? (
                    <Spinner size="14px" />
                  ) : (
                    courses?.data.map((option) => (
                      <MenuItem key={option.id} value={option.id}>
                        {option.name}
                      </MenuItem>
                    ))
                  )}
                </TextField>
                <TextField
                  defaultValue=""
                  onChange={onChange}
                  value={form.level}
                  name="level"
                  label="Level"
                  id="level"
                  fullWidth
                  select
                >
                  {isLevelsLoading ? (
                    <Spinner size="14px" />
                  ) : (
                    levels?.data?.map((option) => (
                      <MenuItem key={option.id} value={option.id}>
                        {option.name}
                      </MenuItem>
                    ))
                  )}
                </TextField>
              </Stack>

              {/* User Profile picture */}

              <div className="w-full flex items-center justify-between py-4">
                <div className=" flex gap-8 items-center mb-4">
                  <img
                    src={
                      image
                        ? URL.createObjectURL(image)
                        : lecturer?.data?.profile_image
                          ? `${BASE_URL.replace(/api\/|v1/g, "")}${lecturer?.data?.profile_image}`
                          : "https://aui.atlassian.com/aui/9.3/docs/images/avatar-person.svg"
                    }
                    alt=""
                    className=" w-32 h-32 rounded-full object-cover"
                  />
                  <Stack direction={"column"} gap={2}>
                    <p className="text-black font-semibold text-lg">
                      Update Profile Picture
                    </p>
                    <input
                      type="file"
                      accept="image/png, image/jpeg"
                      capture="environment"
                      id="image-input"
                      onChange={(e) => setImage(e.target.files[0])}
                    />
                  </Stack>
                </div>
                {isUpdatingLecturer ? (
                  <Spinner />
                ) : (
                  <Button
                    variant="contained"
                    color="success"
                    style={{ textTransform: "capitalize", width: "20%" }}
                    type="submit"
                  >
                    Save
                  </Button>
                )}
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}

export default EditLecturer;
