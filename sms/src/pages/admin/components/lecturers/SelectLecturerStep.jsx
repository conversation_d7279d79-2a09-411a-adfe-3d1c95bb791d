import { AddLecturer, AllLecturers } from "@pages/admin/components/lecturers";
import PropTypes from "prop-types";

function SelectLecturerStep({ step }) {
  switch (step) {
    case 0:
      return <AllLecturers />;
    case 1:
      return <AddLecturer />;
    default:
      throw new Error("Process was not informed. Include in case list.");
  }
}

// Add prop types validation
SelectLecturerStep.propTypes = {
  step: PropTypes.number.isRequired, // Validate that step is a required number
};

export default SelectLecturerStep;
