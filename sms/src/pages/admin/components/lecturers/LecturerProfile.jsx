import { useGetLecturerByIdQuery } from "@app";
import { Spinner } from "@components";
import { BASE_URL } from "@lib/config";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EmailOutlinedIcon from "@mui/icons-material/EmailOutlined";
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined";
import { Button, Stack } from "@mui/material";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

function LecturerProfile() {
  const { id } = useParams();
  const { data, status, isLoading } = useGetLecturerByIdQuery(
    id,
    "get-single-lecturer"
  );
  useEffect(() => {
    console.log(data?.data);
  }, [status]);
  const navigate = useNavigate();
  return (
    <div className=" md:px-16 md:py-10 w-full flex flex-col items-start  justify-between">
      <div className="bg-white p-10 w-full flex flex-col items-start  justify-between">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">
              Lecturer Information
            </p>
          </div>
          <Button
            variant="contained"
            color="success"
            startIcon={<EditOutlinedIcon />}
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate(`/admin/lecturers/${id}/edit`)}
          >
            Edit
          </Button>
        </div>
        <div className="w-full flex items-start justify-between">
          <div className="w-full items-center justify-center flex flex-col">
            <img
              src={
                data?.data?.profile_image
                  ? `${BASE_URL.replace(/api\/|v1/g, "")}${data?.data?.profile_image}`
                  : "https://aui.atlassian.com/aui/9.3/docs/images/avatar-person.svg"
              }
              alt=""
              className="md:w-48 md:h-48 sm:w-32 sm:h-32 rounded-xl object-cover"
            />
            <Stack
              direction="row"
              gap={4}
              justifyContent={"center"}
              alignItems={"center"}
              className="my-4 "
            >
              <div className="bg-green rounded-full cursor-pointer p-3 ">
                <EmailOutlinedIcon color="" />
              </div>
              <div className="bg-green rounded-full cursor-pointer p-3 ">
                <PhoneOutlinedIcon color="" />
              </div>
            </Stack>
          </div>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="w-full flex flex-col items-start gap-3">
              <div className="flex flex-col items-start gap-2">
                <p className="text-black font-normal text-sm">Name</p>
                <p className="text-black font-semibold text-md">
                  {data?.data?.first_name} {data?.data?.last_name}
                </p>
              </div>
              <div className="flex flex-col items-start gap-2">
                <p className="text-black font-normal text-sm">Gender</p>
                <p className="text-black font-semibold text-md">
                  {data?.data?.gender ?? "N/a"}
                </p>
              </div>
              <div className="flex flex-col items-start gap-2">
                <p className="text-black font-normal text-sm">Date of Birth</p>
                <p className="text-black font-semibold text-md">
                  {" "}
                  {data?.data?.dob ?? "N/a"}
                </p>
              </div>

              <div className="w-full flex items-center justify-between mb-4">
                <div className="flex flex-col items-start gap-2">
                  <p className="text-black font-normal text-sm">Contact</p>
                  <p className="text-black font-semibold text-md">
                    {" "}
                    {data?.data?.phone_number ?? "N/a"}
                  </p>
                </div>
              </div>
              {/* <div className="w-full flex items-center justify-between mb-4">
                <div className="flex flex-col items-start gap-2">
                  <p className="text-black font-normal text-sm">Courses</p>
                  <p className="text-black font-semibold text-md">N/A</p>
                </div>
              </div>

              <div className="w-full flex items-center justify-between mb-4">
                <div className="flex flex-col items-start gap-2">
                  <p className="text-black font-normal text-sm">Levels</p>
                  <p className="text-black font-semibold text-md">N/A</p>
                </div>
              </div> */}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default LecturerProfile;
