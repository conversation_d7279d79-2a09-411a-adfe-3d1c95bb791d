import { useGetAllLecturersQuery } from "@app";
import { <PERSON><PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { stringAvatar } from "@lib/util";
import { Delete, EditOutlined, Visibility } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Avatar,
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllLecturers() {
  const [filter, setFilter] = useState("Name"); // Active filter (Name or Email)
  const [searchQuery, setSearchQuery] = useState(""); // Search query input
  const [page, setPage] = useState(1); // Current page number

  const { data, status, isLoading } =
    useGetAllLecturersQuery("get-all-lecturers");

  const navigate = useNavigate();

  // Handle filter change (Name or Email)
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery("");
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  // Filter lecturers based on search query and selected filter
  const filteredLecturers = data?.data?.filter((lecturer) => {
    const valueToFilter =
      filter === "Name"
        ? `${lecturer?.first_name} ${lecturer?.last_name}`
        : lecturer?.email;

    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Handle pagination change
  const handleTableChange = (_event, value) => {
    setPage(value);
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex items-start justify-between mb-4">
        <p className="text-green text-sm font-normal">
          Welcome {data?.user?.first_name} {data?.user?.last_name},
        </p>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="success"
          style={{ textTransform: "capitalize" }}
          onClick={() => navigate("/admin/lecturers/add")}
        >
          Add Lecturer
        </Button>
      </div>

      <div className="w-full flex flex-col items-start h-full">
        <p className="text-black text-lg font-semibold mb-4">All Lecturers</p>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            {/* Search Input */}
            <Box>
              <TextField
                fullWidth
                size="small"
                label={`Search by ${filter}`}
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {/* Filter Dropdown */}
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  onChange={handleFilterChange}
                >
                  <MenuItem value="Name">Name</MenuItem>
                  <MenuItem value="Email">Email</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>

          {/* Import/Export Buttons */}
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
                disabled
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={() => navigate("/admin/lecturers/upload")}
              >
                Import CSV
              </Button>
            </Stack>
          </div>
        </div>

        {/* Table Section */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Name</StyledTableCell>
                  <StyledTableCell>Email</StyledTableCell>
                  <StyledTableCell>Gender</StyledTableCell>
                  <StyledTableCell>Contact</StyledTableCell>
                  <StyledTableCell>Created At</StyledTableCell>
                  <StyledTableCell>Updated At</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : filteredLecturers?.length < 1 ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={8} align="center">
                      No lecturers found
                    </StyledTableCell>
                  </StyledTableRow>
                ) : (
                  filteredLecturers?.map((lecturer, idx) => (
                    <StyledTableRow key={lecturer.id}>
                      <StyledTableCell>
                        {(page - 1) * 10 + idx + 1}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-2">
                          <Avatar
                            {...stringAvatar(
                              `${lecturer?.first_name} ${lecturer?.last_name}`
                            )}
                            size="small"
                          />
                          {`${lecturer?.first_name} ${lecturer?.last_name}`}
                        </div>
                      </StyledTableCell>
                      <StyledTableCell>
                        {lecturer?.email ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {lecturer?.gender ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {lecturer?.phone_number ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {lecturer?.created_at ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {lecturer?.updated_at ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <Visibility
                            color="primary"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(`/admin/lecturers/${lecturer.id}`)
                            }
                          />
                          <EditOutlined
                            color="success"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(`/admin/lecturers/${lecturer.id}/edit`)
                            }
                          />
                          <Delete
                            color="error"
                            sx={{ cursor: "pointer" }}
                            onClick={() => handleDeleteLecturer(lecturer.id)}
                          />
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={10}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllLecturers;
