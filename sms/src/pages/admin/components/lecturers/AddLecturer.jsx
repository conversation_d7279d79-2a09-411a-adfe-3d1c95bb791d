import { useCreateLecturerMutation } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import {
  Button,
  IconButton,
  InputAdornment,
  MenuItem,
  Stack,
  TextField,
} from "@mui/material";
import { lectureresGender } from "@pages/admin/components/lecturers/data";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddLecturer() {
  /** Form state. level and courses would be fetched from the server.
   * So they eventually would manage their own states
   */
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    contact: "",
    dob: "",
    gender: "",
    courses: "",
    level: "",
    password: "",
    confirmPassword: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [image, setImage] = useState(null);

  const handleTogglePasswordVisibility = () => setShowPassword(!showPassword);
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const [addLecturer, { isLoading: isCreatingLecturer }] =
    useCreateLecturerMutation("create-lecturer");

  const navigate = useNavigate();

  const createLecturer = async (e) => {
    e.preventDefault();
    try {
      const formData = new FormData();
      formData.append("first_name", form.firstName);
      formData.append("last_name", form.lastName);
      formData.append("email", form.email);
      formData.append("password", form.password);
      formData.append("password_confirmation", form.confirmPassword);
      formData.append("phone_number", form.contact);
      formData.append("dob", form.dob);
      formData.append("gender", form.gender);
      formData.append("profile_image", image);
      const { message, ...res } = await addLecturer(formData).unwrap();
      console.log(res);
      alert(message);
    } catch (error) {
      console.log(error);
      alert("Could not create lecturer");
    }
  };
  return (
    <div className="w-full flex flex-col items-start px-16 py-10  justify-between">
      <div className="w-full flex flex-col p-10 bg-white">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate("/admin/lecturers")} />
            </div>
            <p className="text-black font-semibold text-lg">Add Lecturer</p>
          </div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
          >
            Import Csv
          </Button>
        </div>
        <p className="text-green text-md font-semibold mb-4">
          Basic Information
        </p>

        <div className=" flex w-full items-center justify-center">
          <form className=" w-full " onSubmit={createLecturer}>
            <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.firstName}
                name="firstName"
                id="firstName"
                label="First Name"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.lastName}
                label="Last Name"
                name="lastName"
                id="lastName"
                fullWidth
                required
              />
            </Stack>
            <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="email"
                onChange={onChange}
                label="Email"
                value={form.email}
                name="email"
                id="email"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.contact}
                name="contact"
                id="contact"
                label="Phone Number"
                fullWidth
                required
              />
            </Stack>
            <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="date"
                onChange={onChange}
                value={form.dob}
                name="dob"
                id="dob"
                label="Date of Birth"
                fullWidth
                required
              />
              <TextField
                defaultValue="Female"
                onChange={onChange}
                value={form.gender}
                name="gender"
                id="gender"
                label="Gender"
                fullWidth
                required
                select
              >
                {lectureresGender.map((option) => (
                  <MenuItem key={option.id} value={option.gender}>
                    {option.gender}
                  </MenuItem>
                ))}
              </TextField>
            </Stack>

            <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type={showPassword ? "text" : "password"}
                onChange={onChange}
                value={form.password}
                name="password"
                id="password"
                label="Password"
                fullWidth
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment sx={{ width: "32px" }}>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                type={showPassword ? "text" : "password"}
                onChange={onChange}
                value={form.confirmPassword}
                name="confirmPassword"
                id="confirmPassword"
                label="Confirm Password"
                fullWidth
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment sx={{ width: "32px" }}>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Stack>
            <div className="w-full flex items-center justify-between py-4">
              <div className=" flex gap-8 items-center mb-4">
                <img
                  src={
                    image
                      ? URL.createObjectURL(image)
                      : "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  }
                  alt=""
                  className=" w-32 h-32 rounded-full object-cover"
                />
                <Stack direction={"column"} gap={2}>
                  <p className="text-black font-semibold text-lg">
                    Update Profile Picture
                  </p>
                  <input
                    type="file"
                    accept="image/*"
                    capture="environment"
                    id="image-input"
                    onChange={(e) => setImage(e.target.files[0])}
                  />
                </Stack>
              </div>
              {isCreatingLecturer ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize", width: "20%" }}
                  type="submit"
                >
                  Save
                </Button>
              )}
            </div>
          </form>
        </div>
        {/* User Profile picture */}
      </div>
    </div>
  );
}

export default AddLecturer;
