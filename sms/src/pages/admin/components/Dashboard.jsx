import { Summary } from "@pages/admin/components";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

function Dashboard() {
  const { user, role } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      window.location.replace("/admin/login");
    }
    if (role == "student") {
      navigate("/student");
    }
    if (role == "accountant") {
      navigate("/finance");
    }
  }, []);
  return (
    <div className="w-full flex flex-col items-center justify-center">
      <Summary />
    </div>
  );
}

export default Dashboard;
