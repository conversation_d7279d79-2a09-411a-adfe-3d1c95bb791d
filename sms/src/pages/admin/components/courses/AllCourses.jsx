import { useDeleteCourseMutation, useGetAllCoursesQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { RequestInterceptor } from "@lib/util";
import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllCourses() {
  const [filter, setFilter] = useState("Course Name"); // Active filter
  const [searchQuery, setSearchQuery] = useState(""); // Search input state
  const [page, setPage] = useState(1); // Current page number

  const { data, isLoading, status } = useGetAllCoursesQuery("all-courses");
  const [deleteCourse, { isLoading: isDeletingCourse }] =
    useDeleteCourseMutation("Delete course");

  useEffect(() => {
    console.log(data);
  }, [status]);

  const handleChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery(""); // Clear search query when filter changes
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  // Filter courses based on search query and selected filter (Course Name, Code, Level)
  const filteredCourses = data?.data?.filter((course) => {
    const valueToFilter =
      filter === "Course Code"
        ? course.code
        : filter === "Course Name"
          ? course.name
          : course?.level?.name; // Adjust if needed for levels
    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Handle pagination change
  const handleTableChange = (_event, value) => {
    setPage(value);
  };

  const navigate = useNavigate();

  const deleteSingleCourse = async (courseId) => {
    await RequestInterceptor.handleRequest(
      () => deleteCourse(courseId),
      { shouldConfirm: true, onSuccess: () => window.location.reload() },
      "Delete Course"
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full flex items-start justify-between mb-4">
        <p className="text-green text-sm font-normal">
          Here's an overview of all courses
        </p>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="success"
          style={{ textTransform: "capitalize" }}
          onClick={() => navigate("/admin/courses/add")}
        >
          Courses
        </Button>
      </div>

      <div className="w-full flex flex-col items-start h-full">
        <p className="text-black text-lg font-semibold mb-4">All Courses</p>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            {/* Search Input */}
            <Box>
              <TextField
                fullWidth
                size="small"
                label={`Search by ${filter}`}
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment>
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {/* Filter Dropdown */}
            <Box sx={{ minWidth: 120 }}>
              <FormControl fullWidth>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  onChange={handleChange}
                >
                  <MenuItem value="Course Name">Course Name</MenuItem>
                  <MenuItem value="Course Code">Course Code</MenuItem>
                  <MenuItem value="Level">Level</MenuItem>
                </TextField>
              </FormControl>
            </Box>
          </div>

          {/* Import/Export Buttons */}
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
                disabled
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={() => navigate("/admin/courses/upload")}
              >
                Import CSV
              </Button>
            </Stack>
          </div>
        </div>

        {/* Table Section */}
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Course Name</StyledTableCell>
                  <StyledTableCell>Course Code</StyledTableCell>
                  <StyledTableCell>Department</StyledTableCell>
                  <StyledTableCell>Level</StyledTableCell>
                  <StyledTableCell>Lecturer</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <Spinner />
                ) : filteredCourses?.length < 1 ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={7} align="center">
                      No courses found
                    </StyledTableCell>
                  </StyledTableRow>
                ) : (
                  filteredCourses?.map((course, idx) => (
                    <StyledTableRow key={course.id}>
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell>{course.name}</StyledTableCell>
                      <StyledTableCell>{course.code}</StyledTableCell>
                      <StyledTableCell>
                        {course?.department?.name}
                      </StyledTableCell>
                      <StyledTableCell>{course?.level?.name}</StyledTableCell>
                      <StyledTableCell>
                        {course?.lecturer?.first_name}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <VisibilityIcon
                            color="primary"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(`/admin/courses/${course.id}`)
                            }
                          />
                          <EditOutlinedIcon
                            color="success"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(`/admin/courses/${course.id}/edit`)
                            }
                          />
                          {isDeletingCourse ? (
                            <Spinner size="17px" />
                          ) : (
                            <DeleteOutlineOutlinedIcon
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() => deleteSingleCourse(course.id)}
                            />
                          )}
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={10}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllCourses;
