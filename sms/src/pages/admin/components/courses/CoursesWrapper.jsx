import { AdminProvider } from "@pages/admin/providers";
import PropTypes from "prop-types";

function CoursesWrapper({ children }) {
  return (
    <AdminProvider>
      <section className="min-h-screen w-full py-5 flex flex-col items-start">
        <div className="w-full items-start mb-2">
          <div className="w-full items-start mb-3">
            <p className="text-black text-md font-semibold">Courses</p>
            <div className="w-[35px] h-[2px] bg-red"></div>
          </div>
        </div>
        <div className="mb-3 w-full flex flex-col items-center justify-center">
          {children}
        </div>
        <div className="w-full flex items-center justify-center">
          <p className="text-grey_500 text-sm">
            &copy; 2024 Copyright{" "}
            <span className="text-primary text-sm">Skye8.tech</span>
          </p>
        </div>
      </section>
    </AdminProvider>
  );
}

// Add prop types validation
CoursesWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

export default CoursesWrapper;
