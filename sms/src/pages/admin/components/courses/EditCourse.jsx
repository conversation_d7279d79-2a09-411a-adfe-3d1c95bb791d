import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { <PERSON><PERSON>, <PERSON>ack, TextField } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function EditCourse() {
  const navigate = useNavigate();
  const [form, setForm] = useState({
    courseTitle: "Introduction to Machine Learning",
    courseCode: "EHIST123",
    department: "Software Engineering",
    level: "200",
    lecturer: "Abongwi Darren",
  });

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  return (
    <div className="w-full flex flex-col items-start h-ful md:px-16 md:py-10">
      <div className="w-full flex flex-col items-start bg-white p-10">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate("/admin/courses")} />
            </div>
            <p className="text-black font-semibold text-lg">
              Edit Course Information
            </p>
          </div>
        </div>
        <div className="w-full flex flex-col items-start">
          <Stack
            direction="row"
            gap={2}
            style={{ width: "100%" }}
            marginBottom={4}
          >
            <div className="w-1/6">
              <p className="text-black font-semibold text-md ">Course Title</p>
            </div>

            <TextField
              onChange={onChange}
              type="text"
              name="courseTitle"
              id="courseTitle"
              label="Course Title"
              value={form.courseTitle}
              fullWidth
            />
          </Stack>
          <Stack
            direction="row"
            gap={2}
            style={{ width: "100%" }}
            marginBottom={4}
          >
            <div className="w-1/6">
              <p className="text-black font-semibold text-md ">Course Code</p>
            </div>
            <TextField
              onChange={onChange}
              type="text"
              name="courseCode"
              id="courseCode"
              label="Course Code"
              value={form.courseCode}
              fullWidth
            />
          </Stack>

          <Stack
            direction="row"
            gap={2}
            style={{ width: "100%" }}
            marginBottom={4}
          >
            <div className="w-1/6">
              <p className="text-black font-semibold text-md ">Level</p>
            </div>
            <TextField
              onChange={onChange}
              type="text"
              name="level"
              id="level"
              label="Level"
              value={form.level}
              fullWidth
            />
          </Stack>

          <Stack
            direction="row"
            gap={2}
            style={{ width: "100%" }}
            marginBottom={4}
          >
            <div className="w-1/6">
              <p className="text-black font-semibold text-md ">Department</p>
            </div>
            <TextField
              onChange={onChange}
              type="text"
              name="department"
              id="department"
              label="Department"
              value={form.department}
              fullWidth
            />
          </Stack>

          <Stack
            direction="row"
            gap={2}
            style={{ width: "100%" }}
            marginBottom={4}
          >
            <div className="w-1/6">
              <p className="text-black font-semibold text-md ">Lecturer</p>
            </div>
            <TextField
              onChange={onChange}
              type="text"
              name="lecturer"
              id="lecturer"
              label="Lecturer"
              value={form.lecturer}
              fullWidth
            />
          </Stack>
        </div>
        <div className="w-full flex items-end justify-end">
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
          >
            Update
          </Button>
        </div>
      </div>
    </div>
  );
}

export default EditCourse;
