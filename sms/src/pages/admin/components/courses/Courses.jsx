import { CourseWrapper } from "@pages/admin/components/courses";
import SelectCourseStep from "@pages/admin/components/courses/SelectCourseStep";
import { useAdminContext } from "@pages/admin/hooks";

function MiniWrapper() {
  const { courseStep } = useAdminContext();
  return <SelectCourseStep step={courseStep} />;
}

function Courses() {
  return (
    <CourseWrapper>
      <MiniWrapper />
    </CourseWrapper>
  );
}

export default Courses;
