import {
  useGetAllDepartmentsQuery,
  useGetAllSchoolsQuery,
  useImportCoursesMutation,
} from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function UploadCourse() {
  UploadCourse.tag = "Upload Course";
  const navigate = useNavigate();
  const { data: departments, isLoading: isDepartmentsLoading } =
    useGetAllDepartmentsQuery("all departments");
  const { data: schools, isLoading: isSchoolsLoading } =
    useGetAllSchoolsQuery("schools");
  const [importCourse, { isLoading: isUploadingCourse }] =
    useImportCoursesMutation("import course");
  const [form, setForm] = useState({
    schoolName: "",
    departmentName: "",
  });
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState();

  const handleFileDrop = (ev) => {
    ev.preventDefault();
    setIsDragging(false);
    if (ev.dataTransfer.items) {
      // Use DataTransferItemList interface to access the file(s). For modern
      [...ev.dataTransfer.items].forEach((item) => {
        // If dropped items aren't files, reject them
        // console.log(ite)
        if (item.kind === "file") {
          const file = item.getAsFile();
          setFile(file);
          return; // we only need a single file
        }
      });
    } else {
      [...ev.dataTransfer.files].forEach((file) => {
        if (file.type.startsWith("text")) {
          setFile(file);
          return;
        }
      });
    }
    // console.log(e);
  };

  const onDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };
  const onDragLeave = () => {
    setIsDragging(false);
  };

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const uploadCourse = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("file", file);
    formData.append("school_id", form.schoolName);
    formData.append("department_id", form.departmentName);
    await RequestInterceptor.handleRequest(
      () => importCourse(formData),
      {
        onSuccess: () => {
          setIsDragging(false);
          setFile(null);
          window.location.reload();
        },
      },
      UploadCourse.tag
    );
  };

  return (
    <div className="w-full flex flex-col items-start px-16 py-10 justify-between">
      <div className="w-full flex flex-col items-start justify-between mb-4">
        <div className="flex items-center gap-2 mb-6">
          <div className="hover:bg-white p-2 rounded-full cursor-pointer">
            <ArrowBackIcon onClick={() => navigate("/admin/courses")} />
          </div>
          <p className="text-black font-semibold text-lg">Upload Course</p>
        </div>
        <form className="w-full" onSubmit={uploadCourse}>
          <div className="mb-4 w-full">
            <Stack direction={"row"} gap={2}>
              <TextField
                onChange={onChange}
                name="schoolName"
                id="school"
                label="School"
                value={form.schoolName}
                fullWidth
                select
              >
                {isSchoolsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  schools?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>

              <TextField
                onChange={onChange}
                name="departmentName"
                id="department"
                label="Department"
                value={form.departmentName}
                fullWidth
                select
              >
                {isDepartmentsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  departments?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </Stack>
          </div>

          <div className="flex items-center justify-center w-full mb-4">
            <label
              htmlFor="dropzone-file"
              className="flex flex-col items-center justify-center w-full h-64 border-2 cursor-pointer border-dashed border-green text-green"
              onDrop={handleFileDrop}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
            >
              <div
                className={`flex flex-col items-center justify-center pt-5 pb-6 w-full h-full ${
                  isDragging
                    ? "bg-grey_500 bg-opacity-10 border-4 border-dashed border-green"
                    : ""
                }`}
              >
                <svg
                  className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 16"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                  />
                </svg>

                {file ? (
                  <span className="text-gray-500">{`${file?.name} (Click to Change)`}</span>
                ) : (
                  <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-semibold">Click to upload</span> or
                    drag and drop
                  </p>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  CSV file only
                </p>
              </div>
              <input
                id="dropzone-file"
                type="file"
                className="hidden"
                required
                onChange={(e) => {
                  console.log(e.target.files);
                  setFile(e.target.files[0]);
                }}
              />
            </label>
          </div>
          <div className="w-full">
            {isUploadingCourse ? (
              <Spinner />
            ) : (
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                type="submit"
              >
                Add Course
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default UploadCourse;
