import {
  useCreateCourseMutation,
  useGetAllDepartmentsQuery,
  useGetAllLecturersQuery,
  useGetAllLevelsQuery,
  useGetAllSchoolsQuery,
} from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddCourse() {
  AddCourse.tag = "Add Course";
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("levels");
  const { data: schools, isLoading: isSchoolsLoading } =
    useGetAllSchoolsQuery("schools");
  const { data: departments, isLoading: isDeparmentsLoading } =
    useGetAllDepartmentsQuery("departments");
  const { data: lecturers, isLoading: isLecturersLoading } =
    useGetAllLecturersQuery("lecturers");

  const [addCourse, { isLoading: isAddingCourse }] =
    useCreateCourseMutation("create course");

  const [form, setForm] = useState({
    school_id: "",
    department_id: "",
    name: "",
    code: "",
    lecturer: "",
    level: "",
    credit_value: 1,
    is_general: false,
    credit_hours: 4,
  });

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    const body = {
      name: form.name,
      code: form.code,
      department_id: form.department_id,
      lecturer_id: form.lecturer,
      credit_value: form.credit_value,
      level_id: form.level,
      credit_hours: form.credit_hours,
      is_general: false,
    };
    await RequestInterceptor.handleRequest(
      () => addCourse(body),
      {
        onSuccess: () => {
          setForm({
            school_id: "",
            department_id: "",
            name: "",
            code: "",
            lecturer: "",
            level: "",
            credit_value: "23",
            is_general: false,
            credit_hours: "",
          });
        },
      },
      AddCourse.tag
    );
  };

  return (
    <div className="w-full flex flex-col  items-start md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col p-10 bg-white">
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">Add Course</p>
          </div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate("/admin/courses/upload")}
          >
            Import Csv
          </Button>
        </div>
        <div className="w-full flex flex-col items-start">
          <form className="w-full" onSubmit={handleSubmit}>
            <Stack direction={"row"} gap={2} sx={{ marginBottom: 2 }}>
              <TextField
                onChange={onChange}
                name="school_id"
                id="school"
                label="School"
                value={form.school_id}
                fullWidth
                required
                select
              >
                {isSchoolsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  schools?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>

              <TextField
                onChange={onChange}
                name="department_id"
                id="department_id"
                label="Department"
                value={form.department_id}
                fullWidth
                required
                select
              >
                {isDeparmentsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  departments?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </Stack>
            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="name"
                id="name"
                label="Course Name"
                value={form.name}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                name="code"
                id="code"
                label="Course Code"
                value={form.code}
                fullWidth
                required
              />
            </Stack>

            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="number"
                name="credit_value"
                id="credit_value"
                label="Credit Value"
                value={form.credit_value}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="number"
                name="credit_hours"
                id="credit_hours"
                label="Credit Hours"
                value={form.credit_hours}
                fullWidth
                required
              />
            </Stack>

            <Stack direction={"row"} sx={{ marginBottom: 2 }} gap={2}>
              <TextField
                onChange={onChange}
                type="text"
                name="lecturer"
                id="lecturer"
                label="lecturer"
                value={form.lecturer}
                fullWidth
                required
                select
              >
                {isLecturersLoading ? (
                  <Spinner size="14px" />
                ) : (
                  lecturers?.data?.map((option, idx) => (
                    <MenuItem key={option.id} value={option.id}>
                      {idx + 1}. {option.first_name} {option.last_name} (
                      {option.email})
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                onChange={onChange}
                type="text"
                name="level"
                id="level"
                label="Level"
                value={form.level}
                fullWidth
                required
                select
              >
                {isLevelsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  levels?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </Stack>
            <div className="w-full flex justify-center">
              {isAddingCourse ? (
                <Spinner />
              ) : (
                <Button
                  type="submit"
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Add Course
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default AddCourse;
