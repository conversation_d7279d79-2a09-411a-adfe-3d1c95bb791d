import { useGetCourseByIdQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { Button, Stack } from "@mui/material";
import { DeleteDialog } from "@pages/admin/components/schools";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function ViewCourse() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data, isLoading, status } = useGetCourseByIdQuery(
    id,
    "get-single-course"
  );

  useEffect(() => {
    console.log(data);
  }, [status]);
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  return (
    <div className="md:px-16 md:py-10 w-full flex flex-col items-start justify-between">
      <div className="bg-white p-10 w-full flex flex-col items-start justify-between">
        <div className="w-full flex items-start justify-between  mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full cursor-pointer">
              <ArrowBackIcon onClick={() => navigate(-1, { replace: true })} />
            </div>
            <p className="text-black font-semibold text-lg">
              Course Information
            </p>
          </div>
          <Stack direction="row" gap={2}>
            <Button
              variant="contained"
              startIcon={<EditOutlinedIcon />}
              color="success"
              style={{ textTransform: "capitalize" }}
              onClick={() => navigate(`/admin/courses/${id}/edit`)}
            >
              Edit
            </Button>
            <Button
              variant="contained"
              startIcon={<DeleteOutlineOutlinedIcon />}
              color="error"
              style={{ textTransform: "capitalize" }}
              onClick={() => setOpenSuccessModal(true)}
            >
              Delete
            </Button>
          </Stack>
        </div>

        {isLoading ? (
          <Spinner />
        ) : (
          <div className="w-full flex flex-col items-start">
            <div className="w-full flex flex-col items-start mb-8">
              <p className="text-black font-medium text-sm">Course Title</p>
              <p className="text-black font-semibold text-xl">
                {data?.data?.name ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <p className="text-black font-medium text-sm">Course Code</p>
              <p className="text-black font-semibold text-xl">
                {data?.data?.code ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <p className="text-black font-medium text-sm">Department</p>
              <p className="text-black font-semibold text-xl">
                {data?.data?.department?.name ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <p className="text-black font-medium text-sm">Level</p>
              <p className="text-black font-semibold text-xl">
                {" "}
                {data?.data?.level?.name ?? "N/A"}
              </p>
            </div>

            <div className="w-full flex flex-col items-start mb-8">
              <p className="text-black font-medium text-sm">
                Assigned Lecturer
              </p>
              <p className="text-black font-semibold text-xl">
                {data?.data?.lecturer?.first_name ?? "N/A"}{" "}
                {data?.data?.lecturer?.last_name ?? "N/A"}
              </p>
            </div>
          </div>
        )}

        {openSuccessModal && (
          <DeleteDialog
            openModal={openSuccessModal}
            closeModal={() => setOpenSuccessModal(false)}
          />
        )}
      </div>
    </div>
  );
}

export default ViewCourse;
