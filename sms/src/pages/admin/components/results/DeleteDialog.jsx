import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { Stack } from "@mui/material";
import PropTypes from "prop-types";

function DeleteDialog({ openModal, closeModal }) {
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Are you sure you want to delete this Result?
        </h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          This action cause this information to be removed from the system.
        </p>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"primary"} onClick={closeModal} />
          <Button title={"Delete"} type={"danger"} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
DeleteDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal as a required boolean
  closeModal: PropTypes.func.isRequired, // Validate closeModal as a required function
};

export default DeleteDialog;
