import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Box,
  Button,
  FormControl,
  Grid,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { finaResults } from "@pages/admin/components/results/data";
import { useState } from "react";

import { StyledTableCell, StyledTableRow } from "@components";
import {
  DeleteDialog,
  EditDialog,
  ResultSelection,
} from "@pages/admin/components/results";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function Transcript() {
  const navigate = useNavigate();

  const [filter, setFilter] = useState({
    matricule: "",
    gpa: "",
  });

  const handleChange = (e) =>
    setFilter({ ...filter, [e.target.name]: e.target.value });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(10);
  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };

  const [openEditModal, setOpenEditModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [OpenSelectionModal, setOpenSelectionModal] = useState(false);
  return (
    <ThemeProvider theme={theme}>
      <section className="py-6 w-full flex flex-col items-start justify-between">
        <h2 className="font-semibold text-black text-lg mb-4">
          Semester: First{" "}
        </h2>
        <div className="w-full flex items-start justify-between mb-4">
          <div className="flex items-start gap-2">
            <Box sx={{ minWidth: 180 }}>
              <FormControl fullWidth>
                {/* <InputLabel id="filter">Filter</InputLabel> */}
                <TextField
                  fullWidth
                  select
                  id="matricule"
                  size="small"
                  value={filter.matricule}
                  label="sort by matricule"
                  name="matricule"
                  onChange={handleChange}
                >
                  {finaResults.map((item) => (
                    <MenuItem key={item.id}>{item.matricule}</MenuItem>
                  ))}
                </TextField>
              </FormControl>
            </Box>
            <Box sx={{ minWidth: 180 }}>
              <FormControl fullWidth>
                {/* <InputLabel id="filter">Filter</InputLabel> */}
                <TextField
                  select
                  id="gpa"
                  size="small"
                  value={filter.gpa}
                  label="sort by gpa"
                  name="gpa"
                  onChange={handleChange}
                >
                  {finaResults.map((item) => (
                    <MenuItem key={item.id}>{item.gpa}</MenuItem>
                  ))}
                </TextField>
              </FormControl>
            </Box>
          </div>
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="outlined"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Export
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Import csv
              </Button>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={() => setOpenSelectionModal(true)}
              >
                Change Selection
              </Button>
            </Stack>
          </div>
        </div>
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Matricule</StyledTableCell>
                  <StyledTableCell>Student Name</StyledTableCell>
                  <StyledTableCell>Exams Taken</StyledTableCell>
                  <StyledTableCell>GPA</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {[].length === 0 ? (
                  <>No data</>
                ) : (
                  [].map((item) => (
                    <StyledTableRow key={item.id}>
                      <StyledTableCell>{item.id}</StyledTableCell>
                      <StyledTableCell>{item.matricule}</StyledTableCell>
                      <StyledTableCell>{item.studentName}</StyledTableCell>
                      <StyledTableCell>{item.examsTaken}</StyledTableCell>
                      <StyledTableCell>{item.gpa}</StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <VisibilityIcon
                            color="primary"
                            sx={{ cursor: "pointer" }}
                            onClick={() =>
                              navigate(
                                `/admin/results/transcripts/${item.matricule}`
                              )
                            }
                          />
                          <EditOutlinedIcon
                            color="success"
                            sx={{ cursor: "pointer" }}
                            onClick={() => setOpenEditModal(true)}
                          />
                          <DeleteOutlineOutlinedIcon
                            color="error"
                            sx={{ cursor: "pointer" }}
                            onClick={() => setOpenDeleteModal(true)}
                          />
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>

            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={count}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
        {openEditModal && (
          <EditDialog
            openModal={openEditModal}
            closeModal={() => setOpenEditModal(false)}
          />
        )}
        {openDeleteModal && (
          <DeleteDialog
            openModal={openDeleteModal}
            closeModal={() => setOpenDeleteModal(false)}
          />
        )}
        {OpenSelectionModal && (
          <ResultSelection
            openModal={OpenSelectionModal}
            closeModal={() => setOpenSelectionModal(false)}
          />
        )}
      </section>
    </ThemeProvider>
  );
}

export default Transcript;
