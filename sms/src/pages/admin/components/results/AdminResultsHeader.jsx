import { useGetAllAcademicYearsQuery, useGetAllLevelsQuery } from "@app";
import { Spinner } from "@components";
import { setFilter, setQuery } from "@features/filter/filterSlice";
import { MenuItem, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";

function AdminResultsHeader() {
  const { data: levels, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("get all levels");
  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("get allademic years");
  const { filter } = useSelector((state) => state.filter);
  const dispatch = useDispatch();

  const handleChange = (e) => {
    dispatch(setFilter({ [e.target.name]: e.target.value }));
    dispatch(
      setQuery(
        `level_id=${filter.level}&academic_year_id=${filter.academic_year}&semester=${filter.semester}`
      )
    );
  };
  return (
    <div className="w-full flex flex-col items-center justify-between gap-6">
      <div className="w-full flex md:flex-row flex-col items-center justify-between bg-white shadow-md p-5 rounded-md gap-3">
        <p className="text-black text-sm font-normal">Select: </p>
        <TextField
          select
          id="academic_year"
          size="small"
          value={filter.academic_year}
          label="select year"
          name="academic_year"
          onChange={handleChange}
          fullWidth
        >
          {isAcademicYearsLoading ? (
            <Spinner size="14px" />
          ) : (
            academicYears?.data?.map((item) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name?.replace("_", "/")}
              </MenuItem>
            ))
          )}
        </TextField>
        <TextField
          select
          id="semester"
          size="small"
          value={filter.semester}
          label="select semester"
          name="semester"
          onChange={handleChange}
          fullWidth
        >
          {[1, 2].map((item) => (
            <MenuItem key={item} value={item}>
              {item}
            </MenuItem>
          ))}
        </TextField>

        <TextField
          select
          id="level"
          size="small"
          value={filter.level}
          label="select Level"
          name="level"
          onChange={handleChange}
          fullWidth
        >
          {isLevelsLoading ? (
            <Spinner size="14px" />
          ) : (
            levels?.data.map((item) => (
              <MenuItem key={item.id} value={+item.id}>
                {item.name}
              </MenuItem>
            ))
          )}
        </TextField>
      </div>
    </div>
  );
}

export default AdminResultsHeader;
