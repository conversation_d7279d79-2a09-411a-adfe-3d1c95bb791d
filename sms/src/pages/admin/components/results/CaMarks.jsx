import {
  Box,
  Button,
  Grid,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";

import { useGetCaMarksQuery } from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { useDownload } from "@hooks/useDownload";
import { RequestInterceptor } from "@lib/util";
import SpecialTableCell from "@pages/admin/components/results/components/SpecialTableCell";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function CaMarks() {
  const navigate = useNavigate();
  const [isDownloadingResult, setIsDownloadingResult] = useState(false);

  const [page, setPage] = useState(1);
  const handleTableChange = async (event, value) => setPage(value);

  const { filter } = useSelector((state) => state.filter);
  const {
    data: caMarks,
    isLoading: isCaMarksLoading,
    isFetching: isMarksLoading,
  } = useGetCaMarksQuery(
    `level_id=${filter.level}&academic_year_id=${filter.academic_year}&semester=${filter.semester}&page=${page}`
  );

  const download = useDownload();
  async function downloadCAMarks() {
    try {
      setIsDownloadingResult(true);
      await RequestInterceptor.downloadFile(
        download,
        `results/all-results/print?level_id=${filter.level}&academic_year_id=${filter.academic_year}&semester=${filter.semester}`,
        "Final-Results"
      );
    } catch (error) {
      console.log(error);
    } finally {
      setIsDownloadingResult(false);
    }
  }

  return (
    <ThemeProvider theme={theme}>
      <section className="py-6 w-full flex flex-col items-start justify-between">
        <h2 className="font-semibold text-black text-lg mb-4">
          All CA Results
        </h2>
        <div className="w-full flex items-start justify-between mb-4">
          <div>
            <Stack direction={"row"} gap={2}>
              {isDownloadingResult ? (
                <Spinner />
              ) : (
                <Button
                  variant="outlined"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  onClick={downloadCAMarks}
                >
                  Export
                </Button>
              )}
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
                onClick={() => navigate("/admin/results/ca-marks/upload")}
              >
                Import csv
              </Button>
            </Stack>
          </div>
        </div>
        <div className="flex flex-col w-full my-2">
          <TableContainer component={Paper}>
            <Table sx={{ maxWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Matricule</StyledTableCell>
                  <StyledTableCell>Course Name</StyledTableCell>
                  <StyledTableCell>Course Code</StyledTableCell>
                  <StyledTableCell>CA Mark</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isCaMarksLoading || isMarksLoading ? (
                  <Spinner />
                ) : caMarks?.data?.data?.length < 1 ? (
                  <>No data</>
                ) : (
                  caMarks?.data?.data?.map((item, idx) => (
                    <StyledTableRow key={item.id}>
                      <StyledTableCell>{idx + 1}</StyledTableCell>
                      <StyledTableCell>
                        {item.student?.matricule ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {item.course?.name ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>
                        {item.course?.code ?? "N/A"}
                      </StyledTableCell>
                      <StyledTableCell>{item.score ?? "N/A"}</StyledTableCell>

                      <SpecialTableCell student={item.student} />
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <Grid
              display={"flex"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              my={3}
            >
              <Box>
                <Stack spacing={2}>
                  <Pagination
                    count={caMarks?.data?.total}
                    page={page}
                    onChange={handleTableChange}
                    showFirstButton
                    showLastButton
                    color="green"
                  />
                </Stack>
              </Box>
            </Grid>
          </TableContainer>
        </div>
      </section>
    </ThemeProvider>
  );
}

export default CaMarks;
