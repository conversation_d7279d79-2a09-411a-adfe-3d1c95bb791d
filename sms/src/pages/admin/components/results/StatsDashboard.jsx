import {
  Assignment,
  Book,
  Business,
  Class,
  EmojiEvents,
  Person,
  School,
} from "@mui/icons-material"; // Import MUI icons
import { Box, Grid, Typography } from "@mui/material";
import { BarChart } from "@mui/x-charts/BarChart";
import PropTypes from "prop-types"; // Import PropTypes

const StatCard = ({ title, value, subtitle, color, icon }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${color}`}>
    <div className="flex gap-2 items-center">
      {icon}
      <h3
        className={`text-sm font-semibold text-${color.split("-")[3]} mb-2 text-nowrap relative top-1`}
      >
        {title}
      </h3>
    </div>
    <p className="text-lg font-bold text-gray-700 my-2">{value}</p>
    <p className="text-sm text-gray-600">{subtitle}</p>
  </div>
);

StatCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
  icon: PropTypes.element.isRequired,
};

const IconMap = {
  "Best Course": Assignment,
  "Best Department": School,
  "Best Level": Class,
  "Overall School Performance": EmojiEvents,
};

const ColorMap = {
  "Best Course": "text-purple",
  "Best Department": "text-green",
  "Best Level": "text-orange",
  "Overall School Performance": "text-blue",
};

const PerformanceItem = ({ title, value, details }) => {
  const IconComponent = IconMap[title] || School;
  const colorClass = ColorMap[title] || "text-blue-500";

  return (
    <div className="bg-white rounded-lg shadow-md p-4 flex flex-col w-full h-full">
      <div className="flex items-center mb-3">
        <IconComponent className={`${colorClass} mr-2`} size={24} />
        <h3 className={`text-md font-semibold ${colorClass} `}>{title}</h3>
      </div>
      <p className={`text-lg font-bold mb-2 text-gray-700`}>{value}</p>
      <p className="text-sm text-gray-600 mt-auto">{details}</p>
    </div>
  );
};

PerformanceItem.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  details: PropTypes.string.isRequired,
};

const StatsDashboard = ({ data }) => {
  const chartData = [
    data?.best_course?.average_grade_point,
    data?.best_department?.average_grade_point,
    data?.best_level?.average_grade_point,
    data?.best_school?.average_grade_point,
  ];
  return (
    <div className="shadow min-h-screen w-full p-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">
        Academic Performance Dashboard
      </h1>

      <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Best Student"
          value={data?.best_student?.student.matricule}
          subtitle={`gpa: ${data?.best_student?.gpa}`}
          color="border-l-4 border-green"
          icon={<School className="text-green" fontSize="large" />}
        />
        <StatCard
          title="Worst Student"
          value={data?.worst_student?.student?.matricule}
          subtitle={`gpa: ${data.worst_student.gpa}`}
          color="border-l-4 border-red"
          icon={<Person className="text-red" fontSize="large" />}
        />
        <StatCard
          title="Best Course"
          value={data?.best_course?.top_student?.course.name}
          subtitle={`Avg. Grade Point: ${data?.best_course?.average_grade_point?.toFixed(2)}`}
          color="border-l-4 border-blue"
          icon={<Book className="text-blue" fontSize="large" />}
        />
        <StatCard
          title="Best Department"
          value={`Avg. GPA: ${data?.best_department.average_grade_point?.toFixed(2)}`}
          subtitle={`Top Student: ${data?.best_department.top_student?.student?.matricule}`}
          color="border-l-4 border-purple"
          icon={<Business className="text-purple" fontSize="large" />}
        />
      </div>

      <div className="bg-white rounded-lg w-full shadow-sm mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Performance Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <PerformanceItem
            title="Best Course"
            value={data.best_course.top_student.course.code}
            details={`${data.best_course.top_student.course.name} - Avg. GPA: ${data.best_course.average_grade_point.toFixed(2)}`}
          />
          <PerformanceItem
            title="Best Department"
            value={`Avg. GPA: ${data.best_department.average_grade_point.toFixed(2)}`}
            details={`Top Student: ${data.best_department.top_student.student.matricule}`}
          />
          <PerformanceItem
            title="Best Level"
            value={`Level ${data.best_level.top_student.level.name}`}
            details={`Avg. GPA: ${data.best_level.average_grade_point.toFixed(2)}`}
          />
          <PerformanceItem
            title="Overall School Performance"
            value={`Avg. GPA: ${data.best_school.average_grade_point.toFixed(2)}`}
            details={`Top Student: ${data.best_school.top_student.student.matricule}`}
          />
        </div>
      </div>
      <Grid item xs={12}>
        <div className="">
          <div className="">
            <Typography variant="h6" sx={{ fontWeight: "bold" }}>
              Performance Comparison
            </Typography>
            <Box sx={{ width: "100%", height: 300 }}>
              <BarChart
                xAxis={[
                  {
                    scaleType: "band",
                    data: [
                      "Best Course",
                      "Best Department",
                      "Best Level",
                      "Best School",
                    ],
                    label: "CA",
                    fill: "green",
                  },
                ]}
                series={[{ data: chartData }]}
                width={500}
                height={300}
              />
            </Box>
          </div>
        </div>
      </Grid>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          CA Performance Overview
        </h2>
        <div className="space-y-4">
          <div className="flex items-center">
            <span className="w-32 text-gray-600">Best Course:</span>
            <div className="flex-1 bg-gray-200 rounded-full h-4">
              <div
                className="bg-blue rounded-full h-4"
                style={{
                  width: `${(data.best_course.average_grade_point / 4) * 100}%`,
                }}
              ></div>
            </div>
            <span className="ml-4 font-semibold">
              {data.best_course.average_grade_point.toFixed(2)}
            </span>
          </div>
          <div className="flex items-center">
            <span className="w-32 text-gray-600">Best Department:</span>
            <div className="flex-1 bg-gray-200 rounded-full h-4">
              <div
                className="bg-purple rounded-full h-4"
                style={{
                  width: `${(data.best_department.average_grade_point / 4) * 100}%`,
                }}
              ></div>
            </div>
            <span className="ml-4 font-semibold">
              {data.best_department.average_grade_point.toFixed(2)}
            </span>
          </div>
          <div className="flex items-center">
            <span className="w-32 text-gray-600">Best Level:</span>
            <div className="flex-1 bg-gray-200 rounded-full h-4">
              <div
                className="bg-green rounded-full h-4"
                style={{
                  width: `${(data.best_level.average_grade_point / 4) * 100}%`,
                }}
              ></div>
            </div>
            <span className="ml-4 font-semibold">
              {data.best_level.average_grade_point.toFixed(2)}
            </span>
          </div>
          <div className="flex items-center">
            <span className="w-32 text-gray-600">Best School:</span>
            <div className="flex-1 bg-gray-200 rounded-full h-4">
              <div
                className="bg-pink rounded-full h-4"
                style={{
                  width: `${(data.best_school.average_grade_point / 4) * 100}%`,
                }}
              ></div>
            </div>
            <span className="ml-4 font-semibold">
              {data.best_school.average_grade_point.toFixed(2)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

StatsDashboard.propTypes = {
  data: PropTypes.shape({
    best_course: PropTypes.shape({
      average_grade_point: PropTypes.number,
      top_student: PropTypes.shape({
        course: PropTypes.shape({
          name: PropTypes.string,
          code: PropTypes.string,
        }),
      }),
    }),
    best_department: PropTypes.shape({
      average_grade_point: PropTypes.number,
      top_student: PropTypes.shape({
        student: PropTypes.shape({
          matricule: PropTypes.string,
        }),
      }),
    }),
    best_level: PropTypes.shape({
      average_grade_point: PropTypes.number,
      top_student: PropTypes.shape({
        level: PropTypes.shape({
          name: PropTypes.string,
        }),
      }),
    }),
    best_school: PropTypes.shape({
      average_grade_point: PropTypes.number,
      top_student: PropTypes.shape({
        student: PropTypes.shape({
          matricule: PropTypes.string,
        }),
      }),
    }),
    best_student: PropTypes.shape({
      student: PropTypes.shape({
        matricule: PropTypes.string,
      }),
      gpa: PropTypes.number,
    }),
    worst_student: PropTypes.shape({
      student: PropTypes.shape({
        matricule: PropTypes.string,
      }),
      gpa: PropTypes.number,
    }),
  }).isRequired,
};

export default StatsDashboard;
