import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function EditDialog({ openModal, closeModal }) {
  const [form, setForm] = useState({
    courseCode: "COME0124",
    courseTitle: "Machine Learning",
    matricule: "EHIST1201",
    mark: "25",
  });
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Edit CA Mark</h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          Fill the field to edit the marks
        </p>
        <form className="w-full ">
          <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
            <TextField
              type="text"
              onChange={onChange}
              value={form.courseCode}
              name="courseCode"
              id="courseCode"
              label="Course Code"
              fullWidth
              required
            />
            <TextField
              type="text"
              onChange={onChange}
              value={form.courseTitle}
              label="Course Title"
              name="courseTitle"
              id="courseTitle"
              fullWidth
              required
            />
          </Stack>
          <Stack direction={2} sx={{ marginBottom: 4 }} gap={2}>
            <TextField
              type="text"
              onChange={onChange}
              value={form.courseCode}
              name="matricule"
              id="matricule"
              label="Matricule"
              fullWidth
              required
            />
            <TextField
              type="number"
              onChange={onChange}
              value={form.mark}
              label="CA Mark"
              name="mark"
              id="mark"
              fullWidth
              required
            />
          </Stack>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          <Button title={"Update"} type={"primary"} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
EditDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal prop
  closeModal: PropTypes.func.isRequired, // Validate closeModal prop
};

export default EditDialog;
