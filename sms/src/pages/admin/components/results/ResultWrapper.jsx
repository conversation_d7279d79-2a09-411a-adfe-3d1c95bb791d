import { BookOutlined } from "@mui/icons-material";
import { Button } from "@mui/material";
import AdminResultsHeader from "@pages/admin/components/results/AdminResultsHeader";
import PropTypes from "prop-types"; // Add this import
import { Link, useLocation, useNavigate } from "react-router-dom";

function ResultWrapper({ children }) {
  const { pathname } = useLocation();
  console.log(pathname);
  const navigate = useNavigate();

  const navItems = [
    {
      id: "1",
      name: "CA Marks",
      itemLink: "/admin/results/ca-marks",
    },
    {
      id: "2",
      name: "Final results",
      itemLink: "/admin/results/final-results",
    },
    {
      id: "3",
      name: "Transcripts",
      itemLink: "/admin/results/transcripts",
    },
  ];

  return (
    <section className="min-h-screen w-full py-5 flex flex-col items-start">
      <AdminResultsHeader />
      <div className="my-2">
        {pathname?.split("/")[3] != "statistics" && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<BookOutlined />}
            onClick={() => navigate("/admin/results/statistics")}
            className="text-2xl"
          >
            View Statistics
          </Button>
        )}
      </div>
      <div className="w-full items-start mb-3">
        <p className="text-black text-md font-semibold">Results</p>
        <div className="w-[35px] h-[2px] bg-red"></div>
      </div>
      <div className="w-full flex flex-col bg-white">
        <ul className="w-full flex items-center justify-between px-10 py-5">
          {navItems.map((item) => (
            <Link to={item.itemLink} key={item.id}>
              <li
                className={`text-md text-black font-semibold hover:text-green ${
                  pathname === item.itemLink
                    ? "text-green font-semibold text-md underline underline-offset-4 decoration-green"
                    : ""
                }`}
              >
                {item.name}
              </li>
            </Link>
          ))}
        </ul>
      </div>

      {children}
    </section>
  );
}

// Add PropTypes validation
ResultWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Validate 'children' prop
};

export default ResultWrapper;
