import { useGetExamMarksQuery } from "@app";
import { <PERSON><PERSON>, StyledTableCell, StyledTableRow } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { DeleteDialog } from "@pages/admin/components/results";
import { firstSemesterResults } from "@pages/admin/components/results/data";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function ViewFinalResults() {
  const navigate = useNavigate();

  const [filter, setFilter] = useState({
    matricule: "",
    course: "",
    gpa: "",
  });

  const handleChange = (e) =>
    setFilter({ ...filter, [e.target.name]: e.target.value });
  const { data: results, isLoading: isResultsLoading } = useGetExamMarksQuery(
    "level_id=10&academic_year_id=2"
  );

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  return (
    <section className="py-6 w-full flex flex-col items-start justify-between">
      <div className="w-full flex items-start justify-between">
        <div className="flex items-center gap-2">
          <div className="hover:bg-white p-2 rounded-full">
            <ArrowBackIcon
              onClick={() => navigate("/admin/results/final-results")}
            />
          </div>
          <p className="text-black font-semibold text-lg">Mary Jones</p>
        </div>
        <Button
          variant="contained"
          color="success"
          style={{ textTransform: "capitalize" }}
        >
          Download
        </Button>
      </div>
      <h2 className="font-semibold text-black text-lg mb-4">Final Marks </h2>
      <div className="w-full flex items-start justify-between mb-4">
        <div className="flex items-start gap-2">
          <Box sx={{ minWidth: 180 }}>
            <FormControl fullWidth>
              {/* <InputLabel id="filter">Filter</InputLabel> */}
              <TextField
                fullWidth
                select
                id="courseCode"
                size="small"
                value={filter.courseCode}
                label="course Code"
                name="courseCode"
                onChange={handleChange}
              >
                {firstSemesterResults.map((item) => (
                  <MenuItem key={item.id} value={item.courseCode}>
                    {item.courseCode}
                  </MenuItem>
                ))}
              </TextField>
            </FormControl>
          </Box>
          <Box sx={{ minWidth: 180 }}>
            <FormControl fullWidth>
              {/* <InputLabel id="filter">Filter</InputLabel> */}
              <TextField
                select
                id="courseTitle"
                size="small"
                value={filter.courseTitle}
                label="course Title"
                name="courseTitle"
                onChange={handleChange}
              >
                {isResultsLoading ? (
                  <Spinner />
                ) : results?.data?.data?.length < 1 ? (
                  <>No data</>
                ) : (
                  results?.data?.data?.map((item) => (
                    <MenuItem key={item.id} value={item.courseTitle}>
                      {item.courseTitle}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </FormControl>
          </Box>
        </div>
        <div>
          <Stack direction={"row"} gap={2}>
            <Button
              variant="outlined"
              color="success"
              style={{ textTransform: "capitalize" }}
            >
              Export
            </Button>
            <Button
              variant="contained"
              color="success"
              style={{ textTransform: "capitalize" }}
              onClick={() => navigate("/admin/results/final-results/upload")}
            >
              Import csv
            </Button>
          </Stack>
        </div>
      </div>
      <div className="w-full flex flex-col items-start justify-center ">
        <ThemeProvider theme={theme}>
          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Course Title</StyledTableCell>
                    <StyledTableCell>Course Code</StyledTableCell>
                    <StyledTableCell>Credit Value</StyledTableCell>
                    <StyledTableCell>CA Mark</StyledTableCell>
                    <StyledTableCell>Exam Mark</StyledTableCell>
                    <StyledTableCell>Total</StyledTableCell>
                    <StyledTableCell>Grade</StyledTableCell>
                    <StyledTableCell>Grade Point</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {firstSemesterResults.map((item) => (
                    <StyledTableRow key={item.id}>
                      <StyledTableCell>{item.id}</StyledTableCell>
                      <StyledTableCell>{item.courseTitle}</StyledTableCell>
                      <StyledTableCell>{item.courseCode}</StyledTableCell>
                      <StyledTableCell>{item.creditValue}</StyledTableCell>
                      <StyledTableCell>{item.ca}</StyledTableCell>
                      <StyledTableCell>{item.exam}</StyledTableCell>
                      <StyledTableCell>{item.total}</StyledTableCell>
                      <StyledTableCell>{item.grade}</StyledTableCell>
                      <StyledTableCell>{item.gadePoint}</StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-3">
                          <EditOutlinedIcon
                            color="success"
                            sx={{ cursor: "pointer" }}
                            // onClick={() => setOpenEditModal(true)}
                          />
                          <DeleteOutlineOutlinedIcon
                            color="error"
                            sx={{ cursor: "pointer" }}
                            onClick={() => setOpenDeleteModal(true)}
                          />
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </ThemeProvider>
      </div>
      {openDeleteModal && (
        <DeleteDialog
          openModal={openDeleteModal}
          closeModal={() => setOpenDeleteModal(false)}
        />
      )}
    </section>
  );
}

export default ViewFinalResults;
