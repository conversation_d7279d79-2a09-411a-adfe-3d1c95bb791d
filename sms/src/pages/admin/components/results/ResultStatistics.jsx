import { useGetStatisticsQuery } from "@app";
import { Spinner } from "@components";
import StatsDashboard from "@pages/admin/components/results/StatsDashboard";
import { useEffect } from "react";

function ResultStatistics() {
  const {
    data: statistics,
    isLoading: isStatisticsLoading,

    status,
  } = useGetStatisticsQuery("get statistics");

  useEffect(() => {
    console.log(statistics);
  }, [status]);
  return (
    <div className="w-full flex flex-col items-start justify-between my-2 gap-2">
      <h1 className="text-gray-600">Showing Statistics</h1>
      {isStatisticsLoading ? (
        <Spinner />
      ) : (
        <StatsDashboard data={statistics?.data} />
      )}
    </div>
  );
}

export default ResultStatistics;
