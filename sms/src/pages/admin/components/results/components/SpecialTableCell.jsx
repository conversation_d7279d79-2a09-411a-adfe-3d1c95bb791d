import { Spinner, StyledTableCell } from "@components";
import { useDownload } from "@hooks/useDownload";
import { RequestInterceptor } from "@lib/util";
import { Download } from "@mui/icons-material";
import { useState } from "react";
import { useSelector } from "react-redux";

export default function SpecialTableCell({ student }) {
  const { filter } = useSelector((state) => state.filter);
  const [isDownloadingResult, setIsDownloadingResult] = useState(false);
  const download = useDownload();
  async function downloadSingleResult() {
    try {
      setIsDownloadingResult(true);
      await RequestInterceptor.downloadFile(
        download,
        `results/generate-pdf?student_id=${student.id}&level_id=${filter.level}&academic_year_id=${filter.academic_year}&semester=${filter.semester}`,
        `Final-Results-${student.matricule}-Level${filter.level}-${filter.academic_year}-Semester${filter.semester}`
      );
    } catch (error) {
      console.log(error);
    } finally {
      setIsDownloadingResult(false);
    }
  }
  return (
    <StyledTableCell>
      {isDownloadingResult ? (
        <Spinner />
      ) : (
        <Download
          className="text-green text-md cursor-pointer"
          onClick={downloadSingleResult}
        />
      )}
    </StyledTableCell>
  );
}
