import {
  useGetAdminStudentExamMarksQuery,
  useGetAllAcademicYearsQuery,
  useGetAllLevelsQuery,
  useGetAllStudentsQuery,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { useDownload } from "@hooks/useDownload";
import { useGetSystemSettings } from "@hooks/useGetSystemSettings";
import { RequestInterceptor } from "@lib/util";
import { Download } from "@mui/icons-material";
import {
  Button,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { useEffect, useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function StudentResultViewer() {
  const [form, setForm] = useState({
    academic_year: "",
    semester: "",
    level: "",
    student: null,
  });
  const [submitted, setSubmitted] = useState(false);
  const [isResultDownloading, setIsResultDownloading] = useState(false);

  const download = useDownload();
  const system = useGetSystemSettings();

  // Fetch levels
  const { data: levelsData, isLoading: isLevelsLoading } =
    useGetAllLevelsQuery("get levels");
  const levels = levelsData?.data || [];

  // Fetch students (optionally filter by level)
  const { data: studentsData, isLoading: isStudentsLoading } =
    useGetAllStudentsQuery(1);
  const students = studentsData?.data || [];
  console.log(students);

  // Fetch academic years
  const { data: academicYearsData, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("academicYears");
  const academicYears = academicYearsData?.data || [];

  // Fetch results only after submit
  const getStudentId = (student) =>
    student?.student_id || student?.id || student?.matricule;
  const shouldFetchResults =
    submitted &&
    form.student &&
    form.academic_year &&
    form.semester &&
    form.level;
  const queryString = shouldFetchResults
    ? `level_id=${form.level}&academic_year_id=${form.academic_year}&semester=${form.semester}&student_id=${getStudentId(form.student)}`
    : null;
  const {
    data: examMarks,
    isLoading: isExamMarksLoading,
    error: examError,
    isError: isExamError,
    isFetching: isExamMarksFetching,
  } = useGetAdminStudentExamMarksQuery(queryString, {
    skip: !shouldFetchResults,
  });
  const results = examMarks?.data?.results || [];

  useEffect(() => {
    // Only set defaults if not already set and data is loaded
    if (
      system?.current_academic_year_id &&
      system?.current_semester &&
      levels.length > 0 &&
      (!form.academic_year || !form.semester || !form.level)
    ) {
      setForm((prev) => ({
        ...prev,
        academic_year: prev.academic_year || system.current_academic_year_id,
        semester: prev.semester || system.current_semester,
        level: prev.level || levels[0].id,
      }));
    }
  }, [system, levels]);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    setSubmitted(false); // Reset submitted so results only load after button click
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setSubmitted(true);
  };

  const handleDownload = async () => {
    setIsResultDownloading(true);
    try {
      await RequestInterceptor.downloadFile(
        download,
        `results/generate-pdf?level_id=${form.level}&academic_year_id=${form.academic_year}&student_id=${getStudentId(form.student)}&semester=${form.semester}`,
        `${form.student?.matricule || "student"}-Semester_${form.semester}-results.pdf`,
        "application/pdf"
      );
    } catch (e) {
      // Error handled in RequestInterceptor
    } finally {
      setIsResultDownloading(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <section className="w-full flex flex-col items-center justify-between gap-8">
        {/* Selection Form */}
        <div className="w-full bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="font-semibold text-black text-xl mb-6">
            Student Result Viewer
          </h2>
          <form className="w-full flex flex-col gap-6" onSubmit={handleSubmit}>
            {/* Student Selection Section */}
            <div className="w-full bg-gray-50 rounded-lg p-4">
              <h3 className="text-gray-700 font-medium mb-4">
                Student Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <TextField
                  select
                  id="level"
                  size="small"
                  value={form.level}
                  label="Level"
                  name="level"
                  onChange={handleChange}
                  sx={{
                    minWidth: 140,
                    background: "#f3f4f6",
                    borderRadius: 2,
                    boxShadow: "0 1px 4px rgba(0,0,0,0.04)",
                    "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                  }}
                  InputLabelProps={{ shrink: true }}
                  SelectProps={{
                    displayEmpty: true,
                    MenuProps: {
                      PaperProps: {
                        style: { maxHeight: 250 },
                      },
                    },
                  }}
                >
                  {isLevelsLoading ? (
                    <MenuItem value="">
                      <Spinner size="14px" />
                    </MenuItem>
                  ) : (
                    levels.map((item) => (
                      <MenuItem
                        key={item.id}
                        value={item.id}
                        style={{ fontWeight: 500 }}
                      >
                        {item.name}
                      </MenuItem>
                    ))
                  )}
                </TextField>
                <Autocomplete
                  id="student"
                  options={students}
                  getOptionLabel={(option) => {
                    if (!option) return "";
                    const name =
                      option.name ||
                      `${option.userInfo?.first_name || ""} ${option.userInfo?.last_name || ""}`.trim();
                    return name && option.matricule
                      ? `${name} (${option.matricule})`
                      : name || option.matricule || "";
                  }}
                  filterOptions={(options, { inputValue }) =>
                    options.filter(
                      (option) =>
                        option.userInfo?.first_name
                          ?.toLowerCase()
                          .includes(inputValue.toLowerCase()) ||
                        option.userInfo?.last_name
                          ?.toLowerCase()
                          .includes(inputValue.toLowerCase()) ||
                        option.matricule
                          ?.toLowerCase()
                          .includes(inputValue.toLowerCase())
                    )
                  }
                  loading={isStudentsLoading}
                  value={form.student}
                  onChange={(_, value) => {
                    setForm((prev) => ({ ...prev, student: value }));
                    setSubmitted(false);
                  }}
                  disabled={!form.level}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Student"
                      required
                      size="small"
                      sx={{
                        background: "#f3f4f6",
                        borderRadius: 2,
                        boxShadow: "0 1px 4px rgba(0,0,0,0.04)",
                        "& .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                      }}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isStudentsLoading ? <Spinner size="14px" /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </div>
            </div>
            {/* Filters Section */}
            <div className="w-full bg-gray-50 rounded-lg p-4">
              <h3 className="text-gray-700 font-medium mb-4">Filter Results</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <TextField
                  select
                  id="academic_year"
                  size="small"
                  value={form.academic_year}
                  label="Year"
                  name="academic_year"
                  onChange={handleChange}
                  sx={{
                    minWidth: 140,
                    background: "#f3f4f6",
                    borderRadius: 2,
                    boxShadow: "0 1px 4px rgba(0,0,0,0.04)",
                    "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                  }}
                  InputLabelProps={{ shrink: true }}
                  SelectProps={{
                    displayEmpty: true,
                    MenuProps: {
                      PaperProps: {
                        style: { maxHeight: 250 },
                      },
                    },
                  }}
                >
                  {isAcademicYearsLoading ? (
                    <MenuItem value="">
                      <Spinner size="14px" />
                    </MenuItem>
                  ) : (
                    academicYears.map((item) => (
                      <MenuItem
                        key={item.id}
                        value={item.id}
                        style={{ fontWeight: 500 }}
                      >
                        {item.name?.replace("_", "/")}
                      </MenuItem>
                    ))
                  )}
                </TextField>
                <TextField
                  select
                  id="semester"
                  size="small"
                  value={form.semester}
                  label="Semester"
                  name="semester"
                  onChange={handleChange}
                  sx={{
                    minWidth: 140,
                    background: "#f3f4f6",
                    borderRadius: 2,
                    boxShadow: "0 1px 4px rgba(0,0,0,0.04)",
                    "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                  }}
                  InputLabelProps={{ shrink: true }}
                  SelectProps={{
                    displayEmpty: true,
                    MenuProps: {
                      PaperProps: {
                        style: { maxHeight: 250 },
                      },
                    },
                  }}
                >
                  <MenuItem value="">Select Semester</MenuItem>
                  <MenuItem value={1}>First</MenuItem>
                  <MenuItem value={2}>Second</MenuItem>
                </TextField>
              </div>
            </div>
            <div className="flex items-center justify-end gap-4 mt-2">
              <Button
                type="submit"
                variant="contained"
                color="success"
                sx={{
                  textTransform: "capitalize",
                  borderRadius: 2,
                  fontWeight: 600,
                  px: 4,
                  py: 1.5,
                  fontSize: 16,
                  boxShadow: "0 2px 8px rgba(34,197,94,0.08)",
                }}
                disabled={isExamMarksLoading || isExamMarksFetching}
              >
                {isExamMarksLoading || isExamMarksFetching ? (
                  <Spinner size="18px" />
                ) : (
                  "View Results"
                )}
              </Button>
            </div>
          </form>
        </div>
        {/* Results Card */}
        <div className="w-full flex flex-col items-center justify-between gap-6">
          <div className="w-full flex flex-col items-center justify-center gap-3">
            <p className="text-green font-bold text-xl mb-2">
              Semester Results
            </p>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer
                  component={Paper}
                  elevation={3}
                  className="rounded-2xl shadow-xl"
                >
                  <Table
                    sx={{ maxWidth: "100%", fontFamily: "Inter, sans-serif" }}
                  >
                    <TableHead>
                      <TableRow sx={{ background: "#f9fafb" }}>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {isResultDownloading ||
                      isExamMarksFetching ||
                      isExamMarksLoading ? (
                        <Spinner />
                      ) : isExamError ? (
                        <tr>
                          <td
                            colSpan={7}
                            className="text-center py-6 text-gray-400 text-lg"
                          >
                            {examError?.data?.message ||
                              "Error fetching results"}
                          </td>
                        </tr>
                      ) : results.length < 1 ? (
                        <tr>
                          <td
                            colSpan={7}
                            className="text-center py-6 text-gray-400 text-lg"
                          >
                            No results for this semester
                          </td>
                        </tr>
                      ) : (
                        results.map((result, idx) => (
                          <StyledTableRow
                            key={
                              result.id ||
                              `${result.course?.code || "course"}-${idx}`
                            }
                            className={
                              idx % 2 === 0
                                ? "bg-white transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                                : "bg-gray-50 transition-all duration-200 hover:bg-green/5 scale-100 hover:scale-[1.01]"
                            }
                          >
                            <StyledTableCell>{idx + 1}</StyledTableCell>
                            <StyledTableCell>
                              {result.course?.name ?? "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              {result.course?.code ?? "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              {result.course?.credit_value ?? "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              {result.ca_mark ?? "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              {result.exam_mark ?? "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              {result.grade ?? "N/A"}
                            </StyledTableCell>
                          </StyledTableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            </ThemeProvider>
          </div>
          {/* Summary Bar */}
          <div className="w-full flex flex-wrap items-center justify-center gap-6 bg-white rounded-xl shadow p-4 mt-2">
            <div className="flex flex-col items-center">
              <span className="text-gray-500 text-sm">Attempted Credit</span>
              <span className="font-bold text-green text-lg">
                {examMarks?.data?.total_credits ?? "-"}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-gray-500 text-sm">Credit Gotten</span>
              <span className="font-bold text-green text-lg">
                {results && results.length > 0
                  ? results
                      .filter((r) =>
                        ["C", "C+", "B", "B+", "A"].includes(
                          (r.grade || "").toUpperCase()
                        )
                      )
                      .reduce(
                        (sum, r) => sum + (Number(r.course?.credit_value) || 0),
                        0
                      )
                  : "-"}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-gray-500 text-sm">GPA</span>
              <span className="font-bold text-green text-lg">
                {examMarks?.data?.gpa ?? "-"}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <Button
                variant="contained"
                color="success"
                sx={{
                  textTransform: "capitalize",
                  borderRadius: 2,
                  fontWeight: 600,
                  px: 4,
                  py: 1.5,
                  fontSize: 16,
                  boxShadow: "0 2px 8px rgba(34,197,94,0.08)",
                  minWidth: 140,
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
                onClick={handleDownload}
                disabled={isResultDownloading || !shouldFetchResults}
                startIcon={<Download />}
              >
                {isResultDownloading ? <Spinner size="18px" /> : "Download"}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </ThemeProvider>
  );
}

export default StudentResultViewer;
