import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { MenuItem, Stack, TextField } from "@mui/material";
import {
  Departments,
  Schools,
  Semester,
  courseCode,
  level,
} from "@pages/admin/components/results/data";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function ResultSelection({ openModal, closeModal }) {
  const [form, setForm] = useState({
    school: "",
    department: "",
    level: "",
    semester: "",
    course: "",
  });
  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Change Selection</h2>
        <form className="w-full ">
          <Stack direction={"row"} gap={2}>
            <TextField
              fullWidth
              select
              id="school"
              size="small"
              value={form.department}
              label="select school"
              name="school"
              onChange={handleChange}
              sx={{ marginBottom: 2 }}
            >
              {Schools.map((item) => (
                <MenuItem key={item.id}>{item.school}</MenuItem>
              ))}
            </TextField>
            <TextField
              fullWidth
              select
              id="school"
              size="small"
              value={form.department}
              label="select department"
              name="department"
              onChange={handleChange}
              sx={{ marginBottom: 2 }}
            >
              {Departments.map((item) => (
                <MenuItem key={item.id}>{item.name}</MenuItem>
              ))}
            </TextField>
          </Stack>
          <Stack direction={"row"} gap={2}>
            <TextField
              fullWidth
              select
              id="semester"
              size="small"
              value={form.semester}
              label="select semester"
              name="semester"
              onChange={handleChange}
              sx={{ marginBottom: 2 }}
            >
              {Semester.map((item) => (
                <MenuItem key={item.id}>{item.semester}</MenuItem>
              ))}
            </TextField>
            <TextField
              fullWidth
              select
              id="school"
              size="small"
              value={form.level}
              label="select level"
              name="level"
              onChange={handleChange}
              sx={{ marginBottom: 2 }}
            >
              {level.map((item) => (
                <MenuItem key={item.id}>{item.level}</MenuItem>
              ))}
            </TextField>
          </Stack>
          <Stack direction={"row"} gap={2}>
            <TextField
              fullWidth
              select
              id="course"
              size="small"
              value={form.semester}
              label="select course"
              name="course"
              onChange={handleChange}
              sx={{ marginBottom: 2 }}
            >
              {courseCode.map((item) => (
                <MenuItem key={item.id}>{item.name}</MenuItem>
              ))}
            </TextField>
          </Stack>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"Continue"} type={"primary"} onClick={closeModal} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
ResultSelection.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal as a required boolean
  closeModal: PropTypes.func.isRequired, // Validate closeModal as a required function
};

export default ResultSelection;
