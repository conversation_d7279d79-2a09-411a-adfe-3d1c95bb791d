import Logo from "@assets/images/logo.jpg";
import { StyledTableCell, StyledTableRow } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Box,
  Button,
  Grid,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import {
  firstSemesterResults,
  gradeSystem,
} from "@pages/admin/components/results/data";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function ViewTranscript() {
  const navigate = useNavigate();

  const [count] = useState(10);
  const [page, setPage] = useState(1);

  // Handle multiple pages
  const handleTableChange = async (event, value) => {
    console.log("Page:", value);
    setPage(value);
    // setLoading(true);
    // const { data, from } = await gotoPage(value);
    // setIndex(from - 1);
    // setData(data);
    // setLoading(false);
  };

  // const [openEditModal, setOpenEditModal] = useState(false);
  // const [openDeleteModal, setOpenDeleteModal] = useState(false);

  return (
    <div className="w-full flex flex-col items-center justify-center py-6 gap-4">
      <div className="w-full flex items-start justify-between">
        <div className="flex items-center gap-2">
          <div className="hover:bg-white p-2 rounded-full">
            <ArrowBackIcon
              onClick={() => navigate("/admin/results/transcripts")}
            />
          </div>
          <p className="text-black font-semibold text-lg">Mary Jones</p>
        </div>
        <Button
          variant="contained"
          color="success"
          style={{ textTransform: "capitalize" }}
        >
          Download
        </Button>
      </div>
      <div className="w-full bg-white p-5 flex flex-col gap-6">
        <div className="w-full flex items-start justify-between">
          <p className="font-semibold text-black text-sm w-1/5">
            EBENZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
          </p>
          <img src={Logo} alt="" className="w-[50px]" />
          <div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                BAMENDA - SONAC STREET
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                <EMAIL>
              </p>
            </div>
            <div className="flex gap-2">
              <p className="font-semibold text-black text-sm">
                {" "}
                +237 678332504
              </p>
            </div>
          </div>
        </div>
        <hr className="w-full flex items-center" />
        <div className="w-full flex items-start justify-between">
          <div className="w-full flex flex-col gap-2">
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">School:</p>
              <h2 className="text-black font-semibold text-md">
                SCHOOL OF HOME ECONOMICS TOURISM AND HOSPITALITY MANAGEMENT
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Department:</p>
              <h2 className="text-black font-semibold text-md">
                HOTEL MANAGEMENT AND CATERING
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Deg in view:</p>
              <h2 className="text-black font-semibold text-md">
                DEGREE IN HOTEL MANAGEMENT AND CATERING
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Reference No:</p>
              <h2 className="text-black font-semibold text-md">
                .....................
              </h2>
            </div>
          </div>

          <div className="w-full flex flex-col items-start justify-between gap-3 ">
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Name:</p>
              <h2 className="text-black font-semibold text-md">
                ABONGWI MARIETTA
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Matricule:</p>
              <h2 className="text-black font-semibold text-md">EHIST3030</h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">
                Date/place of Birth:
              </p>
              <h2 className="text-black font-semibold text-md">
                07/07/1992 , Bamenda
              </h2>
            </div>
            <div className="w-full flex items-start gap-3">
              <p className="text-black font-normal text-sm">Sex:</p>
              <h2 className="text-black font-semibold text-md">Female</h2>
            </div>
          </div>
        </div>
        <div className="w-full flex flex-col items-center justify-center">
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
          >
            Academic Transcript
          </Button>
        </div>

        {/* All Results from the academic years 2021-2024 */}

        <div className="w-full flex flex-col items-start justify-center gap-4">
          <h2 className="font-medium text-black text-md ml-2">
            Academic year:{" "}
            <span className="text-green text-sm font-semibold">2021/2022</span>
          </h2>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey">
              <p className="text-black font-semibold text-lg">First Semester</p>
            </div>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer component={Paper}>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course Title</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Total</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                        <StyledTableCell>Grade Point</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {firstSemesterResults.map((item) => (
                        <StyledTableRow key={item.id}>
                          <StyledTableCell>{item.id}</StyledTableCell>
                          <StyledTableCell>{item.courseTitle}</StyledTableCell>
                          <StyledTableCell>{item.courseCode}</StyledTableCell>
                          <StyledTableCell>{item.creditValue}</StyledTableCell>
                          <StyledTableCell>{item.ca}</StyledTableCell>
                          <StyledTableCell>{item.exam}</StyledTableCell>
                          <StyledTableCell>{item.total}</StyledTableCell>
                          <StyledTableCell>{item.grade}</StyledTableCell>
                          <StyledTableCell>{item.gadePoint}</StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
              count={2}
              rowsPerPage={4}
              rowsPerPageOptions={[5, 10, 25, 50, { value: -1, label: "All" }]}
            /> */}
                  <Grid
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"flex-start"}
                    my={3}
                  >
                    <Box>
                      <Stack spacing={2}>
                        <Pagination
                          count={count}
                          page={page}
                          onChange={handleTableChange}
                          showFirstButton
                          showLastButton
                          color="green"
                        />
                      </Stack>
                    </Box>
                  </Grid>
                </TableContainer>
              </div>
            </ThemeProvider>

            <div className="w-full flex items-center justify-between">
              <div className="w-full flex items-start gap-2">
                <p className="text-md font-normal text-black ml-2">
                  No of courses: <span className="font-semibold">08</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total Credit Value: <span className="font-semibold">21</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total GPA: <span className="font-semibold">4.11</span>
                </p>
              </div>
              <div className="w-1/4 p-2 bg-yellow">
                <p>
                  Second Semester GPA:{" "}
                  <span className="text-md font-semibold text-black">2.45</span>
                </p>
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey">
              <p className="text-black font-semibold text-lg">
                Second Semester
              </p>
            </div>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer component={Paper}>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course Title</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Total</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                        <StyledTableCell>Grade Point</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {firstSemesterResults.map((item) => (
                        <StyledTableRow key={item.id}>
                          <StyledTableCell>{item.id}</StyledTableCell>
                          <StyledTableCell>{item.courseTitle}</StyledTableCell>
                          <StyledTableCell>{item.courseCode}</StyledTableCell>
                          <StyledTableCell>{item.creditValue}</StyledTableCell>
                          <StyledTableCell>{item.ca}</StyledTableCell>
                          <StyledTableCell>{item.exam}</StyledTableCell>
                          <StyledTableCell>{item.total}</StyledTableCell>
                          <StyledTableCell>{item.grade}</StyledTableCell>
                          <StyledTableCell>{item.gadePoint}</StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
              count={2}
              rowsPerPage={4}
              rowsPerPageOptions={[5, 10, 25, 50, { value: -1, label: "All" }]}
            /> */}
                  <Grid
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"flex-start"}
                    my={3}
                  >
                    <Box>
                      <Stack spacing={2}>
                        <Pagination
                          count={count}
                          page={page}
                          onChange={handleTableChange}
                          showFirstButton
                          showLastButton
                          color="green"
                        />
                      </Stack>
                    </Box>
                  </Grid>
                </TableContainer>
              </div>
            </ThemeProvider>

            <div className="w-full flex items-center justify-between">
              <div className="w-full flex items-start gap-2">
                <p className="text-md font-normal text-black ml-2">
                  No of courses: <span className="font-semibold">08</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total Credit Value: <span className="font-semibold">21</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total GPA: <span className="font-semibold">4.11</span>
                </p>
              </div>
              <div className="w-1/5 p-2 bg-orange">
                <p className="text-white font-normal">
                  First Semester GPA:{" "}
                  <span className="text-md font-semibold text-white">2.45</span>
                </p>
              </div>
            </div>
            <div className="w-full flex items-start bg-light_orange border-l-4 border-l-orange p-1">
              <p className="p-2 ">
                Academic Year (2021/2022)GPA{" "}
                <span className="p-2 bg-orange text-white font-semibold">
                  2.25
                </span>
              </p>
            </div>
          </div>
        </div>

        <div className="w-full flex flex-col items-start justify-center gap-4">
          <h2 className="font-medium text-black text-md ml-2">
            Academic year:{" "}
            <span className="text-green text-sm font-semibold">2022/2023</span>
          </h2>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey">
              <p className="text-black font-semibold text-lg">First Semester</p>
            </div>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer component={Paper}>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course Title</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Total</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                        <StyledTableCell>Grade Point</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {firstSemesterResults.map((item) => (
                        <StyledTableRow key={item.id}>
                          <StyledTableCell>{item.id}</StyledTableCell>
                          <StyledTableCell>{item.courseTitle}</StyledTableCell>
                          <StyledTableCell>{item.courseCode}</StyledTableCell>
                          <StyledTableCell>{item.creditValue}</StyledTableCell>
                          <StyledTableCell>{item.ca}</StyledTableCell>
                          <StyledTableCell>{item.exam}</StyledTableCell>
                          <StyledTableCell>{item.total}</StyledTableCell>
                          <StyledTableCell>{item.grade}</StyledTableCell>
                          <StyledTableCell>{item.gadePoint}</StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
              count={2}
              rowsPerPage={4}
              rowsPerPageOptions={[5, 10, 25, 50, { value: -1, label: "All" }]}
            /> */}
                  <Grid
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"flex-start"}
                    my={3}
                  >
                    <Box>
                      <Stack spacing={2}>
                        <Pagination
                          count={count}
                          page={page}
                          onChange={handleTableChange}
                          showFirstButton
                          showLastButton
                          color="green"
                        />
                      </Stack>
                    </Box>
                  </Grid>
                </TableContainer>
              </div>
            </ThemeProvider>

            <div className="w-full flex items-center justify-between">
              <div className="w-full flex items-start gap-2">
                <p className="text-md font-normal text-black ml-2">
                  No of courses: <span className="font-semibold">08</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total Credit Value: <span className="font-semibold">21</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total GPA: <span className="font-semibold">4.11</span>
                </p>
              </div>
              <div className="w-1/5 p-2 bg-orange">
                <p className="text-white font-normal">
                  First Semester GPA:{" "}
                  <span className="text-md font-semibold text-white">2.85</span>
                </p>
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey">
              <p className="text-black font-semibold text-lg">
                Second Semester
              </p>
            </div>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer component={Paper}>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course Title</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Total</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                        <StyledTableCell>Grade Point</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {firstSemesterResults.map((item) => (
                        <StyledTableRow key={item.id}>
                          <StyledTableCell>{item.id}</StyledTableCell>
                          <StyledTableCell>{item.courseTitle}</StyledTableCell>
                          <StyledTableCell>{item.courseCode}</StyledTableCell>
                          <StyledTableCell>{item.creditValue}</StyledTableCell>
                          <StyledTableCell>{item.ca}</StyledTableCell>
                          <StyledTableCell>{item.exam}</StyledTableCell>
                          <StyledTableCell>{item.total}</StyledTableCell>
                          <StyledTableCell>{item.grade}</StyledTableCell>
                          <StyledTableCell>{item.gadePoint}</StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
              count={2}
              rowsPerPage={4}
              rowsPerPageOptions={[5, 10, 25, 50, { value: -1, label: "All" }]}
            /> */}
                  <Grid
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"flex-start"}
                    my={3}
                  >
                    <Box>
                      <Stack spacing={2}>
                        <Pagination
                          count={count}
                          page={page}
                          onChange={handleTableChange}
                          showFirstButton
                          showLastButton
                          color="green"
                        />
                      </Stack>
                    </Box>
                  </Grid>
                </TableContainer>
              </div>
            </ThemeProvider>

            <div className="w-full flex items-center justify-between">
              <div className="w-full flex items-start gap-2">
                <p className="text-md font-normal text-black ml-2">
                  No of courses: <span className="font-semibold">08</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total Credit Value: <span className="font-semibold">21</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total GPA: <span className="font-semibold">4.11</span>
                </p>
              </div>
              <div className="w-1/4 p-2 bg-blue">
                <p className="text-white font-normal">
                  Second Semester GPA:{" "}
                  <span className="text-md font-semibold text-white">3.45</span>
                </p>
              </div>
            </div>
            <div className="w-full flex items-start bg-light_blue border-l-4 border-l-blue p-1">
              <p className="p-2">
                Academic Year (2021/2022)GPA{" "}
                <span className="p-2 bg-blue text-white font-semibold">
                  3.25
                </span>
              </p>
            </div>
          </div>
        </div>

        <div className="w-full flex flex-col items-start justify-center gap-4">
          <h2 className="font-medium text-black text-md ml-2">
            Academic year:{" "}
            <span className="text-green text-sm font-semibold">2023/2024</span>
          </h2>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey">
              <p className="text-black font-semibold text-lg">First Semester</p>
            </div>
            <ThemeProvider theme={theme}>
              <div className="flex flex-col w-full my-2">
                <TableContainer component={Paper}>
                  <Table sx={{ maxWidth: "100%" }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell>S/N</StyledTableCell>
                        <StyledTableCell>Course Title</StyledTableCell>
                        <StyledTableCell>Course Code</StyledTableCell>
                        <StyledTableCell>Credit Value</StyledTableCell>
                        <StyledTableCell>CA Mark</StyledTableCell>
                        <StyledTableCell>Exam Mark</StyledTableCell>
                        <StyledTableCell>Total</StyledTableCell>
                        <StyledTableCell>Grade</StyledTableCell>
                        <StyledTableCell>Grade Point</StyledTableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {firstSemesterResults.map((item) => (
                        <StyledTableRow key={item.id}>
                          <StyledTableCell>{item.id}</StyledTableCell>
                          <StyledTableCell>{item.courseTitle}</StyledTableCell>
                          <StyledTableCell>{item.courseCode}</StyledTableCell>
                          <StyledTableCell>{item.creditValue}</StyledTableCell>
                          <StyledTableCell>{item.ca}</StyledTableCell>
                          <StyledTableCell>{item.exam}</StyledTableCell>
                          <StyledTableCell>{item.total}</StyledTableCell>
                          <StyledTableCell>{item.grade}</StyledTableCell>
                          <StyledTableCell>{item.gadePoint}</StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
              count={2}
              rowsPerPage={4}
              rowsPerPageOptions={[5, 10, 25, 50, { value: -1, label: "All" }]}
            /> */}
                  <Grid
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"flex-start"}
                    my={3}
                  >
                    <Box>
                      <Stack spacing={2}>
                        <Pagination
                          count={count}
                          page={page}
                          onChange={handleTableChange}
                          showFirstButton
                          showLastButton
                          color="green"
                        />
                      </Stack>
                    </Box>
                  </Grid>
                </TableContainer>
              </div>
            </ThemeProvider>

            <div className="w-full flex items-center justify-between">
              <div className="w-full flex items-start gap-2">
                <p className="text-md font-normal text-black ml-2">
                  No of courses: <span className="font-semibold">08</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total Credit Value: <span className="font-semibold">21</span>
                </p>
                <p className="text-md font-normal text-black ml-2">
                  Total GPA: <span className="font-semibold">4.11</span>
                </p>
              </div>
              <div className="w-1/5 p-2 bg-green">
                <p className="text-white font-normal">
                  First Semester GPA:{" "}
                  <span className="text-md font-semibold text-white">3.75</span>
                </p>
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col items-center justify-center gap-6 mb-10">
            <div className="w-full p-2 bg-grey ">
              <p className="text-black font-semibold text-lg">
                Second Semester
              </p>
            </div>
            <div className="w-full min-h-[70vh] flex flex-col items-center justify-center border-1 border-grey_300 p-4">
              <p className="text-md text-red/80 font-semibold">
                This results of this semester are currently unavailable
              </p>
              <p className="text-sm font-normal text-grey_300">
                If you registered and can&apos;t find courses, go to the school
                administration for verification.
              </p>
            </div>

            <div className="w-full flex items-start bg-green/10 border-l-4 border-l-green p-1">
              <p className="p-2 ">
                Academic Year (2021/2022)GPA{" "}
                <span className="p-2 bg-green text-white font-semibold">
                  3.25
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Final grading section */}

        <div className="w-full flex flex-col items-start mb-15">
          <div className="flex flex-col bg-white shadow-sm border-2 p-5 gap-3 border-grey rounded-md">
            <div className="flex items-center justify-between gap-4">
              <p>Total Attempted Credits: </p>
              <div className="p-2 bg-grey">
                <p>160</p>
              </div>
            </div>
            <div className="flex items-center justify-between gap-4">
              <p>Total Credits Earned: </p>
              <div className="p-2 bg-grey">
                <p>160</p>
              </div>
            </div>
            <div className="flex items-center justify-between gap-4">
              <p>Commulative GPA: </p>
              <div className="p-2 bg-orange">
                <p>2.58</p>
              </div>
            </div>
          </div>
        </div>
        {/* Grading System and Degree Classification */}

        <div className="w-full flex flex-col items-start justify-between gap-4">
          <Button
            variant="contained"
            color="secondary"
            style={{ textTransform: "capitalize" }}
          >
            Key
          </Button>

          <div className="w-full flex items-start justify-between gap-4 mb-6 ">
            {/* Grade System */}
            <div className="w-full flex-col items-start justify-center">
              <div className="w-full flex flex-col bg-white shadow-sm border-2 p-5 gap-3 border-grey rounded-md">
                <div className="w-full items-start mb-3">
                  <p className="text-black text-md font-semibold">
                    Grade System
                  </p>
                  <div className="w-[35px] h-[2px] bg-red"></div>
                </div>
                <div className="flex flex-col w-full my-2">
                  <TableContainer component={Paper}>
                    <Table sx={{ maxWidth: "100%" }}>
                      <TableHead>
                        <TableRow>
                          <TableCell>Grade</TableCell>
                          <TableCell>Mark</TableCell>
                          <TableCell>Grade Point GP</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {gradeSystem.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell>{item.grade}</TableCell>
                            <TableCell>{item.mark}</TableCell>
                            <TableCell>{item.gradePoint}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              </div>
            </div>

            {/* Degree Classification */}
            <div className="w-full flex-col items-start justify-center">
              <div className="flex flex-col bg-white shadow-sm border-2 p-5 gap-3 border-grey rounded-md">
                <div className="w-full items-start mb-3">
                  <p className="text-black text-md font-semibold">
                    Degree Classification
                  </p>
                  <div className="w-[35px] h-[2px] bg-red"></div>
                </div>
                <div className="w-full flex items-start justify-between mb-3">
                  <p className="text-black font-normal">First Class:</p>
                  <p className="text-green font-semibold">3.60-4.00</p>
                </div>
                <div className="w-full flex items-start justify-between mb-3">
                  <p className="text-black font-normal">
                    Second Class Upper Division:
                  </p>
                  <p className="text-blue font-semibold">3.00-3.56</p>
                </div>
                <div className="w-full flex items-start justify-between mb-3">
                  <p className="text-black font-normal">
                    Second Class Lower Division:
                  </p>
                  <p className="text-orange font-semibold">2.50-2.99</p>
                </div>
                <div className="w-full flex items-start justify-between mb-3">
                  <p className="text-black font-normal">Third Class:</p>
                  <p className="text-yellow/80 font-semibold">2.25-2.49</p>
                </div>
                <div className="w-full flex items-start justify-between mb-3">
                  <p className="text-black font-normal">Pass:</p>
                  <p className="text-red font-semibold">2.00-2.44</p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full flex items-start justify-between mb-6">
            <div className="flex flex-col bg-white border-2 p-2 gap-3 border-grey rounded-md">
              <p className="text-black font-semibold text-md">Date Issued:</p>
            </div>
            <div className="flex flex-col bg-white border-2 p-2 gap-3 border-grey rounded-md">
              <p className="text-black font-semibold text-md">
                Registra&apos;s Office
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ViewTranscript;
