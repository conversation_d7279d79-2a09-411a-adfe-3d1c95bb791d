import { Box, Grid } from "@mui/material";
import { AdminProvider } from "@pages/admin/providers";
import PropTypes from "prop-types";

export function StudentWrapper({ children }) {
  return (
    <AdminProvider>
      <Box className=" h-full w-full py-5">
        <Grid>
          <Grid item>
            <div className="w-full items-start mb-3">
              <p className="text-black text-md font-semibold">Students</p>
              <div className="w-[35px] h-[2px] bg-red"></div>
            </div>
          </Grid>
        </Grid>
      </Box>
      {children}
    </AdminProvider>
  );
}

// Add prop types validation
StudentWrapper.propTypes = {
  children: PropTypes.node.isRequired, // Validate children prop
};
