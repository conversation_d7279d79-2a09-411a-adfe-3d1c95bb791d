import { Search } from "@mui/icons-material";
import { useState } from "react";

const EnhancedDropdown = ({
  options,
  value,
  onChange,
  label,
  loading,
  name,
  placeholder = "Select an option...",
  getOptionLabel = (option) => option.name || option.label,
  getOptionValue = (option) => option.id || option.value,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const filteredOptions =
    options?.filter((option) =>
      getOptionLabel(option).toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  const selectedOption = options?.find(
    (option) => getOptionValue(option) === value
  );

  const handleSelect = (option) => {
    onChange({
      target: {
        name: name,
        value: getOptionValue(option),
      },
    });
    setSearchTerm("");
    setIsSearching(false);
  };

  return (
    <div className="w-full relative flex gap-3 my-2 items-center ">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div className="relative">
        {isSearching ? (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              placeholder={`Search ${label.toLowerCase()}...`}
              autoFocus
            />
          </div>
        ) : (
          <div
            className="relative cursor-text"
            onClick={() => setIsSearching(true)}
          >
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <div className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md shadow-sm">
              {selectedOption ? getOptionLabel(selectedOption) : placeholder}
            </div>
          </div>
        )}

        {isSearching && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
            {loading ? (
              <div className="p-2 text-gray-500">Loading...</div>
            ) : filteredOptions.length === 0 ? (
              <div className="p-2 text-gray-500">No options found</div>
            ) : (
              filteredOptions.map((option) => (
                <div
                  key={getOptionValue(option)}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleSelect(option)}
                >
                  {getOptionLabel(option)}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedDropdown;
