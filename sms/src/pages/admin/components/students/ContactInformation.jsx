import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import {
  Button,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
} from "@mui/material";
import { RegSuccess } from "@pages/admin/components/students";
import { useAdminContext } from "@pages/admin/hooks";
import { useState } from "react";

export const ContactInformation = () => {
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [form, setForm] = useState({
    email: "",
    contact: "",
    address: "",
    parentsAddress: "",
    password: "",
    confirmPassword: "",
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePasswordVisibility = () => setShowPassword(!showPassword);
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const { navToAddStudentInformation } = useAdminContext();
  return (
    <div className="w-full flex flex-col items-center md:px-16 md:py-10 px-8 py-5  justify-between">
      <div className="w-full flex flex-col bg-white p-8">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={navToAddStudentInformation} />
            </div>
            <p className="text-black font-semibold text-lg">Add Student</p>
          </div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
          >
            Import Csv
          </Button>
        </div>
        <p className="text-green text-md font-semibold mb-4">
          Contact Information
        </p>

        <div className=" flex w-full items-center justify-center">
          <form className=" w-full ">
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                label="Email"
                value={form.email}
                name="email"
                id="email"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.contact}
                name="contact"
                id="contact"
                label="Phone Number"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                label="Address"
                value={form.address}
                name="address"
                id="address"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.parentsAddress}
                name="parentsAddress"
                id="parentsAddress"
                label="Parents Address"
                fullWidth
                required
              />
            </Stack>

            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type={showPassword ? "text" : "password"}
                onChange={onChange}
                value={form.password}
                name="password"
                id="password"
                label="Password"
                fullWidth
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment sx={{ width: "32px" }}>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                type={showPassword ? "text" : "password"}
                onChange={onChange}
                value={form.confirmPassword}
                name="confirmPassword"
                id="confirmPassword"
                label="Confirm Password"
                fullWidth
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment sx={{ width: "32px" }}>
                      <IconButton onClick={handleTogglePasswordVisibility}>
                        {showPassword ? (
                          <VisibilityOffIcon />
                        ) : (
                          <VisibilityIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Stack>
          </form>
        </div>
        <div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize", width: "20%" }}
            onClick={() => setOpenSuccessModal(true)}
          >
            Confirm
          </Button>
        </div>
      </div>
      {openSuccessModal && (
        <RegSuccess
          openModal={openSuccessModal}
          closeModal={() => setOpenSuccessModal(false)}
        />
      )}
    </div>
  );
};
