import {
  useGetAllLevelsQuery,
  useGetAllProgramsQuery,
  useGetStudentByIdQuery,
  useUpdateStudentMutation,
} from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PersonIcon from "@mui/icons-material/Person";
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined";
import {
  Button,
  Card,
  Divider,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function EditStudentProfile() {
  const { id } = useParams();
  const {
    data: info,
    isLoading,
    status,
  } = useGetStudentByIdQuery(id, "get-single-student");
  const [updateStudent, { isLoading: isUpdatingStudent }] =
    useUpdateStudentMutation("update-student");
  const { data: levels, isLoading: isLevelLoading } =
    useGetAllLevelsQuery("levels");
  const { data: programs, isLoading: isProgramLoading } =
    useGetAllProgramsQuery("program");
  const navigate = useNavigate();
  const [form, setForm] = useState({
    fullName: "",
    program: "",
    programName: "",
    matricule: "",
    maritalStatus: "",
    dob: "",
    gender: "",
    placeOfbirth: "",
    region: "",
    nationality: "",
    level: "",
    NID: "",
    contact: "",
    address: "",
    email: "",
    parentAddress: "",
  });

  useEffect(() => {
    switch (status) {
      case "fulfilled":
        {
          // Added curly braces to wrap the variable declaration
          const { data } = info;
          const programId = programs?.data?.find(
            (p) => p.name === data.program?.name
          )?.id;

          setForm({
            fullName: `${data.userInfo.first_name} ${data.userInfo.last_name}`,
            program: programId || "", // Set the program ID
            programName: data.program?.name || "", // Store program name
            matricule: data.matricule || "",
            maritalStatus: data.marital_status || "",
            dob: data.userInfo.dob
              ? new Date(data.userInfo.dob).toISOString().split("T")[0]
              : "",
            gender: data.userInfo.gender || "",
            placeOfbirth: data.place_of_birth || "",
            region: data.region_of_origin || "",
            nationality: data.nationality || "",
            level: data.level?.name || "",
            NID: data.nid || "",
            contact: data.userInfo.phone_number || "",
            address: data.address || "",
            email: data.userInfo.email || "",
            parentAddress: data.parent_address || "",
          });
        }
        console.log(info);
    }
  }, [status, programs]);

  const onChange = (e) => {
    const { name, value } = e.target;
    if (name === "program") {
      // When program changes, update both program ID and name
      const selectedProgram = programs?.data?.find((p) => p.id === value);
      setForm((prev) => ({
        ...prev,
        program: value,
        programName: selectedProgram?.name || "",
      }));
    } else {
      setForm((prev) => ({ ...prev, [name]: value }));
    }
  };
  const updateProfile = async (e) => {
    e.preventDefault();

    const jsonPayload = {
      first_name: form.fullName?.split(" ")[0] ?? " ",
      last_name: form.fullName?.split(" ")[1] ?? " ",
      gender: form.gender,
      email: form.email,
      phone_number: form.contact,
      dob: form.dob,
      matricule: form.matricule,
      place_of_birth: form.placeOfbirth,
      region_of_origin: form.region,
      marital_status: form.maritalStatus,
      program_id: form.program,
      level_id: form.level,
      nationality: form.nationality,
    };

    try {
      await RequestInterceptor.handleRequest(
        () =>
          updateStudent({
            id,
            body: jsonPayload,
          }),
        { onSuccess: () => navigate(-1) },
        "Student"
      );
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="w-full bg-gray-50 min-h-screen py-8 px-4 md:px-8">
      <div className="max-w-7xl mx-auto">
        <Card className="p-6 md:p-8 bg-white rounded-lg shadow-sm">
          {/* Header */}
          <div className="w-full flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <Button
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(-1)}
                sx={{
                  color: "#1F7F1F",
                  "&:hover": {
                    backgroundColor: "rgba(31, 127, 31, 0.04)",
                  },
                }}
              >
                Back
              </Button>
              <Typography variant="h5" className="text-gray-800 font-semibold">
                Edit Student Information
              </Typography>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Spinner />
            </div>
          ) : (
            <form onSubmit={updateProfile} className="space-y-8">
              {/* Basic Information Section */}
              <div>
                <div className="flex items-center gap-2 mb-6">
                  <PersonIcon color="action" />
                  <Typography variant="h6" className="text-gray-800">
                    Basic Information
                  </Typography>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextField
                    label="Full Name"
                    name="fullName"
                    value={form.fullName}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    select
                    label="Program"
                    name="program"
                    value={form.program}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  >
                    {isProgramLoading ? (
                      <MenuItem disabled>Loading...</MenuItem>
                    ) : (
                      programs?.data?.map((option) => (
                        <MenuItem key={option.id} value={option.id}>
                          {option.name}
                        </MenuItem>
                      ))
                    )}
                  </TextField>
                  <TextField
                    label="Matricule"
                    name="matricule"
                    value={form.matricule}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    select
                    label="Level"
                    name="level"
                    value={form.level}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  >
                    {isLevelLoading ? (
                      <MenuItem disabled>Loading...</MenuItem>
                    ) : (
                      levels?.data?.map((option) => (
                        <MenuItem key={option.id} value={option.id}>
                          {option.name}
                        </MenuItem>
                      ))
                    )}
                  </TextField>
                  <TextField
                    label="Date of Birth"
                    name="dob"
                    type="date"
                    value={form.dob}
                    onChange={onChange}
                    required
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    select
                    label="Gender"
                    name="gender"
                    value={form.gender}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  >
                    <MenuItem value="Male">Male</MenuItem>
                    <MenuItem value="Female">Female</MenuItem>
                  </TextField>
                </div>
              </div>

              <Divider />

              {/* Additional Information Section */}
              <div>
                <div className="flex items-center gap-2 mb-6">
                  <PhoneOutlinedIcon color="action" />
                  <Typography variant="h6" className="text-gray-800">
                    Additional Information
                  </Typography>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextField
                    label="Marital Status"
                    name="maritalStatus"
                    value={form.maritalStatus}
                    onChange={onChange}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Place of Birth"
                    name="placeOfbirth"
                    value={form.placeOfbirth}
                    onChange={onChange}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Region of Origin"
                    name="region"
                    value={form.region}
                    onChange={onChange}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Nationality"
                    name="nationality"
                    value={form.nationality}
                    onChange={onChange}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                </div>
              </div>

              {/* Contact Information Section */}
              <div>
                <div className="flex items-center gap-2 mb-6">
                  <PhoneOutlinedIcon color="action" />
                  <Typography variant="h6" className="text-gray-800">
                    Contact Information
                  </Typography>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextField
                    label="Email"
                    name="email"
                    type="email"
                    value={form.email}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Phone Number"
                    name="contact"
                    value={form.contact}
                    onChange={onChange}
                    required
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Address"
                    name="address"
                    value={form.address}
                    onChange={onChange}
                    fullWidth
                    multiline
                    rows={2}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                  <TextField
                    label="Parent Address"
                    name="parentAddress"
                    value={form.parentAddress}
                    onChange={onChange}
                    fullWidth
                    multiline
                    rows={2}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                          borderColor: "#1F7F1F",
                        },
                      },
                    }}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-4 pt-4">
                <Button
                  variant="outlined"
                  onClick={() => navigate(-1)}
                  sx={{
                    textTransform: "capitalize",
                    padding: "8px 24px",
                    borderRadius: "8px",
                    borderColor: "#1F7F1F",
                    color: "#1F7F1F",
                    "&:hover": {
                      borderColor: "#1F7F1F",
                      backgroundColor: "rgba(31, 127, 31, 0.04)",
                    },
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="success"
                  disabled={isUpdatingStudent}
                  sx={{
                    textTransform: "capitalize",
                    padding: "8px 24px",
                    borderRadius: "8px",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-2px)",
                      boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                    },
                  }}
                >
                  {isUpdatingStudent ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          )}
        </Card>
      </div>
    </div>
  );
}

export default EditStudentProfile;
