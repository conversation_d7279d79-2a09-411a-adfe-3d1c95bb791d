import {
  AllStudents,
  ContactInformation,
  StudentInformation,
} from "@pages/admin/components/students";
import PropTypes from "prop-types";

export function SelectRegistrationStep({ step }) {
  switch (step) {
    case 0:
      return <AllStudents />;
    case 1:
      return <StudentInformation />;
    case 2:
      return <ContactInformation />;
    default:
      throw new Error("Process was not informed. Include in case list.");
  }
}

// Add prop types validation
SelectRegistrationStep.propTypes = {
  step: PropTypes.number.isRequired,
};
