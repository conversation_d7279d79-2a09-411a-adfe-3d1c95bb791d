import { useGetStudentByIdQuery } from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EmailOutlinedIcon from "@mui/icons-material/EmailOutlined";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PersonIcon from "@mui/icons-material/Person";
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined";
import { Avatar, Button, Card, Chip, Divider, Typography } from "@mui/material";
import { Link, useNavigate, useParams } from "react-router-dom";

const StudentProfile = () => {
  const { id } = useParams();
  const { data, isLoading } = useGetStudentByIdQuery(id, "get-single-student");
  const navigate = useNavigate();

  return (
    <div className="w-full bg-gray-50 min-h-screen py-8 px-4 md:px-8">
      <div className="max-w-7xl mx-auto">
        <Card className="p-6 md:p-8 bg-white rounded-lg shadow-sm">
          {/* Header */}
          <div className="w-full flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <Button
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate("/admin/students")}
                sx={{
                  color: "#1F7F1F",
                  "&:hover": {
                    backgroundColor: "rgba(31, 127, 31, 0.04)",
                  },
                }}
              >
                Back
              </Button>
              <Typography variant="h5" className="text-gray-800 font-semibold">
                Student Information
              </Typography>
            </div>

            <Link to={`/admin/students/${id}/edit`}>
              <Button
                variant="contained"
                color="success"
                startIcon={<EditOutlinedIcon />}
                sx={{
                  textTransform: "capitalize",
                  padding: "8px 24px",
                  borderRadius: "8px",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                  },
                }}
              >
                Edit Profile
              </Button>
            </Link>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Spinner />
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Profile Image and Contact */}
              <div className="flex flex-col items-center">
                <Avatar
                  src={data?.data?.userInfo?.profile_image}
                  alt={`${data?.data?.userInfo?.first_name} ${data?.data?.userInfo?.last_name}`}
                  sx={{
                    width: 200,
                    height: 200,
                    mb: 4,
                    border: "4px solid #f3f4f6",
                  }}
                />
                <div className="w-full space-y-4">
                  <div className="flex items-center gap-3 text-gray-600">
                    <EmailOutlinedIcon color="action" />
                    <Typography variant="body2">
                      {data?.data?.userInfo?.email ?? "N/A"}
                    </Typography>
                  </div>
                  <div className="flex items-center gap-3 text-gray-600">
                    <PhoneOutlinedIcon color="action" />
                    <Typography variant="body2">
                      {data?.data?.userInfo?.phone_number ?? "N/A"}
                    </Typography>
                  </div>
                  <div className="flex items-center gap-3 text-gray-600">
                    <LocationOnIcon color="action" />
                    <Typography variant="body2">N/A</Typography>
                  </div>
                  <div className="flex items-center gap-3 mt-4">
                    <Typography variant="subtitle2" className="text-gray-700">
                      Fee Status:
                    </Typography>
                    <Chip
                      label="N/A"
                      size="small"
                      sx={{
                        backgroundColor: "#f3f4f6",
                        color: "#6b7280",
                        fontWeight: 500,
                      }}
                    />
                  </div>
                </div>
              </div>

              <Divider
                orientation="vertical"
                flexItem
                className="hidden lg:block"
              />

              {/* Right Column - Information Sections */}
              <div className="lg:col-span-2 space-y-8">
                {/* Basic Information */}
                <div>
                  <div className="flex items-center gap-2 mb-6">
                    <PersonIcon color="action" />
                    <Typography variant="h6" className="text-gray-800">
                      Basic Information
                    </Typography>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InfoItem
                      label="Full Name"
                      value={`${data?.data?.userInfo?.first_name} ${data?.data?.userInfo?.last_name}`}
                    />
                    <InfoItem
                      label="Degree Program"
                      value={`${data?.data?.program?.name ?? "N/A"} (${data?.data?.program?.abbreviation})`}
                    />
                    <InfoItem
                      label="Matricule"
                      value={data?.data?.matricule ?? "N/A"}
                    />
                    <InfoItem
                      label="Gender"
                      value={data?.data?.userInfo?.gender ?? "N/A"}
                    />
                    <InfoItem
                      label="Date of Birth"
                      value={data?.data?.userInfo?.dob ?? "N/A"}
                    />
                    <InfoItem
                      label="Region of Origin"
                      value={data?.data?.region_of_origin ?? "N/A"}
                    />
                    <InfoItem
                      label="Marital Status"
                      value={data?.data?.marital_status ?? "N/A"}
                    />
                    <InfoItem
                      label="Nationality"
                      value={data?.data?.nationality ?? "N/A"}
                    />
                    <InfoItem
                      label="Place of Birth"
                      value={data?.data?.place_of_birth ?? "N/A"}
                    />
                  </div>
                </div>

                <Divider />

                {/* Contact Information */}
                <div>
                  <div className="flex items-center gap-2 mb-6">
                    <PhoneOutlinedIcon color="action" />
                    <Typography variant="h6" className="text-gray-800">
                      Contact Information
                    </Typography>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InfoItem
                      label="Email Address"
                      value={data?.data?.userInfo?.email ?? "N/A"}
                    />
                    <InfoItem
                      label="Phone Number"
                      value={data?.data?.userInfo?.phone_number ?? "N/A"}
                    />
                    <InfoItem label="Parent Address" value="N/A" />
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

// Helper component for info items
const InfoItem = ({ label, value }) => (
  <div className="flex flex-col gap-1">
    <Typography variant="subtitle2" className="text-gray-500">
      {label}
    </Typography>
    <Typography variant="body2" className="text-gray-800">
      {value}
    </Typography>
  </div>
);

export default StudentProfile;
