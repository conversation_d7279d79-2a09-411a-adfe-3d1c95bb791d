import {
  useCreateStudentMutation,
  useGetAllDepartmentsQuery,
  useGetAllLevelsQuery,
  useGetAllProgramsQuery,
  useGetAllAcademicYearsQuery,
} from "@app";
import { Spinner } from "@components";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { lectureresGender } from "@pages/admin/components/lecturers/data";
import { studentRegion } from "@pages/admin/components/students/data";
import { useAdminContext } from "@pages/admin/hooks";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

export function StudentInformation() {
  const { navToContactInformation } = useAdminContext();
  const [createStudent, { isLoading: isCreatingStudent }] =
    useCreateStudentMutation("createStudent");
  const { data: departments, isLoading: isDepartmentLoading } =
    useGetAllDepartmentsQuery("departments");
  const { data: levels, isLoading: isLevelLoading } =
    useGetAllLevelsQuery("levels");
  const { data: programs, isLoading: isProgramLoading } =
    useGetAllProgramsQuery("program");
  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("academicYears");

  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    maritalStatus: "",
    email: "",
    program: "",
    phone: "",
    username: "",
    dob: "",
    gender: "",
    placeOfbirth: "",
    region: "",
    nationality: "",
    level: "",
    NID: "",
    password: "",
    department: "",
    date_of_admission: "",
    matricule: "",
    academic_year: "",
  });

  const [image, setImage] = useState(null);

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();

    formData.append("first_name", form.firstName);
    formData.append("last_name", form.lastName);
    formData.append("gender", form.gender);
    formData.append("email", form.email);
    formData.append("phone_number", form.phone);
    formData.append("password", form.password);
    formData.append("username", form.username);
    formData.append("profile_image", "image");
    formData.append("dob", form.dob);
    formData.append("matricule", form.matricule);
    formData.append("place_of_birth", form.placeOfbirth);
    formData.append("region_of_origin", form.region);
    formData.append("marital_status", form.maritalStatus);
    formData.append("program_id", form.program);
    formData.append("date_of_admission", form.date_of_admission);
    formData.append("department_id", form.department);
    formData.append("level_id", form.level);
    formData.append("nationality", form.nationality);
    formData.append("academic_year_id", form.academic_year);

    try {
      const { message, ...res } = await createStudent(formData).unwrap();
      console.log(res);
      alert(message);
      navigate(-1);
    } catch (error) {
      console.log(error);
      alert("An error occurred, while creating the student");
    }
  };

  return (
    <div className="w-full flex flex-col items-center md:px-16 md:py-10 px-8 py-5  justify-between">
      <div className="w-full flex flex-col bg-white p-8">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate("/admin/students")} />
            </div>
            <p className="text-black font-semibold text-lg">Add Student</p>
          </div>
          <Button
            variant="contained"
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate("/admin/students/upload")}
          >
            Import Csv
          </Button>
        </div>
        <p className="text-green text-md font-semibold mb-4">
          Basic Information
        </p>

        <div className=" flex w-full items-center justify-center">
          <form className="w-full" onSubmit={handleSubmit}>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.firstName}
                name="firstName"
                id="firstName"
                label="First Name"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.lastName}
                label="Last Name"
                name="lastName"
                id="lastName"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.phone}
                name="phone"
                id="phone"
                label="Phone Number"
                fullWidth
                required
              />
              <TextField
                type="text"
                onChange={onChange}
                value={form.username}
                label="Username"
                name="username"
                id="username"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                defaultValue="Female"
                onChange={onChange}
                value={form.gender}
                name="gender"
                id="gender"
                label="Gender"
                fullWidth
                required
                select
              >
                {lectureresGender.map((option) => (
                  <MenuItem key={option.id} value={option.gender}>
                    {option.gender}
                  </MenuItem>
                ))}
              </TextField>
              <TextField
                type="date"
                onChange={onChange}
                value={form.dob}
                name="dob"
                id="dob"
                label="Date of Birth"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.placeOfbirth}
                name="placeOfbirth"
                id="placeOfbirht"
                label="Place Of Birth"
                fullWidth
                required
              />
              <TextField
                defaultValue=""
                onChange={onChange}
                value={form.region}
                name="region"
                id="region"
                label="Region"
                fullWidth
                required
                select
              >
                {studentRegion.map((option) => (
                  <MenuItem key={option.id} value={option.region}>
                    {option.region}
                  </MenuItem>
                ))}
              </TextField>
            </Stack>

            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.maritalStatus}
                name="maritalStatus"
                id="maritalStatus"
                label="Marital Status"
                fullWidth
                required
                select
              >
                <MenuItem key={"single"} value={"Single"}>
                  Single
                </MenuItem>
                <MenuItem key={"maried"} value={"Maried"}>
                  Maried
                </MenuItem>
                <MenuItem key={"divorced"} value={"Divorced"}>
                  Divorced
                </MenuItem>
              </TextField>
              <TextField
                defaultValue=""
                onChange={onChange}
                value={form.email}
                name="email"
                id="email"
                label="Email Address"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.NID}
                name="NID"
                id="nid"
                label="NID Number"
                fullWidth
                required
              />
            </Stack>

            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                defaultValue=""
                select
                onChange={onChange}
                value={form.program}
                name="program"
                id="programs"
                label="Program"
                fullWidth
              >
                {isProgramLoading ? (
                  <Spinner size="15px" />
                ) : (
                  programs?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                defaultValue=""
                onChange={onChange}
                value={form.level}
                name="level"
                label="Level"
                id="level"
                fullWidth
                select
              >
                {isLevelLoading ? (
                  <Spinner size="15px" />
                ) : (
                  levels?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </Stack>

            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                defaultValue=""
                onChange={onChange}
                value={form.academic_year}
                name="academic_year"
                label="Academic Year"
                id="academic_year"
                fullWidth
                select
              >
                {isAcademicYearsLoading ? (
                  <Spinner size="15px" />
                ) : (
                  academicYears?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                type="text"
                onChange={onChange}
                value={form.nationality}
                name="nationality"
                id="nationality"
                label="Nationality"
                fullWidth
              />
            </Stack>

            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="date"
                onChange={onChange}
                value={form.date_of_admission}
                name="date_of_admission"
                id="date_of_admission"
                label="Date of admission"
                fullWidth
                required
              />
              <TextField
                type="password"
                onChange={onChange}
                value={form.password}
                name="password"
                id="password"
                label="Password"
                fullWidth
                required
              />
            </Stack>
            <Stack direction="row" sx={{ marginBottom: 4 }} gap={2}>
              <TextField
                type="text"
                onChange={onChange}
                value={form.matricule}
                name="matricule"
                id="matricule"
                label="Matricule"
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                value={form.department}
                name="department"
                id="department"
                label="Department"
                fullWidth
                required
                select
              >
                {isDepartmentLoading ? (
                  <Spinner size="15px" />
                ) : (
                  departments?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </Stack>
            {/* User Profile picture */}

            <div className="w-full flex items-center justify-between py-4">
              <div className=" flex gap-8 items-center mb-4">
                <img
                  src={
                    image
                      ? URL.createObjectURL(image)
                      : "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  }
                  alt=""
                  className=" w-32 h-32 rounded-full object-cover"
                />
                <Stack direction={"column"} gap={2}>
                  <p className="text-black font-semibold text-lg">
                    Update Profile Picture
                  </p>
                  <input
                    type="file"
                    accept="image/*"
                    capture="environment"
                    id="image-input"
                    onChange={(e) => setImage(e.target.files[0])}
                  />
                </Stack>
              </div>
              {isCreatingStudent ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize", width: "20%" }}
                  onClick={navToContactInformation}
                  type="submit"
                >
                  Continue
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
