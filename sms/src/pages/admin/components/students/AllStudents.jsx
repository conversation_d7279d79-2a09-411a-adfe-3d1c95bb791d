import { useGetAllStudentsQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { stringAvatar } from "@lib/util";
import { EditOutlined, Visibility } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Avatar,
  Button,
  FormControl,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllStudents() {
  const [filter, setFilter] = useState("Name");
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);

  const { data, isLoading } = useGetAllStudentsQuery(page);
  const navigate = useNavigate();

  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setSearchQuery("");
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const filteredStudents = data?.data?.filter((student) => {
    const valueToFilter =
      filter === "Name"
        ? `${student?.userInfo?.first_name} ${student?.userInfo?.last_name}`
        : student?.matricule;

    return valueToFilter?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const handleTableChange = (_event, value) => {
    setPage(value);
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full bg-white rounded-lg shadow-sm p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-6">
          <div>
            <Typography
              variant="h5"
              className="text-gray-800 font-semibold mb-1"
            >
              Students Dashboard
            </Typography>
            <Typography variant="body2" className="text-gray-500">
              Welcome back, Nfon Andrew
            </Typography>
          </div>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            onClick={() => navigate("/admin/students/add")}
            sx={{
              textTransform: "capitalize",
              padding: "8px 24px",
              borderRadius: "8px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              transition: "all 0.3s ease",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
              },
            }}
          >
            Add New Student
          </Button>
        </div>

        {/* Search and Filter Section */}
        <div className="w-full bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4 w-full md:w-auto">
              <TextField
                fullWidth
                size="small"
                label={`Search by ${filter}`}
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment className="cursor-pointer">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  backgroundColor: "white",
                  borderRadius: "8px",
                  "& .MuiOutlinedInput-root": {
                    "&:hover fieldset": {
                      borderColor: "#1F7F1F",
                    },
                  },
                }}
              />
              <FormControl sx={{ minWidth: 120 }}>
                <TextField
                  select
                  id="filter"
                  size="small"
                  value={filter}
                  label="Filter"
                  onChange={handleFilterChange}
                  sx={{
                    backgroundColor: "white",
                    borderRadius: "8px",
                    "& .MuiOutlinedInput-root": {
                      "&:hover fieldset": {
                        borderColor: "#1F7F1F",
                      },
                    },
                  }}
                >
                  <MenuItem value="Name">Name</MenuItem>
                  <MenuItem value="Matricule">Matricule</MenuItem>
                </TextField>
              </FormControl>
            </div>
            <Stack direction="row" spacing={2}>
              <Button
                variant="outlined"
                color="success"
                sx={{
                  textTransform: "capitalize",
                  padding: "8px 24px",
                  borderRadius: "8px",
                  borderColor: "#1F7F1F",
                  "&:hover": {
                    borderColor: "#1F7F1F",
                    backgroundColor: "rgba(31, 127, 31, 0.04)",
                  },
                }}
                disabled
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                color="success"
                onClick={() => navigate("/admin/students/upload")}
                sx={{
                  textTransform: "capitalize",
                  padding: "8px 24px",
                  borderRadius: "8px",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                  },
                }}
              >
                Import CSV
              </Button>
            </Stack>
          </div>
        </div>

        {/* Table Section */}
        <div className="w-full">
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: "none",
              border: "1px solid #e5e7eb",
              borderRadius: "8px",
              overflow: "hidden",
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: "#f9fafb" }}>
                  <StyledTableCell>S/N</StyledTableCell>
                  <StyledTableCell>Name</StyledTableCell>
                  <StyledTableCell>Matricule</StyledTableCell>
                  <StyledTableCell>Gender</StyledTableCell>
                  <StyledTableCell>Email</StyledTableCell>
                  <StyledTableCell>Contact</StyledTableCell>
                  <StyledTableCell>Level</StyledTableCell>
                  <StyledTableCell>Department</StyledTableCell>
                  <StyledTableCell>Program</StyledTableCell>
                  <StyledTableCell>Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={10} align="center">
                      <Spinner />
                    </StyledTableCell>
                  </StyledTableRow>
                ) : filteredStudents?.length < 1 ? (
                  <StyledTableRow>
                    <StyledTableCell colSpan={10} align="center">
                      <Typography
                        variant="body1"
                        className="text-gray-500 py-4"
                      >
                        No students found
                      </Typography>
                    </StyledTableCell>
                  </StyledTableRow>
                ) : (
                  filteredStudents?.map((student, idx) => (
                    <StyledTableRow
                      key={student?.userInfo?.id}
                      hover
                      sx={{
                        "&:hover": {
                          backgroundColor: "rgba(31, 127, 31, 0.04)",
                        },
                      }}
                    >
                      <StyledTableCell>
                        {(page - 1) * 10 + idx + 1}
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-2">
                          <Avatar
                            {...stringAvatar(
                              `${student?.userInfo?.first_name ?? student?.matricule} ${student?.userInfo?.last_name ?? `${idx + 1}`}`
                            )}
                            sx={{
                              width: 32,
                              height: 32,
                              fontSize: "0.875rem",
                              backgroundColor: "#1F7F1F",
                            }}
                          />
                          <Typography variant="body2">
                            {`${student?.userInfo?.first_name} ${student?.userInfo?.last_name}`}
                          </Typography>
                        </div>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.matricule ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.userInfo?.gender == "Male" ? "M" : "F"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.userInfo?.email ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.userInfo?.phone ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.level?.name ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.department?.name ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <Typography variant="body2" className="text-gray-600">
                          {student?.program?.name ?? "N/A"}
                        </Typography>
                      </StyledTableCell>
                      <StyledTableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            size="small"
                            startIcon={<Visibility />}
                            onClick={() =>
                              navigate(`/admin/students/${student?.id}`)
                            }
                            sx={{
                              color: "#1F7F1F",
                              "&:hover": {
                                backgroundColor: "rgba(31, 127, 31, 0.04)",
                              },
                            }}
                          >
                            View
                          </Button>
                          <Button
                            size="small"
                            startIcon={<EditOutlined />}
                            onClick={() =>
                              navigate(`/admin/students/${student?.id}/edit`)
                            }
                            sx={{
                              color: "#1F7F1F",
                              "&:hover": {
                                backgroundColor: "rgba(31, 127, 31, 0.04)",
                              },
                            }}
                          >
                            Edit
                          </Button>
                        </div>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <div className="flex justify-center mt-4">
            <Pagination
              count={Math.ceil((data?.total || 0) / 10)}
              page={page}
              onChange={handleTableChange}
              color="success"
              sx={{
                "& .MuiPaginationItem-root": {
                  "&.Mui-selected": {
                    backgroundColor: "#1F7F1F",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "#1F7F1F",
                    },
                  },
                },
              }}
            />
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllStudents;
