import { Button, SuccessSvg } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import PropTypes from "prop-types";

const RegSucess = ({ openModal, closeModal }) => {
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="flex flex-col justify-center items-center gap-y-2">
        <SuccessSvg />
        <h6 className="text-xl text-black font-semibold">
          Account Created Succefully
        </h6>
        <p className="text-[12px] text-grey_500 mb-4">
          You have successfully added a new student to you institution
        </p>
        <Button title={"Go Back To Dashboard"} type={"primary"} />
      </div>
    </ModalWrapper>
  );
};

// Add prop types validation
RegSucess.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default RegSucess;
