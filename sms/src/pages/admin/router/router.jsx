import {
  AddCourse,
  AddDepartments,
  AddLecturer,
  AddProgram,
  AddSchool,
  AllLevels,
  AllPrograms,
  CaMarks,
  ContactInformation,
  Courses,
  Dashboard,
  Departments,
  EditCourse,
  EditDepartment,
  EditLecturer,
  EditProgram,
  EditSchool,
  EditStudentProfile,
  FinalResults,
  ForgotPassword,
  Lecturer,
  LecturerProfile,
  Level,
  Login,
  Programs,
  Register,
  ResetPassword,
  Result,
  SchoolInformation,
  Schools,
  Student,
  StudentInformation,
  StudentProfile,
  Transcript,
  UploadCourse,
  ViewCaMarks,
  ViewCourse,
  ViewFinalResults,
  ViewProgram,
  ViewTranscript,
} from "@pages/admin/components";
import {
  AcademicYear,
  AllAcademicYears,
} from "@pages/admin/components/academicYear";
import AccountPage from "@pages/admin/components/account/Page";
import AllEvents from "@pages/admin/components/calendar/AllEvents";
import CalendarWrapper from "@pages/admin/components/calendar/CalendarWrapper";
import EventForm from "@pages/admin/components/calendar/EventForm";
import ViewDepartment from "@pages/admin/components/departments/ViewDepartment";
import UploadLecturers from "@pages/admin/components/lecturers/UploadLecturers";
import UploadProgram from "@pages/admin/components/programs/UploadProgram";
import ResultStatistics from "@pages/admin/components/results/ResultStatistics";
import StudentResultViewer from "@pages/admin/components/results/StudentResultViewer";
import UploadCA from "@pages/admin/components/results/UploadCA";
import UploadResults from "@pages/admin/components/results/UploadResults";
import SettingsPage from "@pages/admin/components/settings/Page";
import UploadStudent from "@pages/admin/components/students/UploadStudents";
import { ErrorPageNotFound } from "@pages/error";

export const adminRoutes = [
  {
    element: <Dashboard />,
    path: "/admin",
    errorElement: <ErrorPageNotFound />,
  },
  {
    element: <Lecturer />,
    path: "/admin/lecturers",
    errorElement: <ErrorPageNotFound />,
  },
  {
    element: <AccountPage />,
    path: "/admin/account",
    errorElement: <ErrorPageNotFound />,
  },
  {
    element: <LecturerProfile />,
    path: "/admin/lecturers/:id",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <EditLecturer />,
    path: "/admin/lecturers/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <AddLecturer />,
    path: "/admin/lecturers/add",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <UploadLecturers />,
    path: "/admin/lecturers/upload",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <LecturerProfile />,
    path: "/admin/lecturers/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <SettingsPage />,
    path: "/admin/settings",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <AcademicYear />,
    path: "/admin/academic-year",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllAcademicYears />,
      },
    ],
  },
  {
    element: <Level />,
    path: "/admin/levels",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllLevels />,
      },
    ],
  },

  {
    element: <Student />,
    path: "/admin/students/",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <StudentInformation />,
    path: "/admin/students/add",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <ContactInformation />,
    path: "/admin/students/add-contact",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <StudentProfile />,
    path: "/admin/students/:id",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <EditStudentProfile />,
    path: "/admin/students/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <UploadStudent />,
    path: "/admin/students/upload",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <Schools />,
    path: "/admin/schools",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <AddSchool />,
    path: "/admin/schools/create",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <SchoolInformation />,
    path: "/admin/schools/:id",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <EditSchool />,
    path: "/admin/schools/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <Departments />,
    path: "/admin/departments",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <Programs />,
    path: "/admin/programs",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllPrograms />,
      },
      {
        element: <AddProgram />,
        path: "/admin/programs/add",
      },
      {
        element: <UploadProgram />,
        path: "/admin/programs/upload",
      },
      {
        element: <EditProgram />,
        path: "/admin/programs/:id/edit",
      },
      {
        element: <ViewProgram />,
        path: "/admin/programs/:id",
      },
    ],
  },

  {
    element: <AddDepartments />,
    path: "/admin/departments/add",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <ViewDepartment />,
    path: "/admin/departments/:id",
    errorElement: <>This path doesn&apos;t exist yet</>,
  },
  {
    element: <EditDepartment />,
    path: "/admin/departments/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet</>,
  },
  {
    element: <Courses />,
    path: "/admin/courses",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <AddCourse />,
    path: "/admin/courses/add",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <ViewCourse />,
    path: "/admin/courses/:id",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <EditCourse />,
    path: "/admin/courses/:id/edit",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <UploadCourse />,
    path: "/admin/courses/upload",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <Result />,
    path: "/admin/results",
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <CaMarks />,
      },
      {
        path: "statistics",
        element: <ResultStatistics />,
      },
      {
        element: <CaMarks />,
        path: "ca-marks",
      },
      {
        element: <UploadCA />,
        path: "ca-marks/upload",
      },
      {
        element: <ViewCaMarks />,
        path: "ca-marks/:id",
      },
      {
        element: <FinalResults />,
        path: "final-results",
      },
      {
        element: <UploadResults />,
        path: "final-results/upload",
      },
      {
        element: <ViewFinalResults />,
        path: "final-results/:id",
      },
      {
        element: <Transcript />,
        path: "transcripts",
      },
      {
        element: <ViewTranscript />,
        path: "transcripts/:id",
      },
    ],
  },
  {
    element: <StudentResultViewer />,
    path: "/admin/student-results",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <CalendarWrapper />,
    path: "/admin/calendar",
    errorElement: <ErrorPageNotFound />,
    children: [
      {
        index: true,
        element: <AllEvents />,
      },
      {
        path: "add",
        element: <EventForm />,
      },
      {
        path: ":id/edit",
        element: <EventForm />,
      },
    ],
  },
];

export const authRoutes = [
  {
    element: <Login />,
    path: "/admin/login",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <Login />,
    path: "/admin/signin",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <Register />,
    path: "/admin/register",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <Register />,
    path: "/admin/signup",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <ForgotPassword />,
    path: "/admin/forgot-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
  {
    element: <ResetPassword />,
    path: "/admin/reset-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
];
