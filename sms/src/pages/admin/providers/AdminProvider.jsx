import PropTypes from "prop-types"; // Add this import
import { createContext, useReducer } from "react";

export const AdminContext = createContext({});

const stepReducer = (state, action) => {
  switch (action.type) {
    case "allstudents":
      return { ...state, studentStep: 0 };
    case "studentinformation":
      return { ...state, studentStep: 1 };
    case "contactinformation":
      return { ...state, studentStep: 2 };
    case "editstudent":
      return { ...state, studentStep: 3 };

    case "all":
      return { ...state, lecturerStep: 0 };

    case "allDepartments":
      return { ...state, departmentStep: 0 };
    case "uploadDepartments":
      return { ...state, departmentStep: 1 };
    case "addDepartments":
      return { ...state, departmentStep: 2 };
    //School Section
    case "allSchools":
      return { ...state, schoolStep: 0 };
    case "addSchool":
      return { ...state, schoolStep: 1 };
    case "editSchool":
      return { ...state, schoolStep: 2 };
    case "uploadSchool":
      return { ...state, schoolStep: 3 };

    case "allCourses":
      return { ...state, courseStep: 0 };

    default:
      return state;
  }
};

export function AdminProvider({ children }) {
  const [state, dispatch] = useReducer(stepReducer, {
    step: 0,
    lecturerStep: 0,
    departmentStep: 0,
    schoolStep: 0,
    studentStep: 0,
    courseStep: 0,
  });

  const navToAllStudents = () => dispatch({ type: "allstudents" });

  const viewAllLecturers = () => dispatch({ type: "all" });

  const navToAllDepartments = () => dispatch({ type: "allDepartments" });
  const navToUploadDepartments = () => dispatch({ type: "uploadDepartments" });
  const navToaddDepartments = () => dispatch({ type: "addDepartments" });

  const navToAllSchools = () => dispatch({ type: "allSchools" });
  const navToUploadSchool = () => dispatch({ type: "uploadSchool" });

  const navToAllCourses = () => dispatch({ type: "allCourses" });

  const value = {
    step: state.step,
    lecturerStep: state.lecturerStep,
    departmentStep: state.departmentStep,
    schoolStep: state.schoolStep,
    studentStep: state.studentStep,
    courseStep: state.courseStep,
    navToAllStudents,
    viewAllLecturers,
    navToAllDepartments,
    navToUploadDepartments,
    navToaddDepartments,
    navToAllSchools,
    navToUploadSchool,
    navToAllCourses,
  };
  return (
    <AdminContext.Provider value={value}>{children}</AdminContext.Provider>
  );
}

// Add PropTypes validation
AdminProvider.propTypes = {
  children: PropTypes.node.isRequired, // Add this line
};
