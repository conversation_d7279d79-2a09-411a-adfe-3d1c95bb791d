import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  But<PERSON>,
  Container,
  Avatar,
} from '@mui/material';
import {
  SchoolOutlined,
  PersonOutlined,
  AdminPanelSettingsOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import sideImage from "@assets/images/image.jpg";
import Logo from "@assets/images/logo.jpg";

const PortalSelection = () => {
  const navigate = useNavigate();

  const portals = [
    {
      title: 'Student Portal',
      description: 'Access your academic dashboard and learning resources.',
      icon: <SchoolOutlined sx={{ fontSize: 48 }} />,
      color: '#16a34a', // System green color
      path: '/student/login',
    },
    {
      title: 'Lecturer Portal',
      description: 'Manage courses and track student progress.',
      icon: <PersonOutlined sx={{ fontSize: 48 }} />,
      color: '#0f766e', // Teal color
      path: '/lecturer/login',
    },
    {
      title: 'Admin Portal',
      description: 'System administration and management.',
      icon: <AdminPanelSettingsOutlined sx={{ fontSize: 48 }} />,
      color: '#1e40af', // Blue color
      path: '/admin/login',
    }
  ];

  const handlePortalAccess = (path) => {
    navigate(path);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        py: 8,
        backgroundImage: `url(${sideImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, rgba(22, 163, 74, 0.9) 0%, rgba(15, 118, 110, 0.9) 100%)',
          zIndex: 1,
        },
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box textAlign="center" mb={8}>
            {/* Logo and School Name */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                mb={4}
                sx={{
                  background: 'rgba(255,255,255,0.15)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 4,
                  p: 3,
                  border: '1px solid rgba(255,255,255,0.2)',
                  maxWidth: 'fit-content',
                  mx: 'auto',
                }}
              >
                <img
                  src={Logo}
                  alt="EHIST Logo"
                  style={{
                    width: '60px',
                    height: '60px',
                    marginRight: '16px',
                    borderRadius: '8px',
                  }}
                />
                <Typography
                  variant="h3"
                  fontWeight="800"
                  color="white"
                  sx={{
                    fontSize: { xs: '2rem', md: '2.5rem' },
                    textShadow: '0 4px 8px rgba(0,0,0,0.3)',
                    letterSpacing: '-0.02em',
                  }}
                >
                  EHIST
                </Typography>
              </Box>
            </motion.div>

            <Typography
              variant="h4"
              fontWeight="600"
              color="white"
              gutterBottom
              sx={{
                fontSize: { xs: '1.8rem', md: '2.2rem' },
                textShadow: '0 4px 8px rgba(0,0,0,0.3)',
                mb: 2,
              }}
            >
              Academic Portal
            </Typography>
            <Typography
              variant="h6"
              color="rgba(255,255,255,0.95)"
              sx={{
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                fontWeight: 400,
                textShadow: '0 2px 4px rgba(0,0,0,0.2)',
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: 1.4,
              }}
            >
              Secure access to your learning management system
            </Typography>
          </Box>
        </motion.div>

        {/* Portal Cards */}
        <Grid container spacing={4} justifyContent="center">
          {portals.map((portal, index) => (
            <Grid item xs={12} md={6} lg={4} key={portal.title}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.2, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255,255,255,0.98)',
                    backdropFilter: 'blur(15px)',
                    border: '1px solid rgba(255,255,255,0.3)',
                    borderRadius: 3,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      boxShadow: '0 25px 50px rgba(0,0,0,0.15)',
                      transform: 'translateY(-8px) scale(1.02)',
                      border: `2px solid ${portal.color}`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box display="flex" alignItems="center" mb={3}>
                      <Avatar
                        sx={{
                          width: 80,
                          height: 80,
                          backgroundColor: portal.color,
                          mr: 3,
                          boxShadow: `0 8px 24px ${portal.color}40`,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'scale(1.1)',
                            boxShadow: `0 12px 32px ${portal.color}60`,
                          },
                        }}
                      >
                        {portal.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h5" fontWeight="bold" gutterBottom>
                          {portal.title}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {portal.description}
                        </Typography>
                      </Box>
                    </Box>

                    <Box mb={3}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                        Secure access portal for authorized users only.
                      </Typography>
                    </Box>

                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePortalAccess(portal.path)}
                      sx={{
                        backgroundColor: portal.color,
                        borderRadius: 2,
                        py: 1.8,
                        fontSize: '1.1rem',
                        fontWeight: 'bold',
                        textTransform: 'none',
                        boxShadow: `0 4px 16px ${portal.color}40`,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          backgroundColor: portal.color,
                          filter: 'brightness(0.9)',
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 24px ${portal.color}60`,
                        },
                        '&:active': {
                          transform: 'translateY(0px)',
                        },
                      }}
                    >
                      Access {portal.title}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Quick Access Links */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <Box textAlign="center" mt={6}>
            <Typography variant="h6" color="white" mb={2}>
              Quick Access (For Testing) - No Login Required
            </Typography>
            <Box display="flex" justifyContent="center" gap={2} flexWrap="wrap">
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate('/student')}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Student Dashboard (Direct)
              </Button>
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate('/lecturer')}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Lecturer Dashboard (Direct)
              </Button>
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate('/admin')}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Admin Dashboard (Direct)
              </Button>
              <Button
                variant="contained"
                color="secondary"
                onClick={() => navigate('/test')}
                sx={{
                  backgroundColor: '#ff6b35',
                  '&:hover': { backgroundColor: '#e55a2b' }
                }}
              >
                🧪 Test All Features
              </Button>
            </Box>
          </Box>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <Box textAlign="center" mt={8}>
            <Typography variant="body2" color="rgba(255,255,255,0.8)">
              © 2024 School Management System. All rights reserved.
            </Typography>
            <Typography variant="body2" color="rgba(255,255,255,0.9)" mt={1} fontWeight="medium">
              Powered by Skye8
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default PortalSelection;
