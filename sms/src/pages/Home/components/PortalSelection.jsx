import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  But<PERSON>,
  Container,
  Avatar,
} from '@mui/material';
import {
  SchoolOutlined,
  PersonOutlined,
  AdminPanelSettingsOutlined,
  AccountBalanceOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const PortalSelection = () => {
  const navigate = useNavigate();

  const portals = [
    {
      title: 'Student Portal',
      description: 'Access your courses, assignments, grades, and academic resources.',
      icon: <SchoolOutlined sx={{ fontSize: 48 }} />,
      color: '#3b82f6',
      path: '/student/login',
      features: [
        'View course materials',
        'Submit assignments',
        'Take assessments',
        'Check grades and results',
        'Access notifications'
      ]
    },
    {
      title: 'Lecturer Portal',
      description: 'Manage your courses, create assignments, and track student progress.',
      icon: <PersonOutlined sx={{ fontSize: 48 }} />,
      color: '#10b981',
      path: '/lecturer/login',
      features: [
        'Manage course content',
        'Create assignments & assessments',
        'Grade student submissions',
        'View student analytics',
        'Upload course materials'
      ]
    },
    {
      title: 'Admin Portal',
      description: 'Comprehensive system administration and management tools.',
      icon: <AdminPanelSettingsOutlined sx={{ fontSize: 48 }} />,
      color: '#f59e0b',
      path: '/admin/login',
      features: [
        'User management',
        'Course administration',
        'System configuration',
        'Reports and analytics',
        'Security monitoring'
      ]
    },
    {
      title: 'Finance Portal',
      description: 'Handle financial operations, fee management, and transactions.',
      icon: <AccountBalanceOutlined sx={{ fontSize: 48 }} />,
      color: '#ef4444',
      path: '/finance/login',
      features: [
        'Fee management',
        'Payment processing',
        'Financial reports',
        'Transaction history',
        'Revenue analytics'
      ]
    }
  ];

  const handlePortalAccess = (path) => {
    navigate(path);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        py: 8,
      }}
    >
      <Container maxWidth="lg">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h2"
              fontWeight="bold"
              color="white"
              gutterBottom
              sx={{ fontSize: { xs: '2rem', md: '3rem' } }}
            >
              School Management System
            </Typography>
            <Typography
              variant="h5"
              color="rgba(255,255,255,0.9)"
              sx={{ fontSize: { xs: '1rem', md: '1.25rem' } }}
            >
              Choose your portal to access the Learning Management System
            </Typography>
          </Box>
        </motion.div>

        {/* Portal Cards */}
        <Grid container spacing={4}>
          {portals.map((portal, index) => (
            <Grid item xs={12} md={6} key={portal.title}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.2, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255,255,255,0.95)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box display="flex" alignItems="center" mb={3}>
                      <Avatar
                        sx={{
                          width: 80,
                          height: 80,
                          backgroundColor: portal.color,
                          mr: 3,
                        }}
                      >
                        {portal.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h5" fontWeight="bold" gutterBottom>
                          {portal.title}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {portal.description}
                        </Typography>
                      </Box>
                    </Box>

                    <Box mb={3}>
                      <Typography variant="h6" fontWeight="medium" mb={2}>
                        Key Features:
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, m: 0 }}>
                        {portal.features.map((feature, idx) => (
                          <Typography
                            component="li"
                            variant="body2"
                            color="text.secondary"
                            key={idx}
                            sx={{ mb: 0.5 }}
                          >
                            {feature}
                          </Typography>
                        ))}
                      </Box>
                    </Box>

                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePortalAccess(portal.path)}
                      sx={{
                        backgroundColor: portal.color,
                        '&:hover': {
                          backgroundColor: portal.color,
                          filter: 'brightness(0.9)',
                        },
                        py: 1.5,
                        fontSize: '1.1rem',
                        fontWeight: 'bold',
                      }}
                    >
                      Access {portal.title}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <Box textAlign="center" mt={8}>
            <Typography variant="body2" color="rgba(255,255,255,0.8)">
              © 2024 School Management System. All rights reserved.
            </Typography>
            <Typography variant="body2" color="rgba(255,255,255,0.6)" mt={1}>
              Powered by Advanced Learning Management Technology
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default PortalSelection;
