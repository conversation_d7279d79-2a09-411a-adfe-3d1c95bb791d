import error from "@assets/images/404.svg";
import { Button } from "@mui/material";
import { useNavigate } from "react-router-dom";

const ErrorPageNotFound = () => {
  const navigate = useNavigate();
  return (
    <div className="w-full h-full flex flex-col justify-center items-center md:p-32 p-8">
      <div className="w-full flex md:flex-row flex-col justify-between items-center">
        <div className=" md:w-2/5 w-full flex flex-col items-center text-center justify-between">
          <h1 className="text-green font-black text-[128px]">Oops!</h1>
          <p className="text-black font-normal text-md">
            The page you are requesting for might have been removed, had
            it&apos;s name changed or temporarily unavailable.
          </p>
          <Button
            color="success"
            variant="contained"
            sx={{ marginTop: "20px" }}
            onClick={() => navigate(-1)}
          >
            GO TO HOMEPAGE
          </Button>
        </div>
        <div className="md:w-2/5 hidden md:flex justify-center">
          <img src={error} className=" object-cover" />
        </div>
      </div>
    </div>
  );
};

export default ErrorPageNotFound;
