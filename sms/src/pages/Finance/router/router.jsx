import {
  Accounts,
  AddFeePayment,
  AllAccounts,
  AllExpenditure,
  AllFeePayment,
  AllFeeTypes,
  AllIncome,
  AllTransactions,
  Category,
  Dashboard,
  EditFeePayment,
  Expenditure,
  ExpenditureCategory,
  FeePayment,
  Income,
  IncomeCategory,
  Transactions,
  ViewFeePayment,
} from "@pages/Finance/components";

export const financeRoutes = [
  {
    path: "/finance",
    element: <Dashboard />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    path: "/finance/accounts",
    element: <Accounts />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllAccounts />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },

  {
    path: "/finance/transactions",
    element: <Transactions />,

    children: [
      {
        index: true,
        element: <AllTransactions />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },
  {
    path: "/finance/transactions/fee-payment",
    element: <FeePayment />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllFeePayment />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
      {
        path: "/finance/transactions/fee-payment/add",
        element: <AddFeePayment />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
      {
        path: "/finance/transactions/fee-payment/:id/edit",
        element: <EditFeePayment />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
      {
        path: "/finance/transactions/fee-payment/:id",
        element: <ViewFeePayment />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },
  {
    path: "/finance/transactions/expenditure",
    element: <Expenditure />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllExpenditure />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },
  {
    path: "/finance/transactions/income",
    element: <Income />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        index: true,
        element: <AllIncome />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },

  {
    path: "/finance/category",
    element: <Category />,
    errorElement: <>This path doesn&apos;t exist yet.</>,
    children: [
      {
        path: "/finance/category/income",
        element: <IncomeCategory />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
      {
        path: "/finance/category/expenditure",
        element: <ExpenditureCategory />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
      {
        path: "/finance/category/fee-types",
        element: <AllFeeTypes />,
        errorElement: <>This path doesn&apos;t exist yet.</>,
      },
    ],
  },
];
