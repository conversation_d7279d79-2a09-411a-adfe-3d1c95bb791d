import { logout } from "@features";
import AuthGuard from "@pages/auth/AuthGaurd";
import { DashboardLayout } from "@pages/Finance/components";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Outlet } from "react-router-dom";

function Layout() {
  const { role, token } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  useEffect(() => {
    if (!token) {
      dispatch(logout());
      window.location.replace("/admin/login");
    }
    if (role !== "accountant") {
      dispatch(logout());
      window.location.replace("/admin/login");
    }
  });
  return (
    <>
      <DashboardLayout>
        <AuthGuard allowedRoles={["accountant"]}>
          <Outlet />
        </AuthGuard>
      </DashboardLayout>
    </>
  );
}

export default Layout;
