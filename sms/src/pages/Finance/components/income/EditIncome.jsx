import {
  useEditIncomeMutation,
  useGetAllAccountsQuery,
  useGetAllIncomeCategoriesQuery,
} from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { MenuItem, Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function EditIncome({ openModal, closeModal, currentIncome }) {
  EditIncome.tag = "Edit Income";
  const [form, setForm] = useState({
    amount: "",
    account_id: "",
    income_category_id: "",
    from: "",
    description: "",
    income_date: new Date(),
  });
  const {
    data: incomeCategories,
    isLoading: isIncomeCategoriesLoading,
    isError,
  } = useGetAllIncomeCategoriesQuery("getAllIncomeCategories");
  const { data: accounts, isLoading: isAccountsLoading } =
    useGetAllAccountsQuery("getAllAccounts");

  const [updateIncome, { isLoading: isUpdatingIncome }] =
    useEditIncomeMutation("Edit income");

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const onSubmit = async (e) => {
    e.preventDefault();
    await RequestInterceptor.handleRequest(
      () => updateIncome({ id: currentIncome.id, body: form }),
      {
        onSuccess: () => {
          window.location.reload();
        },
      },
      EditIncome.tag
    );
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Edit Income Details
        </h2>
        <form className="w-full flex flex-col gap-4" onSubmit={onSubmit}>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <>
              <TextField
                type="text"
                onChange={onChange}
                value={form.income_category_id}
                name="income_category_id"
                id="income_category_id"
                label="Category"
                fullWidth
                select
                required
              >
                {isIncomeCategoriesLoading ? (
                  <Spinner size="14px" />
                ) : isError ? (
                  <code>Error fetching Income Categories</code>
                ) : (
                  incomeCategories?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                type="text"
                onChange={onChange}
                value={form.account_id}
                name="account_id"
                id="account_id"
                label="Account Name"
                fullWidth
                select
                required
              >
                {isAccountsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  accounts?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </>
          </div>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.amount}
              label="Amount"
              name="amount"
              id="amount"
              fullWidth
              required
            />
            <TextField
              type="date"
              onChange={onChange}
              value={form.income_date}
              label="Date"
              name="income_date"
              id="income_date"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex gap-3 flex-col mb-4">
            <TextField
              type="text"
              onChange={onChange}
              value={form.from}
              name="from"
              id="from"
              label="From"
              fullWidth
              required
            />

            <textarea
              id="description"
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Expense description..."
              value={form.description}
              name="description"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          {isUpdatingIncome ? (
            <Spinner />
          ) : (
            <Button title={"Create"} type={"primary"} onClick={onSubmit} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add PropTypes validation
EditIncome.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  currentIncome: PropTypes.shape({
    id: PropTypes.string.isRequired,
  }).isRequired,
};

export default EditIncome;
