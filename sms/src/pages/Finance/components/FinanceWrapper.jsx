import PropTypes from "prop-types";

export function FinanceWrapper({ title, children }) {
  return (
    <section className="min-h-screen w-full py-5 flex flex-col items-start">
      <div className="w-full items-start mb-2">
        <div className="w-full items-start mb-3">
          <p className="text-black text-md font-semibold">{title}</p>
          <div className="w-[35px] h-[2px] bg-red"></div>
        </div>
      </div>
      <div className="mb-3 w-full flex flex-col items-center justify-center">
        {children}
      </div>
      <div className="w-full flex items-center justify-center">
        <p className="text-grey_500 text-sm">
          &copy; {new Date().getFullYear()} Copyright{" "}
          <span className="text-primary text-sm">Skye8.tech</span>
        </p>
      </div>
    </section>
  );
}

FinanceWrapper.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};
