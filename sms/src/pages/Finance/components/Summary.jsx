import { useGetDashboardStatsQuery } from "@app";
import { Spinner } from "@components";
import LineChartComponent from "@components/molecules/LineChartComponent";
import PieChartComponent from "@components/molecules/PieChartComponent";
import {
  MoreHorizRounded,
  SchoolOutlined,
  SwapHorizRounded,
  TuneRounded,
} from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import { Button } from "@mui/material";
import {
  EventCalendar,
  RecentTransactions,
} from "@pages/Finance/components/dashboardCharts";
import Props from "@pages/Home/Props";
import { useEffect } from "react";
import { useSelector } from "react-redux";

function Summary() {
  const currentYear = new Date().getFullYear();
  const { user } = useSelector((state) => state.auth);
  const {
    data: statistics,
    isLoading: isStatisticsLoading,
    status,
  } = useGetDashboardStatsQuery("finance dashbaord stats");

  useEffect(() => {
    console.log(statistics);
  }, [status]);

  return (
    <div className="w-full flex flex-col items-start py-5 justify-between">
      <Props header={"Dashboard"} />
      <div className="w-full flex justify-between mt-5">
        <h6 className="font-bold">
          Welcome,{" "}
          <b className="text-[#186318]">
            {" "}
            {user?.first_name} {user?.last_name},
          </b>
        </h6>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="success"
          style={{ textTransform: "capitalize" }}
          disabled
        >
          Report
        </Button>
      </div>
      {isStatisticsLoading ? (
        <Spinner />
      ) : (
        <>
          <div className="w-full grid md:grid-cols-4 grid-cols-2  gap-4 mt-5">
            <div className="w-full flex justify-center items-center gap-4 bg-purple text-white py-8 rounded-[10px]">
              <div className="flex items-center p-2 rounded-full bg-white text-purple">
                <SchoolOutlined />
              </div>
              <div>
                <p>{statistics?.data?.total_students}</p>
                <p className="text-[10.5px]">Students</p>
              </div>
            </div>
            <div className="w-full flex justify-center items-center gap-4 bg-orange text-white py-8 rounded-[10px]">
              <div className="flex items center p-2 rounded-[50%] bg-white text-orange">
                <SwapHorizRounded />
              </div>
              <div>
                <p>{statistics?.data?.balance}</p>
                <p className="text-[10.5px]">Balance (xaf)</p>
              </div>
            </div>

            <div className="flex justify-center items-center gap-4 bg-green text-white w-full py-8 rounded-[10px]">
              <div className="flex items center p-2 rounded-full bg-white text-green">
                <SwapHorizRounded />
              </div>
              <div>
                <p>{statistics?.data?.total_income}</p>
                <p className="text-[10.5px]">Income (xaf)</p>
              </div>
            </div>
            <div className="flex justify-center items-center gap-4 bg-red text-white w-full py-8 rounded-[10px]">
              <div className="flex items center p-2 rounded-full bg-white text-red">
                <SwapHorizRounded />
              </div>
              <div>
                <p>{statistics?.data?.total_expenditure}</p>
                <p className="text-[10.5px]">Expenditure (xaf)</p>
              </div>
            </div>
          </div>
          <div className="w-full mt-5 grid md:grid-cols-2 grid-cols-1 gap-4">
            <div className="w-full bg-white drop-shadow-md rounded-[10px] pt-2 px-4">
              <div className="flex justify-end gap-4">
                <select className="w-20 border" name="" id="">
                  <option value="">{currentYear}</option>
                </select>
                <MoreHorizRounded />
              </div>
              <LineChartComponent
                label={"Income - Expense"}
                label1={"Income"}
                label2={"Expense"}
                data1={statistics?.data?.income_data?.map(
                  (income) => income.total
                )}
                data2={statistics?.data?.expense_data?.map(
                  (income) => income.total
                )}
                labels={statistics?.data?.income_data?.map(
                  (income) => income.date
                )}
              />
            </div>
            <div className="bg-white drop-shadow-md w-full flex flex-col justify-center p-5 rounded-[10px]">
              <div className="w-full flex items-center  justify-end gap-4">
                <select className="w-20 border" name="" id="">
                  <option value="">{currentYear}</option>
                </select>
                <MoreHorizRounded />
              </div>

              <PieChartComponent data={statistics?.data?.accounts} />
            </div>

            <div className="bg-white w-full drop-shadow-md text-center rounded-[10px] p-5">
              <div className="w-full flex justify-between items-center">
                <p className="text-[16px] font-semibold">Recent Transactions</p>
                <div className="flex justify-center items-center gap-2 bg-grey w-24 h-8">
                  <TuneRounded />
                  <p>Filters</p>
                </div>
              </div>
              <RecentTransactions />
              <span className="text-sm font-semibold text-center text-blue cursor-pointer mt-5">
                View More
              </span>
            </div>
            <div className="bg-white w-full drop-shadow-md rounded-[10px] p-5">
              <span className="text-[16px] font-semibold">Events Calendar</span>
              <div className="w-full flex justify-center items-center">
                <EventCalendar />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default Summary;
