import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import PropTypes from "prop-types"; // Import PropTypes

export default function SimpleBarChart({ labels, data }) {
  return (
    <BarChart
      height={300}
      series={[{ data: data, label: "Income", id: "uvId" }]}
      xAxis={[{ data: labels, scaleType: "band" }]}
    />
  );
}

// Add prop types validation
SimpleBarChart.propTypes = {
  labels: PropTypes.array.isRequired, // Validate labels as an array
  data: PropTypes.array.isRequired, // Validate data as an array
};
