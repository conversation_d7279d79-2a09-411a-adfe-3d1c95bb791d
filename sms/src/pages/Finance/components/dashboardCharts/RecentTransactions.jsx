import { MoreHorizRounded } from "@mui/icons-material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";

function createData(category, transactionID, amount, date, action) {
  return { category, transactionID, amount, date, action };
}

const rows = [
  createData(
    "Fee Payment",
    "Transaction102",
    "+50,000XAF",
    "14-05-2024",
    <MoreHorizRounded />
  ),
  createData(
    "Fee Payment",
    "Transaction102",
    "+50,000XAF",
    "14-05-2024",
    <MoreHorizRounded />
  ),
  createData(
    "Fee Payment",
    "Transaction102",
    "+50,000XAF",
    "14-05-2024",
    <MoreHorizRounded />
  ),
];

export default function BasicTable() {
  return (
    <TableContainer className="mt-4">
      <Table sx={{ width: 600 }} aria-label="simple table">
        <TableHead className="bg-grey">
          <TableRow>
            <TableCell>Category</TableCell>
            <TableCell align="right">Transaction ID</TableCell>
            <TableCell align="right">Amount (XAF)</TableCell>
            <TableCell align="right">Date</TableCell>
            <TableCell align="right">Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.map((row) => (
            <TableRow
              key={row.category}
              sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {row.category}
              </TableCell>
              <TableCell align="right">{row.transactionID}</TableCell>
              <TableCell align="right">{row.amount}</TableCell>
              <TableCell align="right">{row.date}</TableCell>
              <TableCell align="right">{row.action}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
