import { styled } from "@mui/material/styles";
import { PieChart } from "@mui/x-charts/PieChart";
import { useDrawingArea } from "@mui/x-charts/hooks";
import PropTypes from "prop-types";

const data = [
  { value: 800, label: "Unpaid" },
  { value: 800, label: "Half Paid" },
  { value: 1200, label: "Full Payment" },
];

const size = {
  width: 420,
  height: 200,
};

const StyledText = styled("text")(({ theme }) => ({
  fill: theme.palette.text.primary,
  textAnchor: "middle",
  dominantBaseline: "central",
  fontSize: 20,
}));

function PieCenterLabel({ children }) {
  const { width, height, left, top } = useDrawingArea();
  return (
    <StyledText x={left + width / 2} y={top + height / 2}>
      {children}
    </StyledText>
  );
}

// Add prop validation for PieCenterLabel
PieCenterLabel.propTypes = {
  children: PropTypes.node.isRequired,
};

export default function PieChartWithCenterLabel() {
  return (
    <div className="w-full flex  items-center justify-center">
      <PieChart series={[{ data, innerRadius: 80 }]} {...size}>
        <PieCenterLabel>Fee Payment</PieCenterLabel>
      </PieChart>
    </div>
  );
}
