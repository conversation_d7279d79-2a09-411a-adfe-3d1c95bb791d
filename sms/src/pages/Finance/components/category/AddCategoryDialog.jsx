import {
  useCreateExpenseCategoriesMutation,
  useCreateFeeTypeMutation,
  useCreateIncomeCategoriesMutation,
} from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types";
import { useState } from "react";

function AddCategoryDialog({ openModal, closeModal, type }) {
  if (!type) {
    throw new Error(
      "Type must be specified. one of 'income', 'expenditure', fee-type "
    );
  }
  if (type != "income" && type != "expenditure" && type != "fee") {
    throw new Error("Type must one of 'income', 'expenditure', 'fee-type ");
  }
  const [form, setForm] = useState({
    category: "",
    desc: "",
  });
  const [createCategory, { isLoading: isCreatingCategory }] =
    useCreateIncomeCategoriesMutation("create Income categories");
  const [createExpenseCategory, { isLoading: isCreatingExpenseCategory }] =
    useCreateExpenseCategoriesMutation("create expense category");
  const [createFeeType, { isLoading: isCreatingFeeType }] =
    useCreateFeeTypeMutation("Create fee type");
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();

    const actions = {
      income: createCategory,
      expenditure: createExpenseCategory,
      fee: createFeeType,
    };

    const body = {
      name: form.category,
      description: form.desc,
    };
    await RequestInterceptor.handleRequest(
      () => actions[type](body),
      {
        onSuccess: () => {
          window.location.reload();
        },
      },
      `Create ${type}`
    );
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Add {type} Category
        </h2>
        <form className="w-full flex flex-col gap-4 " onSubmit={handleSubmit}>
          <div className="w-full flex gap-3 flex-col mb-4">
            <TextField
              type="text"
              onChange={onChange}
              value={form.category}
              name="category"
              id="category"
              label="Category"
              fullWidth
              required
            />

            <textarea
              id="desc"
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Category Description..."
              value={form.desc}
              name="desc"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          {isCreatingCategory ||
          isCreatingExpenseCategory ||
          isCreatingFeeType ? (
            <Spinner />
          ) : (
            <Button title={"Create"} type={"primary"} onClick={handleSubmit} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}
AddCategoryDialog.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  type: PropTypes.oneOf(["income", "expenditure", "fee"]).isRequired,
};

export default AddCategoryDialog;
