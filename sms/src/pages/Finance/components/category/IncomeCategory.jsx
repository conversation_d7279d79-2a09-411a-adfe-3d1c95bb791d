import { useGetAllIncomeCategoriesQuery } from "@app";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  InputAdornment,
  MenuItem,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { WelcomeHeader } from "@pages/Finance/components";
import { CategoryContainer } from "@pages/Finance/components/category";
import { useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function IncomeCategory() {
  const { data, isLoading } = useGetAllIncomeCategoriesQuery(
    "all income categories"
  );
  const [openAddModal, setOpenAddModal] = useState(false);
  const [filter, setFilter] = useState("Name");

  const handleChange = (event) => {
    setFilter(event.target.value);
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full">
        <div className="w-full flex items-start justify-between mb-4">
          <WelcomeHeader />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => setOpenAddModal(true)}
          >
            Income Category
          </Button>
        </div>

        <div className="w-full flex flex-col items-start h-full">
          <p className="text-black text-lg font-semibold mb-4">
            Income Category
          </p>
          <div className="w-full flex items-start justify-between mb-4">
            <div className="flex items-start gap-2">
              <Box>
                <TextField
                  fullWidth
                  size="small"
                  label="Search for category"
                  value={""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment>
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ minWidth: 120 }}>
                <FormControl fullWidth>
                  <TextField
                    select
                    id="filter"
                    size="small"
                    value={filter}
                    label="Filter"
                    name="filter"
                    onChange={handleChange}
                  >
                    <MenuItem value="Name">category</MenuItem>
                  </TextField>
                </FormControl>
              </Box>
            </div>
          </div>

          <CategoryContainer
            categories={data?.data}
            isLoading={isLoading}
            type={"income"}
            openAddModal={openAddModal}
            setOpenAddModal={setOpenAddModal}
          />
        </div>
      </div>
    </ThemeProvider>
  );
}

export default IncomeCategory;
