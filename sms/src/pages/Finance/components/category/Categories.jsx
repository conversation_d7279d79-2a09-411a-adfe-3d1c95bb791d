import {
  useDeleteExpenseCategoryMutation,
  useDeleteIncomeCategoryMutation,
} from "@app";
import { Spinner, StyledTableCell, StyledTableRow } from "@components";
import { DateWizard, RequestInterceptor } from "@lib/util";
import { DeleteOutline, EditOutlined } from "@mui/icons-material";
import {
  Box,
  Grid,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import {
  AddCategoryDialog,
  EditCategoryDialog,
} from "@pages/Finance/components/category";
import PropTypes from "prop-types"; // Ensure this import is present
import { useState } from "react";

function Categories({
  type,
  categories,
  isLoading,
  openAddModal,
  setOpenAddModal,
}) {
  if (!type) {
    throw new Error("Type must be specified. one of 'income', 'expenditure' ");
  }
  if (type != "income" && type != "expenditure" && type != "fee") {
    throw new Error("Type must one of 'income', 'expenditure', 'fee-type ");
  }
  const [currentData, setCurrentData] = useState();

  const [openEditModal, setOpenEditModal] = useState(false);
  const [deleteIncomeCategory, { isLoading: isDeletingIncomeCategory }] =
    useDeleteIncomeCategoryMutation("Delete income categories");
  const [
    deleteExpenditureCategory,
    { isLoading: isDeletingExpenditureCategory },
  ] = useDeleteExpenseCategoryMutation("Delete expense categories");
  const actions = {
    income: deleteIncomeCategory,
    expenditure: deleteExpenditureCategory,
  };
  const deleteCategory = async (id) => {
    await RequestInterceptor.handleRequest(
      () => actions[type](id),
      {},
      `Delete ${type} Category`
    );
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(1); // Remove if not used
  const [page, setPage] = useState(1);

  const handleTableChange = (event, value) => {
    setPage(value);
    // You can add pagination logic here if needed
  };

  return (
    <div className="flex flex-col w-full my-2">
      <TableContainer component={Paper}>
        <Table sx={{ maxWidth: "100%" }}>
          <TableHead>
            <TableRow>
              <StyledTableCell>S/N</StyledTableCell>
              <StyledTableCell>{type} Type</StyledTableCell>
              <StyledTableCell>Created At</StyledTableCell>
              <StyledTableCell>Updated At</StyledTableCell>
              <StyledTableCell>Action</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <Spinner />
            ) : categories?.length < 1 ? (
              <div className="grid place-items-center">No {type} category</div>
            ) : (
              categories?.map((category, idx) => (
                <StyledTableRow key={category.id}>
                  <StyledTableCell>{idx + 1}</StyledTableCell>
                  <StyledTableCell>{category?.name ?? "N/A"}</StyledTableCell>
                  <StyledTableCell>
                    {DateWizard.toLocaleDate(category.created_at) ?? "N/A"}
                  </StyledTableCell>
                  <StyledTableCell>
                    {DateWizard.toLocaleDate(category.updated_at) ?? "N/A"}
                  </StyledTableCell>
                  <StyledTableCell>
                    <div className="flex items-center gap-3">
                      <EditOutlined
                        color="success"
                        sx={{ cursor: "pointer" }}
                        onClick={() => {
                          setCurrentData(category);
                          setOpenEditModal(true);
                        }}
                      />

                      {isDeletingIncomeCategory ||
                      isDeletingExpenditureCategory ? (
                        <Spinner />
                      ) : (
                        <DeleteOutline
                          color="error"
                          sx={{ cursor: "pointer" }}
                          onClick={() => deleteCategory(category.id)}
                        />
                      )}
                    </div>
                  </StyledTableCell>
                </StyledTableRow>
              ))
            )}
          </TableBody>
        </Table>

        <Grid
          display={"flex"}
          alignItems={"center"}
          justifyContent={"flex-start"}
          my={3}
        >
          <Box>
            <Stack spacing={2}>
              <Pagination
                count={count}
                page={page}
                onChange={handleTableChange}
                showFirstButton
                showLastButton
                color="green"
              />
            </Stack>
          </Box>
        </Grid>
        {openEditModal && (
          <EditCategoryDialog
            currentCategory={currentData}
            openModal={openEditModal}
            closeModal={() => setOpenEditModal(false)}
            type={type}
          />
        )}

        {openAddModal && (
          <AddCategoryDialog
            openModal={openAddModal}
            closeModal={() => setOpenAddModal(false)}
            type={type}
          />
        )}
      </TableContainer>
    </div>
  );
}

// Update PropTypes validation
Categories.propTypes = {
  type: PropTypes.oneOf(["income", "expenditure", "fee"]).isRequired,
  categories: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string,
      created_at: PropTypes.string,
      updated_at: PropTypes.string,
    })
  ).isRequired, // Ensure categories is validated as an array of objects
  isLoading: PropTypes.bool.isRequired,
  openAddModal: PropTypes.bool.isRequired,
  setOpenAddModal: PropTypes.func.isRequired,
};

export default Categories;
