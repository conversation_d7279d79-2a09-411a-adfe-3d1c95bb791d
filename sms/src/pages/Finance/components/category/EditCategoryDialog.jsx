import {
  useEditExpenseCategoryMutation,
  useEditFeeTypeMutation,
  useEditIncomeCategoryMutation,
} from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types";
import { useState } from "react";

function EditCategoryDialog({ openModal, closeModal, currentCategory, type }) {
  if (!type) {
    throw new Error(
      "Type must be specified. one of 'income', 'expenditure', fee-type "
    );
  }
  if (type != "income" && type != "expenditure" && type != "fee") {
    throw new Error("Type must one of 'income', 'expenditure', 'fee-type ");
  }
  EditCategoryDialog.tag = `Edit ${type} Category`;
  const [form, setForm] = useState({
    category: currentCategory?.name ?? "Category Name",
    desc: currentCategory?.description ?? "Sample Description",
  });
  const [updateIncomeCategory, { isLoading: isUpdatingIncomeCategory }] =
    useEditIncomeCategoryMutation("update income category");
  const [updateExpenseCategory, { isLoading: isUpdatingExpenseCategory }] =
    useEditExpenseCategoryMutation("update expense category");
  const [updateFeeType, { isLoading: isUpdatingFeeType }] =
    useEditFeeTypeMutation("update fee type");

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const actions = {
    income: updateIncomeCategory,
    expenditure: updateExpenseCategory,
    fee: updateFeeType,
  };
  const handleUpdateCategory = async (e) => {
    e.preventDefault();
    await RequestInterceptor.handleRequest(
      () =>
        actions[type]({
          id: currentCategory.id,
          body: { name: form.category, description: form.desc },
        }),
      {
        onSuccess: () => {
          window.location.reload();
        },
      },
      EditCategoryDialog.tag
    );
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">
          Editing Category: {currentCategory?.name}
        </h2>
        <form
          className="w-full flex flex-col gap-4 "
          onSubmit={handleUpdateCategory}
        >
          <div className="w-full flex gap-3 flex-col mb-4">
            <TextField
              type="text"
              onChange={onChange}
              value={form.category}
              name="category"
              id="category"
              label="Category"
              fullWidth
              required
            />

            <textarea
              id="desc"
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Category Description..."
              value={form.desc}
              name="desc"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          {isUpdatingIncomeCategory ||
          isUpdatingExpenseCategory ||
          isUpdatingFeeType ? (
            <Spinner />
          ) : (
            <Button
              title={"Update"}
              type={"primary"}
              onClick={handleUpdateCategory}
            />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add PropTypes validation
EditCategoryDialog.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  currentCategory: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string,
    description: PropTypes.string,
  }).isRequired,
  type: PropTypes.oneOf(["income", "expenditure", "fee"]).isRequired,
};

export default EditCategoryDialog;
