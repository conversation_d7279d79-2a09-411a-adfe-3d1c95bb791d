import { useGetSingleFeeQuery } from "@app";
import Logo from "@assets/images/logo.jpg";
import { Spinner } from "@components";
import { useDownload } from "@hooks/useDownload";
import { DateWizard, RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import Download from "@mui/icons-material/Download";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function ViewFeePayment() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { data, isLoading } = useGetSingleFeeQuery(id);
  const dowload = useDownload();

  const [isDownLoadingReceipt, setIsDowloadingReceipt] = useState(false);

  const handleDownloadReceipt = async (id) => {
    try {
      setIsDowloadingReceipt(true);
      await RequestInterceptor.downloadFile(
        dowload,
        `fees/installment/${id}`,
        `${data?.data?.student?.matricule}-Receipt(installment)`,
        "application/pdf"
      );
      setIsDowloadingReceipt(false);
    } catch (error) {
      console.log("Error Downloading date", error);
      setIsDowloadingReceipt(false);
    }
  };
  // const [feeTypeState, setFeeTypeState] = useState()
  // const [acedemicYearState, setAcademicYearState] = useState()
  // const {data:feeType, isLoading:isFeeTypeLoading} = useGetFeeTypeByIdQuery(feeTypeState)
  // const {data:academicYear, isLoading: isAcademicYearLoading} = useGetAcademicYearByIdQuery(acedemicYearState)

  // useEffect(()=>{
  //   switch(feeStatus){
  //     case "fulfilled": {
  //       setAcademicYearState(data)
  //       setFeeTypeState(data)
  //     }
  //   }
  // }, [feeStatus])
  return (
    <section className="w-full flex flex-col gap-6">
      <div className="w-full flex items-center gap-2">
        <div className="hover:bg-white p-2 rounded-full cursor-pointer">
          <ArrowBackIcon onClick={() => navigate(-1)} />
        </div>
        <p className="text-black font-semibold text-md">Transaction Details</p>
      </div>

      {isLoading ? (
        <Spinner />
      ) : (
        <div className="w-full flex flex-col items-center bg-white p-10 shadow-md">
          <div className="w-full flex flex-col items-start justify-between gap-4 mb-6">
            <div className="w-full flex items-start justify-between">
              <p className="font-semibold text-black text-sm w-1/5">
                EBENZER HIGHER INSTITUTE OF SCIENCE AND TECHNOLOGY (EHIST)
              </p>
              <img src={Logo} alt="" className="w-[50px]" />
              <div>
                <div className="flex gap-2">
                  <p className="font-semibold text-black text-sm">
                    BAMENDA - SONAC STREET
                  </p>
                </div>
                <div className="flex gap-2">
                  <p className="font-semibold text-black text-sm">
                    <EMAIL>
                  </p>
                </div>
                <div className="flex gap-2">
                  <p className="font-semibold text-black text-sm">
                    +237 678332504
                  </p>
                </div>
              </div>
            </div>
            <div className="w-full h-[2px] bg-grey_300" />
            <div className="w-full flex items-start justify-between">
              <div className="w-full flex flex-col items-start gap-2">
                <div className="w-full flex items-start gap-3">
                  <p className="text-black font-normal text-sm">School:</p>
                  <h2 className="text-black font-semibold text-md">
                    {data?.data?.student?.department?.school?.name ?? "N/A"}
                  </h2>
                </div>
                <div className="w-full flex items-start gap-3">
                  <p className="text-black font-normal text-sm">Department:</p>
                  <h2 className="text-black font-semibold text-md">
                    {data?.data?.student?.department?.name ?? "N/A"}
                  </h2>
                </div>
                <div className="w-full flex items-end gap-3">
                  <p className="text-black font-normal text-sm">Name:</p>
                  <h2 className="text-black font-semibold text-md">
                    {data?.data?.student?.user?.first_name}{" "}
                    {data?.data?.student?.user?.last_name}
                  </h2>
                </div>
                <div className="w-full flex items-end gap-3">
                  <p className="text-black font-normal text-sm">Matricule:</p>
                  <h2 className="text-black font-semibold text-md">
                    {data?.data?.student?.matricule ?? "N/A"}
                  </h2>
                </div>
              </div>
              <div className="w-full flex flex-col items-start gap-2">
                <div className="w-full flex items-center justify-end gap-3">
                  <p className="text-black font-normal text-sm">Level:</p>
                  <h2 className="text-black font-semibold text-md">
                    {" "}
                    {data?.data?.student?.level?.name ?? "N/A"}
                  </h2>
                </div>
                <div className="w-full flex items-center justify-end gap-3">
                  <p className="text-black font-normal text-sm">
                    Academic Year:
                  </p>
                  <h2 className="text-black font-semibold text-md">
                    {data?.data?.academic_year?.name?.replace("_", "/") ??
                      "N/A"}
                  </h2>
                </div>
                <div className="w-full flex items-start justify-end gap-3">
                  <p className="text-black font-normal text-sm">Semester:</p>
                  <h2 className="text-black font-semibold text-md">
                    {" "}
                    {data?.data?.student?.semester?.name ??
                      data?.data?.student?.semester ??
                      "N/A"}
                  </h2>
                </div>
              </div>
            </div>
          </div>

          <div className="shadow-md w-full">
            <div className="w-full flex items-center justify-center">
              <p className="text-green font-normal text-md">
                Transaction Details
              </p>
            </div>

            <div className="flex flex-col items-center justify-center w-full px-10 my-2">
              <TableContainer>
                <Table sx={{ maxWidth: "100%" }}>
                  <TableHead>
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Payment Motive</TableCell>
                      <TableCell>
                        {data?.data?.fee_type?.name ?? "N/A"}{" "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Amount (XAF)</TableCell>
                      <TableCell>{data?.data?.amount ?? "N/A"}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Account</TableCell>
                      <TableCell>
                        {data?.data?.account?.name ?? "N/A"}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Bank Reference</TableCell>
                      <TableCell>{data?.data?.bank_ref ?? "N/A"}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Payment Channel</TableCell>
                      <TableCell>
                        {data?.data?.payment_channel?.name ??
                          data?.data?.payment_channel ??
                          "N/A"}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Payment Date</TableCell>
                      <TableCell>{data?.data?.payment_date ?? "N/A"}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Academic Year</TableCell>
                      <TableCell>
                        {data?.data?.academic_year?.name?.replace("_", "/") ??
                          "N/A"}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </div>

          <h1 className="my-2 text-primary">Installments</h1>
          <div className="flex flex-col w-full my-2">
            {data?.data?.installments?.length > 0 ? (
              <TableContainer component={Paper}>
                <Table sx={{ maxWidth: "100%" }}>
                  <TableHead style={{ backgroundColor: "#1F7F1F" }}>
                    <TableRow style={{ color: "white" }}>
                      <TableCell>
                        <span className="text-white">S/N</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Amount (XAF)</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Account Type</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Fee Type</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Reference</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Academic Year</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Date</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-white">Action</span>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  {isLoading ? (
                    <Spinner />
                  ) : (
                    <TableBody>
                      {data?.data?.installments?.map((installment, idx) => (
                        <TableRow key={installment.id}>
                          <TableCell>{idx + 1}</TableCell>
                          <TableCell>{installment?.amount ?? "N/A"}</TableCell>
                          <TableCell>
                            {data?.data?.account?.name ?? "N/A"}
                          </TableCell>
                          <TableCell>
                            {data?.data?.fee_type?.name ?? "N/A"}{" "}
                          </TableCell>
                          <TableCell>
                            {installment?.reference ?? "N/A"}
                          </TableCell>
                          <TableCell>
                            {data?.data?.academic_year?.name?.replace(
                              "_",
                              "/"
                            ) ?? "N/A"}
                          </TableCell>
                          <TableCell>
                            {DateWizard.toLocaleDate(installment.created_at) ??
                              "N/A"}
                          </TableCell>
                          <TableCell>
                            {isDownLoadingReceipt ? (
                              <Spinner size="14px" />
                            ) : (
                              <Download
                                onClick={() =>
                                  handleDownloadReceipt(installment?.id)
                                }
                                className="text-primary"
                                sx={{ cursor: "pointer" }}
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  )}
                </Table>
              </TableContainer>
            ) : (
              <>No Installments</>
            )}
          </div>
        </div>
      )}
    </section>
  );
}

export default ViewFeePayment;
