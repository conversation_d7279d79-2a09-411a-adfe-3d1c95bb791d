import { useGetAllFeesQuery } from "@app";
import { <PERSON><PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { DateWizard } from "@lib/util";
import { DeleteOutline, EditOutlined, Visibility } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { WelcomeHeader } from "@pages/Finance/components";
import { AddAccount, EditAccount } from "@pages/Finance/components/accounts";
import { useMemo, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllFeePayment() {
  const { data, isLoading, isError } = useGetAllFeesQuery("all fees");

  const [openEditModal, setOpenEditModal] = useState(false);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [filter, setFilter] = useState("Name");
  const [count, setCount] = useState(1);
  const [page, setPage] = useState(1);

  const navigate = useNavigate();

  // Extract all installments into a new variable
  const allInstallments = useMemo(() => {
    if (!data?.data) return [];
    return data.data.flatMap((transaction) =>
      (transaction.installments || []).map((installment) => ({
        ...installment,
        student: transaction.student,
        fee_type: transaction.fee_type,
        account: transaction.account,
        academic_year: transaction.academic_year,
        parent_transaction_id: transaction.id,
      }))
    );
  }, [data?.data]);
  console.log(allInstallments);

  const handleChange = (event) => {
    setFilter(event.target.value);
  };

  const handleTableChange = (event, value) => {
    setPage(value);
  };

  const renderTransactionTable = (transactions) => (
    <TableBody>
      {transactions.map((transaction) => (
        <StyledTableRow key={transaction.id}>
          <StyledTableCell>
            {transaction?.student?.matricule ?? "N/A"}
          </StyledTableCell>
          <StyledTableCell>{transaction?.amount ?? "N/A"}</StyledTableCell>
          <StyledTableCell>
            <p
              className={
                transaction.status === "complete"
                  ? "text-green font-semibold"
                  : "text-orange font-semibold"
              }
            >
              {transaction.status ?? "N/A"}
            </p>
          </StyledTableCell>
          <StyledTableCell>
            {transaction?.fee_type?.name ?? "N/A"}
          </StyledTableCell>
          <StyledTableCell>
            {transaction?.account?.name ?? "N/A"}
          </StyledTableCell>
          <StyledTableCell>{transaction?.bank_ref ?? "N/A"}</StyledTableCell>
          <StyledTableCell>
            {transaction?.academic_year?.name?.replace("_", "/") ?? "N/A"}
          </StyledTableCell>
          <StyledTableCell>
            {DateWizard.toLocaleDate(transaction.created_at) ?? "N/A"}
          </StyledTableCell>
          <StyledTableCell>
            <div className="flex items-center gap-3">
              <Link to={`/finance/transactions/fee-payment/${transaction.id}`}>
                <Visibility color="primary" sx={{ cursor: "pointer" }} />
              </Link>
              <EditOutlined
                color="success"
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  navigate(
                    `/finance/transactions/fee-payment/${transaction.id}/edit`
                  )
                }
              />
              <DeleteOutline
                color="error"
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  alert(
                    "You can't DELETE this transaction. Contact the system administrator."
                  )
                }
              />
            </div>
          </StyledTableCell>
        </StyledTableRow>
      ))}
    </TableBody>
  );

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full">
        <div className="w-full flex items-start justify-between mb-4">
          <WelcomeHeader />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => navigate("/finance/transactions/fee-payment/add")}
          >
            Fee Payment
          </Button>
        </div>

        <div className="w-full flex flex-col items-start h-full">
          <p className="text-black text-lg font-semibold mb-4">
            All Fee Payment
          </p>
          <div className="w-full flex items-start justify-between mb-4">
            <div className="flex items-start gap-2">
              <Box>
                <TextField
                  fullWidth
                  size="small"
                  label="Search for account"
                  value={""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment>
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ minWidth: 120 }}>
                <FormControl fullWidth>
                  <TextField
                    select
                    id="filter"
                    size="small"
                    value={filter}
                    label="Filter"
                    name="filter"
                    onChange={handleChange}
                  >
                    <MenuItem value="Name">Name</MenuItem>
                    <MenuItem value="Email">Email</MenuItem>
                    <MenuItem value="Contact">Contact</MenuItem>
                  </TextField>
                </FormControl>
              </Box>
            </div>
            <div>
              <Stack direction={"row"} gap={2}>
                <Button
                  variant="outlined"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Export CSV
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Import CSV
                </Button>
              </Stack>
            </div>
          </div>

          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>Student Matricule</StyledTableCell>
                    <StyledTableCell>Amount (XAF)</StyledTableCell>
                    <StyledTableCell>Status</StyledTableCell>
                    <StyledTableCell>Fee Type</StyledTableCell>
                    <StyledTableCell>Account</StyledTableCell>
                    <StyledTableCell>Bank Reference</StyledTableCell>
                    <StyledTableCell>Academic Year</StyledTableCell>
                    <StyledTableCell>Date</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                {isLoading ? (
                  <Spinner />
                ) : isError ? (
                  <>Error loading data</>
                ) : (
                  renderTransactionTable(data?.data || [])
                )}
              </Table>
              <Grid
                display={"flex"}
                alignItems={"center"}
                justifyContent={"flex-start"}
                my={3}
              >
                <Box>
                  <Stack spacing={2}>
                    <Pagination
                      count={count}
                      page={page}
                      onChange={handleTableChange}
                      showFirstButton
                      showLastButton
                      color="green"
                    />
                  </Stack>
                </Box>
              </Grid>
            </TableContainer>
          </div>

          <h1>Installments</h1>
          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>Student Matricule</StyledTableCell>
                    <StyledTableCell>Amount (XAF)</StyledTableCell>
                    <StyledTableCell>Status</StyledTableCell>
                    <StyledTableCell>Fee Type</StyledTableCell>
                    <StyledTableCell>Account</StyledTableCell>
                    <StyledTableCell>Bank Reference</StyledTableCell>
                    <StyledTableCell>Academic Year</StyledTableCell>
                    <StyledTableCell>Date</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                {isLoading ? (
                  <Spinner />
                ) : isError ? (
                  <>Error loading data</>
                ) : (
                  renderTransactionTable(allInstallments)
                )}
              </Table>
              <Grid
                display={"flex"}
                alignItems={"center"}
                justifyContent={"flex-start"}
                my={3}
              >
                <Box>
                  <Stack spacing={2}>
                    <Pagination
                      count={count}
                      page={page}
                      onChange={handleTableChange}
                      showFirstButton
                      showLastButton
                      color="green"
                    />
                  </Stack>
                </Box>
              </Grid>
            </TableContainer>
          </div>
        </div>
      </div>
      {openEditModal && (
        <EditAccount
          openModal={openEditModal}
          closeModal={() => setOpenEditModal(false)}
        />
      )}
      {openAddModal && (
        <AddAccount
          openModal={openAddModal}
          closeModal={() => setOpenAddModal(false)}
        />
      )}
    </ThemeProvider>
  );
}

export default AllFeePayment;
