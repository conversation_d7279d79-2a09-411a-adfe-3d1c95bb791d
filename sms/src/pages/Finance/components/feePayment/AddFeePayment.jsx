import {
  useCreatFeesMutation,
  useGetAllAcademicYearsQuery,
  useGetAllAccountsQuery,
  useGetAllFeeTypesQuery,
  useGetAllStudentsQuery,
} from "@app";
import { Spinner } from "@components";
import { RequestInterceptor } from "@lib/util";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Button, MenuItem, Stack, TextField } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

function AddFeePayment() {
  AddFeePayment.tag = "Add Fee Payment";
  const navigate = useNavigate();

  const { data: students, isLoading: isStudentLoading } =
    useGetAllStudentsQuery("allStudent");
  const { data: academicYears, isLoading: isAcademicYearsLoading } =
    useGetAllAcademicYearsQuery("allAcademicYears");
  const { data: accounts, isLoading: isAccountsLoading } =
    useGetAllAccountsQuery("allAccounts");
  const { data: feeTypes, isLoading: isFeeTypesLoading } =
    useGetAllFeeTypesQuery("allFeeTypes");
  const [createFees, { isLoading: isCreatingFee }] =
    useCreatFeesMutation("create fees");

  const [form, setForm] = useState({
    student_id: "",
    academic_year_id: "",
    account_id: "",
    amount: "",
    status: "",
    bank_ref: "",
    payment_channel: "",
    date: "",
    fee_type_id: "",
    is_installment: false,
  });
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(form);
    await RequestInterceptor.handleRequest(
      () =>
        createFees({
          ...form,
          payment_date: form.date,
          is_installment: form.is_installment ? 1 : 0,
        }),
      {
        onSuccess: () => {
          navigate("/finance/transactions", { replace: true });
        },
      },
      AddFeePayment.tag
    );
  };

  return (
    <div className="w-full flex flex-col items-start  md:px-16 md:py-10 py-5 px-8 justify-between">
      <div className="w-full flex flex-col bg-white p-10">
        <div className="w-full flex items-start  justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="hover:bg-grey p-2 rounded-full">
              <ArrowBackIcon onClick={() => navigate(-1)} />
            </div>
            <p className="text-black font-semibold text-lg">Add Fee Payment</p>
          </div>
          <div>
            <Stack direction={"row"} gap={2}>
              <Button
                variant="contained"
                color="success"
                style={{ textTransform: "capitalize" }}
              >
                Import csv
              </Button>
            </Stack>
          </div>
        </div>

        <div className=" flex flex-col w-full items-center justify-between">
          <form className=" w-full " onSubmit={handleSubmit}>
            <div className="w-full flex md:flex-row flex-col gap-3 mb-4">
              <TextField
                onChange={onChange}
                name="student_id"
                id="student_id"
                label="Student Name"
                value={form.student_id}
                fullWidth
                required
                select
              >
                {isStudentLoading ? (
                  <Spinner size="15px" />
                ) : (
                  students?.data?.map((student, idx) => (
                    <MenuItem
                      key={student.student_id}
                      value={student?.student_id}
                    >
                      {idx + 1}. {student?.userInfo?.first_name}{" "}
                      {student?.userInfo?.last_name} ({student?.matricule})
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <div className="w-full flex md:flex-row flex-col gap-3 mb-4">
              <TextField
                type="currency"
                onChange={onChange}
                id="amount"
                label="Amount"
                value={form.amount}
                fullWidth
                name="amount"
                required
              />
              <TextField
                value={form.academic_year_id}
                onChange={onChange}
                name="academic_year_id"
                id="academic_year_id"
                label="Academic Year"
                fullWidth
                required
                select
              >
                {isAcademicYearsLoading ? (
                  <Spinner size="15px" />
                ) : (
                  academicYears?.data?.map((year, idx) => (
                    <MenuItem key={year.id} value={year.id}>
                      {idx + 1}. {year?.name?.replace("_", "/")}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <div className="w-full flex md:flex-row flex-col gap-3 mb-4">
              <TextField
                name="account_id"
                type="text"
                onChange={onChange}
                value={form.account_id}
                id="account_id"
                label="Account Name"
                fullWidth
                required
                select
              >
                {isAccountsLoading ? (
                  <Spinner size="15px" />
                ) : (
                  accounts?.data?.map((account, idx) => (
                    <MenuItem key={account.id} value={account.id}>
                      {idx + 1}. {account?.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                name="fee_type_id"
                type="text"
                onChange={onChange}
                value={form.fee_type_id}
                id="fee_type_id"
                label="Fee Type"
                fullWidth
                required
                select
              >
                {isFeeTypesLoading ? (
                  <Spinner size="15px" />
                ) : (
                  feeTypes?.data?.map((feeType, idx) => (
                    <MenuItem key={feeType.id} value={feeType.id}>
                      {idx + 1}. {feeType?.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <div className="mb-4 w-full flex md:flex-row flex-col gap-3 ">
              <TextField
                onChange={onChange}
                type="text"
                label="Bank Reference"
                name="bank_ref"
                id="bank_ref"
                value={form.bank_ref}
                fullWidth
                required
              />
              <TextField
                onChange={onChange}
                type="text"
                label="Payment Channel"
                name="payment_channel"
                id="payment_channel"
                value={form.payment_channel}
                fullWidth
                required
              />
            </div>
            <div className="w-full flex md:flex-row flex-col gap-3 mb-4">
              <TextField
                type="date"
                onChange={onChange}
                value={form.date}
                label="Date"
                name="date"
                id="date"
                fullWidth
                required
              />
              <TextField
                type="text"
                label="Status"
                className="w-full"
                onChange={onChange}
                name="status"
                id="status"
                value={form.status}
                select
              >
                {isFeeTypesLoading ? (
                  <Spinner size="15px" />
                ) : (
                  ["complete", "incomplete"].map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </div>
            <div className="w-full flex md:flex-row flex-col gap-3 mb-4">
              <div className="flex justify-between gap-2 items-center">
                <label htmlFor="is_installment"> Is Installment?</label>
                <input
                  type="checkbox"
                  label="Is installment?"
                  className="text-green bg-green w-8 h-6 border-2 border-solid "
                  name="is_installment"
                  id="is_installment"
                  onChange={(e) => {
                    setForm({
                      ...form,
                      is_installment: e.target.checked ? 1 : 0,
                    });
                  }}
                  checked={form.is_installment}
                  value={form.is_installment}
                />
              </div>
            </div>

            <div className="w-full flex justify-center">
              {isCreatingFee ? (
                <Spinner />
              ) : (
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                  type="submit"
                >
                  Add Fee Payment
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
export default AddFeePayment;
