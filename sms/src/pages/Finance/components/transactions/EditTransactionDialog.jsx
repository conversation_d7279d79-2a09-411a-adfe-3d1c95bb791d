import { But<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { MenuItem, Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function EditTransactionDialog({ openModal, closeModal }) {
  const [form, setForm] = useState({
    accountName: "Nfon Andrew",
    accountNumber: "*********",
    transactionType: "Income",
    date: "12-8-2024",
    channel: "mtn-MoMo",
  });

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Edit Transaction</h2>
        <form className="w-full flex flex-col gap-4 ">
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.accountName}
              label="Account Name"
              name="accountName"
              id="accountName"
              fullWidth
              required
            />
            <TextField
              type="text"
              onChange={onChange}
              value={form.accountNumber}
              name="accountNumber"
              id="accountNumber"
              label="Account Number"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="date"
              onChange={onChange}
              value={form.transactionType}
              name="transactionType"
              id="transactionType"
              label="Transaction Type"
              fullWidth
              required
              select
            >
              <MenuItem value="income">Income</MenuItem>
              <MenuItem value="expenditure">Expenditure</MenuItem>
            </TextField>
            <TextField
              type="date"
              onChange={onChange}
              value={form.date}
              label="Date"
              name="date"
              id="date"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.channel}
              name="channel"
              id="channel"
              label="Channel"
              fullWidth
              required
            />
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          <Button title={"Update"} type={"primary"} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
EditTransactionDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default EditTransactionDialog;
