import { useGetAllStudentsQuery, useGetAllTransactionsQuery } from "@app";
import { Spin<PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { DeleteOutline, EditOutlined } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { WelcomeHeader } from "@pages/Finance/components";
import {
  AddTransactionDialog,
  EditTransactionDialog,
} from "@pages/Finance/components/transactions";
import { useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllTransactions() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data, isLoading, status } = useGetAllStudentsQuery("allStudent");

  const [openEditModal, setOpenEditModal] = useState(false);
  const [openAddModal, setOpenAddModal] = useState(false);
  // const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const [filter, setFilter] = useState("Name");
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(1); // Set an initial count
  const [page, setPage] = useState(1);

  const handleChange = (event) => {
    setFilter(event.target.value);
  };

  const handleTableChange = (event, value) => {
    setPage(value);
    // You can add pagination logic here if needed
  };

  const { data: transactions, isLoading: isTransactionLoading } =
    useGetAllTransactionsQuery("transactions");

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full">
        <div className="w-full flex items-start justify-between mb-4">
          <WelcomeHeader />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => setOpenAddModal(true)}
          >
            Transaction
          </Button>
        </div>

        <div className="w-full flex flex-col items-start h-full">
          <p className="text-black text-lg font-semibold mb-4">
            All Transactions
          </p>
          <div className="w-full flex items-start justify-between mb-4">
            <div className="flex items-start gap-2">
              <Box>
                <TextField
                  fullWidth
                  size="small"
                  label="Search for account"
                  value={""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment>
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ minWidth: 120 }}>
                <FormControl fullWidth>
                  <TextField
                    select
                    id="filter"
                    size="small"
                    value={filter}
                    label="Filter"
                    name="filter"
                    onChange={handleChange}
                  >
                    <MenuItem value="Name">Id</MenuItem>
                    <MenuItem value="Email">Expenditure</MenuItem>
                    <MenuItem value="Contact">Income </MenuItem>
                  </TextField>
                </FormControl>
              </Box>
            </div>
            <div>
              <Stack direction={"row"} gap={2}>
                <Button
                  variant="outlined"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Export CSV
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  style={{ textTransform: "capitalize" }}
                >
                  Import CSV
                </Button>
              </Stack>
            </div>
          </div>

          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Transaction Reference</StyledTableCell>
                    <StyledTableCell>Transaction ID</StyledTableCell>
                    <StyledTableCell>Amount (XAF)</StyledTableCell>
                    <StyledTableCell>Transaction type</StyledTableCell>
                    <StyledTableCell>Date</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isTransactionLoading ? (
                    <Spinner />
                  ) : (
                    transactions?.data?.data?.map((transaction, idx) => (
                      <StyledTableRow key={transaction.id}>
                        <StyledTableCell>{idx + 1}</StyledTableCell>
                        <StyledTableCell>
                          {transaction.reference ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {transaction.id ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <span
                            className={
                              transaction.transaction_type == "Expense"
                                ? "text-red"
                                : "text-green"
                            }
                          >
                            {transaction.amount ?? "N/A"}
                          </span>
                        </StyledTableCell>
                        <StyledTableCell>
                          <span
                            className={
                              transaction.transaction_type == "Expense"
                                ? "text-red"
                                : "text-green"
                            }
                          >
                            {transaction.transaction_type ?? "N/A"}
                          </span>
                        </StyledTableCell>
                        <StyledTableCell>
                          {transaction.transaction_date ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div className="flex items-center gap-3">
                            <EditOutlined
                              color="success"
                              sx={{ cursor: "pointer" }}
                              onClick={() => setOpenEditModal(true)}
                            />

                            <DeleteOutline
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() =>
                                alert(
                                  "You can't delete this student. Contact the system administrator."
                                )
                              }
                            />
                          </div>
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              <Grid
                display={"flex"}
                alignItems={"center"}
                justifyContent={"flex-start"}
                my={3}
              >
                <Box>
                  <Stack spacing={2}>
                    <Pagination
                      count={count}
                      page={page}
                      onChange={handleTableChange}
                      showFirstButton
                      showLastButton
                      color="green"
                    />
                  </Stack>
                </Box>
              </Grid>
              {openEditModal && (
                <EditTransactionDialog
                  openModal={openEditModal}
                  closeModal={() => setOpenEditModal(false)}
                />
              )}

              {openAddModal && (
                <AddTransactionDialog
                  openModal={openAddModal}
                  closeModal={() => setOpenAddModal(false)}
                />
              )}
            </TableContainer>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllTransactions;
