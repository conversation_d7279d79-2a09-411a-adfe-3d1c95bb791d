import LogOutModal from "@components/LogOutModal";
import {
  AccountCircleOutlined,
  BarChartOutlined,
  GridViewRounded,
  KeyboardArrowDown,
  KeyboardArrowUp,
  LogoutOutlined,
  SwapHorizOutlined,
} from "@mui/icons-material";
import CategoryOutlinedIcon from "@mui/icons-material/CategoryOutlined";
import MenuIcon from "@mui/icons-material/Menu";
import {
  AppBar,
  Box,
  Collapse,
  CssBaseline,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
} from "@mui/material";
import Head from "@pages/admin/components/partials/Header";
import PropTypes from "prop-types";
import * as React from "react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const drawerWidth = 240;

export default function DashboardLayout({ children }) {
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleDrawerClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerToggle = () => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };
  const role = "finance";
  const { pathname } = useLocation();
  const [isCollapse, setIsCollapse] = useState({});

  const handleCollapse = (index) => {
    setIsCollapse((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }));
  };

  const [openLogoutModal, setOpenLogoutModal] = useState(false);
  const handleLogout = () => {
    setOpenLogoutModal(true);
  };

  const user = {
    role: "finance",
  };

  const financePaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/accounts`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Transactions",
      link: `/${user.role}/transactions`,
      icon: <SwapHorizOutlined />,
      children: [
        {
          tabName: "Fee Payment",
          link: `/${user.role}/transactions/fee-payment`,
        },
        { tabName: "Income", link: `/${user.role}/transactions/income` },
        {
          tabName: "Expenditure",
          link: `/${user.role}/transactions/expenditure`,
        },
      ],
    },
    {
      tabName: "Category",
      link: `/${user.role}/category`,
      icon: <CategoryOutlinedIcon />,
      children: [
        { tabName: "Income", link: `/${user.role}/category/income` },
        {
          tabName: "Expenditure",
          link: `/${user.role}/category/expenditure`,
        },
        {
          tabName: "Fee Types",
          link: `/${user.role}/category/fee-types`,
        },
      ],
    },
    {
      tabName: "Report",
      link: `/finance`,
      icon: <BarChartOutlined />,
    },
    {
      tabName: "Logout",
      modal: true, // open a modal when clicked to confirm logout
      icon: <LogoutOutlined />,
    },
  ];

  const sideBarItems = financePaths;

  const drawer = (
    <div>
      {/* <Toolbar /> */}
      <div>
        <Link to={`/${role}`} className="w-full flex  items-center gap-3 p-5">
          <img src={logo} style={{ height: "44px", width: "40px" }} />
          <p className="text-green font-semibold text-md">EHIST</p>
        </Link>
      </div>
      <Divider />
      <List>
        {sideBarItems.map((item, index) => (
          <div
            key={item.tabName}
            onClick={item.tabName === "Logout" ? handleLogout : null}
          >
            {item.children ? (
              <>
                <ListItem
                  onClick={() => handleCollapse(index)}
                  className={`w-full hover:bg-[#F0F1F3] hover:border-l-4 hover:border-l-[#186318] hover:text-[#186318] ${
                    pathname === item.link
                      ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                      : ""
                  }`}
                >
                  <ListItemIcon className="hover:text-[#186318]">
                    {item.icon}
                  </ListItemIcon>
                  <div className="w-full flex gap-4">
                    <ListItemText className="text-[11px]">
                      {item.tabName}
                    </ListItemText>
                    {isCollapse[index] ? (
                      <KeyboardArrowUp />
                    ) : (
                      <KeyboardArrowDown />
                    )}
                  </div>
                </ListItem>
                <List>
                  <Collapse in={isCollapse[index]} timeout="auto" unmountOnExit>
                    {item.children?.map((childItem) => (
                      <Link key={childItem.tabName} to={childItem.link}>
                        <ListItem
                          className={`hover:bg-[#F0F1F3] hover:border-l-4 hover:border-l-[#186318] hover:text-[#186318] w-[90%] mx-1 ${
                            pathname === childItem.link
                              ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                              : ""
                          }`}
                        >
                          <ListItemText className="text-[11px] pl-8">
                            {childItem.tabName}
                          </ListItemText>
                        </ListItem>
                      </Link>
                    ))}
                  </Collapse>
                </List>
              </>
            ) : (
              <Link to={item.link}>
                <ListItem
                  className={`hover:bg-[#F0F1F3] hover:border-l-4 hover:border-l-[#186318] hover:text-[#186318] w-[90%] mx-1 ${
                    pathname === item.link
                      ? "border-l-4 border-l-[#186318] text-[#186318] bg-[#F0F1F3]"
                      : ""
                  }`}
                >
                  <ListItemIcon className="hover:text-[#186318]">
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText className="text-[11px]">
                    {item.tabName}
                  </ListItemText>
                </ListItem>
              </Link>
            )}
          </div>
        ))}
      </List>
      <Divider />
    </div>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: "white",
          color: "black",
        }}
      >
        <Toolbar>
          <IconButton
            color="green"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: "none" } }}
          >
            <MenuIcon />
          </IconButton>
          <Head className="text-black" />
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        {/* The implementation can be swapped with js to avoid SEO duplication of links. */}
        <Drawer
          // container={container}
          variant="temporary"
          open={mobileOpen}
          onTransitionEnd={handleDrawerTransitionEnd}
          onClose={handleDrawerClose}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", sm: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
        }}
      >
        <Toolbar />
        <section className="w-full flex flex-col items-center justify-center">
          {children}
        </section>
      </Box>
      {openLogoutModal && (
        <LogOutModal
          openModal={openLogoutModal}
          closeModal={() => setOpenLogoutModal(false)}
        />
      )}
    </Box>
  );
}

DashboardLayout.propTypes = {
  children: PropTypes.node.isRequired,
};
