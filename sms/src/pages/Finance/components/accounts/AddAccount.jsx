import { useCreateAccountMutation } from "@app";
import { <PERSON><PERSON>, Spin<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function AddAccount({ openModal, closeModal }) {
  const [form, setForm] = useState({
    name: "",
    description: "",
  });

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const [createAccount, { isLoading: isCreatingAccount }] =
    useCreateAccountMutation("create finance account");
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { message } = await createAccount(form).unwrap();
      alert(message);
      closeModal();
      window.location.reload();
    } catch (error) {
      console.error("An error occurred while creating the account", error);
      alert(
        error?.data?.message ?? "An error occurred while creating the account"
      );
    }
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Add Account</h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          Enter account details
        </p>
        <form className="w-full flex flex-col gap-4" onSubmit={handleSubmit}>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.name}
              label="Account Name"
              name="name"
              id="name"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex flex-col gap-1">
            <p>Description (optional)</p>
            <textarea
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Account description (optional)..."
              value={form.description}
              name="description"
              id="description"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          {isCreatingAccount ? (
            <Spinner />
          ) : (
            <Button title={"Create"} type={"primary"} onClick={handleSubmit} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
AddAccount.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal as a required boolean
  closeModal: PropTypes.func.isRequired, // Validate closeModal as a required function
};

export default AddAccount;
