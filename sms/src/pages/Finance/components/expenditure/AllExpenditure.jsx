import { useGetAllExpendituresQuery } from "@app";
import { <PERSON><PERSON>, StyledTableCell, StyledTableRow } from "@components";
import { DateWizard } from "@lib/util";
import { DeleteOutline, EditOutlined } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { WelcomeHeader } from "@pages/Finance/components";
import {
  AddExpenditureDialog,
  EditExpenditureDialog,
} from "@pages/Finance/components/expenditure";
import { useState } from "react";

const theme = createTheme({
  palette: {
    green: {
      main: "#1F7F1F",
    },
  },
});

function AllExpenditure() {
  const { data, isLoading } = useGetAllExpendituresQuery("all expenditures");

  const [openEditModal, setOpenEditModal] = useState(false);
  const [openAddModal, setOpenAddModal] = useState(false);
  // const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const [filter, setFilter] = useState("Name");
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [count, setCount] = useState(1); // Set an initial count
  const [page, setPage] = useState(1);

  const handleChange = (event) => {
    setFilter(event.target.value);
  };

  const handleTableChange = (event, value) => {
    setPage(value);
    // You can add pagination logic here if needed
  };

  return (
    <ThemeProvider theme={theme}>
      <div className="w-full">
        <div className="w-full flex items-start justify-between mb-4">
          <WelcomeHeader />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="success"
            style={{ textTransform: "capitalize" }}
            onClick={() => setOpenAddModal(true)}
          >
            Expenditure
          </Button>
        </div>

        <div className="w-full flex flex-col items-start h-full">
          <p className="text-black text-lg font-semibold mb-4">
            All Expenditures
          </p>
          <div className="w-full flex items-start justify-between mb-4">
            <div className="flex items-start gap-2">
              <Box>
                <TextField
                  fullWidth
                  size="small"
                  label="Search for account"
                  value={""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment>
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ minWidth: 120 }}>
                <FormControl fullWidth>
                  <TextField
                    select
                    id="filter"
                    size="small"
                    value={filter}
                    label="Filter"
                    name="filter"
                    onChange={handleChange}
                  >
                    <MenuItem value="Name">Name</MenuItem>
                    <MenuItem value="Email">Category</MenuItem>
                    <MenuItem value="Contact">Amount</MenuItem>
                  </TextField>
                </FormControl>
              </Box>
            </div>
          </div>

          <div className="flex flex-col w-full my-2">
            <TableContainer component={Paper}>
              <Table sx={{ maxWidth: "100%" }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>S/N</StyledTableCell>
                    <StyledTableCell>Category</StyledTableCell>
                    <StyledTableCell>Account</StyledTableCell>
                    <StyledTableCell>Amount (XAF) </StyledTableCell>
                    <StyledTableCell>To</StyledTableCell>
                    <StyledTableCell>Description</StyledTableCell>
                    <StyledTableCell>Created At</StyledTableCell>
                    <StyledTableCell>Updated At</StyledTableCell>
                    <StyledTableCell>Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    <Spinner />
                  ) : (
                    data?.data?.map((expense, idx) => (
                      <StyledTableRow key={expense.id}>
                        <StyledTableCell>{idx + 1}</StyledTableCell>
                        <StyledTableCell>
                          {expense.category?.name ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {expense.account?.name ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <p className="text-red">{expense.amount ?? "N/A"}</p>
                        </StyledTableCell>
                        <StyledTableCell>{expense.to ?? "N/A"}</StyledTableCell>
                        <StyledTableCell>
                          {expense.description ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {DateWizard.toLocaleDate(expense.created_at) ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {DateWizard.toLocaleDate(expense.updated_at) ?? "N/A"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div className="flex items-center gap-3">
                            <EditOutlined
                              color="success"
                              sx={{ cursor: "pointer" }}
                              onClick={() => alert("You can't edit this item")}
                            />

                            <DeleteOutline
                              color="error"
                              sx={{ cursor: "pointer" }}
                              onClick={() =>
                                alert("You can't delete this item")
                              }
                            />
                          </div>
                        </StyledTableCell>
                      </StyledTableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              <Grid
                display={"flex"}
                alignItems={"center"}
                justifyContent={"flex-start"}
                my={3}
              >
                <Box>
                  <Stack spacing={2}>
                    <Pagination
                      count={count}
                      page={page}
                      onChange={handleTableChange}
                      showFirstButton
                      showLastButton
                      color="green"
                    />
                  </Stack>
                </Box>
              </Grid>
              {openEditModal && (
                <EditExpenditureDialog
                  openModal={openEditModal}
                  closeModal={() => setOpenEditModal(false)}
                />
              )}

              {openAddModal && (
                <AddExpenditureDialog
                  openModal={openAddModal}
                  closeModal={() => setOpenAddModal(false)}
                />
              )}
            </TableContainer>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default AllExpenditure;
