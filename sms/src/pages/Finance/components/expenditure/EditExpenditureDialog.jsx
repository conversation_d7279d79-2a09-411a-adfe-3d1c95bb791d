import { Button } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { MenuItem, Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function EditExpenditureDialog({ openModal, closeModal }) {
  const [form, setForm] = useState({
    expenseCategory: "Payroll",
    to: "Mah Marieta",
    amount: "30,000xaf",
    desc: "Amount for respective purpose",
  });
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Add Expenditure</h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          Enter expense details
        </p>
        <form className="w-full flex flex-col gap-4 ">
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.expenseCategory}
              name="expenseCategory"
              id="expenseCategory"
              label="Expense Category"
              fullWidth
              required
              select
            >
              <MenuItem>Payroll</MenuItem>
            </TextField>
            <TextField
              type="text"
              onChange={onChange}
              value={form.to}
              label="Recipient"
              name="to"
              id="to"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex gap-3 flex-col mb-4">
            <TextField
              type="text"
              onChange={onChange}
              value={form.amount}
              name="amount"
              id="amount"
              label="Amount"
              fullWidth
              required
            />

            <textarea
              id="desc"
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Expense description..."
              value={form.desc}
              name="desc"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          <Button title={"Save"} type={"primary"} />
        </Stack>
      </div>
    </ModalWrapper>
  );
}

// Add prop types validation
EditExpenditureDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default EditExpenditureDialog;
