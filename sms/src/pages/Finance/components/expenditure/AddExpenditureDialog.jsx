import {
  useCreateExpenditureMutation,
  useGetAllAccountsQuery,
  useGetAllExpenseCategoriesQuery,
} from "@app";
import { <PERSON><PERSON>, Spinner } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { RequestInterceptor } from "@lib/util";
import { MenuItem, Stack, TextField } from "@mui/material";
import PropTypes from "prop-types"; // Add this import
import { useState } from "react";

function AddExpenditureDialog({ openModal, closeModal }) {
  AddExpenditureDialog.tag = "Add Expenditure";
  const {
    data: expenseCategories,
    isLoading: isExpenseCategoriesLoading,
    isError,
  } = useGetAllExpenseCategoriesQuery("get expense Categories");
  const { data: accounts, isLoading: isAccountsLoading } =
    useGetAllAccountsQuery("getAllAccounts");
  const [form, setForm] = useState({
    account_id: "",
    amount: "",
    expense_category_id: "",
    expense_date: "",
    to: "",
    student_id: "",
    description: "",
  });
  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });
  const [addExpenditure, { isLoading: isAddingExpenditure }] =
    useCreateExpenditureMutation("createExpenditure");
  const handleSubmit = async (e) => {
    e.preventDefault();
    await RequestInterceptor.handleRequest(
      () => addExpenditure(form),
      {
        onSuccess: () => {
          window.location.reload();
        },
      },
      AddExpenditureDialog.tag
    );
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="w-full flex flex-col justify-between items-center gap-y-2">
        <h2 className="text-black font-semibold text-lg">Add Expenditure</h2>
        <p className="text-grey_500 font-medium text-sm text-center">
          Enter expense details
        </p>
        <form className="w-full flex flex-col gap-4" onSubmit={handleSubmit}>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <>
              <TextField
                type="text"
                onChange={onChange}
                value={form.expense_category_id}
                name="expense_category_id"
                id="expense_category_id"
                label="Category"
                fullWidth
                select
                required
              >
                {isExpenseCategoriesLoading ? (
                  <Spinner size="14px" />
                ) : isError ? (
                  <code>Error fetching Income Categories</code>
                ) : (
                  expenseCategories?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
              <TextField
                type="text"
                onChange={onChange}
                value={form.account_id}
                name="account_id"
                id="account_id"
                label="Account Name"
                fullWidth
                select
                required
              >
                {isAccountsLoading ? (
                  <Spinner size="14px" />
                ) : (
                  accounts?.data?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.name}
                    </MenuItem>
                  ))
                )}
              </TextField>
            </>
          </div>
          <div className="w-full flex gap-3 md:flex-row flex-col">
            <TextField
              type="text"
              onChange={onChange}
              value={form.amount}
              label="Amount"
              name="amount"
              id="amount"
              fullWidth
              required
            />
            <TextField
              type="date"
              onChange={onChange}
              value={form.expense_date}
              label="Date"
              name="expense_date"
              id="expense_date"
              fullWidth
              required
            />
          </div>
          <div className="w-full flex gap-3 flex-col mb-4">
            <TextField
              type="text"
              onChange={onChange}
              value={form.to}
              name="to"
              id="to"
              label="To"
              fullWidth
              required
            />
          </div>
          <div className="w-full">
            <textarea
              id="description"
              rows="4"
              className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-sm border border-gray-300 focus:outline-green focus:border-green "
              placeholder="Expense description..."
              value={form.description}
              name="description"
              onChange={onChange}
            ></textarea>
          </div>
        </form>

        <Stack direction={"row"} gap={2}>
          <Button title={"cancel"} type={"danger"} onClick={closeModal} />
          {isAddingExpenditure ? (
            <Spinner />
          ) : (
            <Button title={"Create"} type={"primary"} onClick={handleSubmit} />
          )}
        </Stack>
      </div>
    </ModalWrapper>
  );
}
// Add PropTypes validation
AddExpenditureDialog.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
};

export default AddExpenditureDialog;
