import {
  LecturerDashboard,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>F<PERSON>gotPassword,
  LecturerR<PERSON>tPassword,
  MyCourses,
  CourseDetails,
  CreateAssignment,
  ManageAssignments,
  GradeSubmissions,
  CreateAssessment,
  ManageAssessments,
  StudentAnalytics,
  CourseMaterialsManagement,
  LecturerProfile,
} from "@pages/lecturer/components";

export const lecturerRoutes = [
  {
    element: <LecturerDashboard />,
    path: "/lecturer",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <MyCourses />,
    path: "/lecturer/courses",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <CourseDetails />,
    path: "/lecturer/courses/:courseId",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <CourseMaterialsManagement />,
    path: "/lecturer/courses/:courseId/materials",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <ManageAssignments />,
    path: "/lecturer/assignments",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <CreateAssignment />,
    path: "/lecturer/assignments/create",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <GradeSubmissions />,
    path: "/lecturer/assignments/:assignmentId/submissions",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <ManageAssessments />,
    path: "/lecturer/assessments",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <CreateAssessment />,
    path: "/lecturer/assessments/create",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <StudentAnalytics />,
    path: "/lecturer/analytics",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <LecturerProfile />,
    path: "/lecturer/profile",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
];

export const lecturerAuthRoutes = [
  {
    element: <LecturerLogin />,
    path: "/lecturer/login",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <LecturerForgotPassword />,
    path: "/lecturer/forgot-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },

  {
    element: <LecturerResetPassword />,
    path: "/lecturer/reset-password",
    errorElement: <>This path doesn&apos;t exist yet.</>,
  },
];
