import { Box } from "@mui/material";
import LecturerSidebar from "./LecturerSidebar";
import PropTypes from "prop-types";

function LecturerLayout({ children }) {
  return (
    <Box sx={{ display: "flex", minHeight: "100vh" }}>
      <LecturerSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          backgroundColor: "#f8fafc",
          minHeight: "100vh",
          p: 3,
        }}
      >
        {children}
      </Box>
    </Box>
  );
}

LecturerLayout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default LecturerLayout;
