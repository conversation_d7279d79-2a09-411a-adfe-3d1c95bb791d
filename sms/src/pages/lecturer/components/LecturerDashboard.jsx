import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Button,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Paper,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUpOutlined,
  GroupOutlined,
  AssignmentOutlined,
  AssessmentOutlined,
  BookOutlined,
  NotificationsOutlined,
  CalendarTodayOutlined,
  MoreVertOutlined,
  AddOutlined,
  VisibilityOutlined,
  SchoolOutlined,
  TimelineOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { useGetLecturerDashboardStatsQuery, useGetRecentActivitiesQuery } from '@app';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const LecturerDashboard = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);

  // Use RTK Query hooks
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
  } = useGetLecturerDashboardStatsQuery();

  const {
    data: activitiesData,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useGetRecentActivitiesQuery();

  // Extract data or use fallback
  const stats = statsData?.data || {
    total_courses: 0,
    total_students: 0,
    total_assignments: 0,
    pending_grading: 0,
  };

  const recentActivities = activitiesData?.data || [];
  const loading = statsLoading || activitiesLoading;
  const error = statsError || activitiesError;

  // Mock data for fallback
  const mockData = {
        stats: {
          totalCourses: 4,
          totalStudents: 143,
          activeAssignments: 8,
          pendingGrading: 12,
        },
        recentActivities: [
          {
            id: 1,
            type: 'submission',
            title: 'New submission for Python Basics Assignment',
            student: 'John Doe',
            course: 'CS101',
            time: '2 hours ago',
            avatar: 'JD',
          },
          {
            id: 2,
            type: 'question',
            title: 'Student question about Database Design',
            student: 'Jane Smith',
            course: 'DB201',
            time: '4 hours ago',
            avatar: 'JS',
          },
          {
            id: 3,
            type: 'completion',
            title: 'Assessment completed by 25 students',
            course: 'DS301',
            time: '1 day ago',
            avatar: 'DS',
          },
        ],
        upcomingDeadlines: [
          {
            id: 1,
            title: 'Python Programming Assignment',
            course: 'CS101',
            dueDate: '2024-02-15',
            submissions: 35,
            total: 45,
          },
          {
            id: 2,
            title: 'Database Design Quiz',
            course: 'DB201',
            dueDate: '2024-02-18',
            submissions: 20,
            total: 32,
          },
        ],
        coursePerformance: [
          { course: 'CS101', performance: 85, students: 45, color: '#3b82f6' },
          { course: 'DB201', performance: 78, students: 32, color: '#10b981' },
          { course: 'DS301', performance: 92, students: 28, color: '#f59e0b' },
          { course: 'WD401', performance: 73, students: 38, color: '#8b5cf6' },
        ],
      };

  const StatCard = ({ title, value, icon, color, trend, onClick }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card
        sx={{
          height: '100%',
          cursor: onClick ? 'pointer' : 'default',
          transition: 'all 0.3s ease',
          border: '1px solid #f1f5f9',
          '&:hover': {
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            transform: 'translateY(-2px)',
          },
        }}
        onClick={onClick}
      >
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {title}
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={color}>
                {value}
              </Typography>
              {trend && (
                <Box display="flex" alignItems="center" mt={1}>
                  <TrendingUpOutlined sx={{ fontSize: 16, color: '#10b981', mr: 0.5 }} />
                  <Typography variant="caption" color="#10b981">
                    {trend}
                  </Typography>
                </Box>
              )}
            </Box>
            <Avatar
              sx={{
                bgcolor: `${color}15`,
                color: color,
                width: 56,
                height: 56,
              }}
            >
              {icon}
            </Avatar>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  const performanceData = {
    labels: mockData.coursePerformance.map(course => course.course),
    datasets: [
      {
        data: mockData.coursePerformance.map(course => course.performance),
        backgroundColor: mockData.coursePerformance.map(course => course.color),
        borderWidth: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
        },
      },
    },
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error?.data?.message || error?.message || 'Failed to load dashboard data. Please try again.'}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: '#fafbfc', minHeight: '100vh' }}>
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Typography
            variant="h4"
            fontWeight="800"
            sx={{
              background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            Welcome back, {user?.first_name}! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
            Here's what's happening with your courses today.
          </Typography>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Courses"
            value={stats.total_courses}
            icon={<BookOutlined />}
            color="#3b82f6"
            trend="+2 this semester"
            onClick={() => navigate('/lecturer/courses')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Students"
            value={stats.total_students}
            icon={<GroupOutlined />}
            color="#10b981"
            trend="+12 this week"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Assignments"
            value={stats.total_assignments}
            icon={<AssignmentOutlined />}
            color="#f59e0b"
            onClick={() => navigate('/lecturer/assignments')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Grading"
            value={stats.pending_grading}
            icon={<AssessmentOutlined />}
            color="#ef4444"
            trend="2 urgent"
          />
        </Grid>
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <Card sx={{ height: '100%', border: '1px solid #f1f5f9' }}>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="bold">
                    Recent Activities
                  </Typography>
                  <Button
                    size="small"
                    endIcon={<VisibilityOutlined />}
                    sx={{ textTransform: 'none' }}
                  >
                    View All
                  </Button>
                </Box>
                <List sx={{ p: 0 }}>
                  {(recentActivities.length > 0 ? recentActivities : mockData.recentActivities).map((activity, index) => (
                    <React.Fragment key={activity.id}>
                      <ListItem sx={{ px: 0, py: 2 }}>
                        <ListItemAvatar>
                          <Avatar
                            sx={{
                              bgcolor: activity.type === 'submission' ? '#3b82f615' :
                                      activity.type === 'question' ? '#f59e0b15' : '#10b98115',
                              color: activity.type === 'submission' ? '#3b82f6' :
                                     activity.type === 'question' ? '#f59e0b' : '#10b981',
                            }}
                          >
                            {activity.avatar}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="body2" fontWeight="medium">
                              {activity.title}
                            </Typography>
                          }
                          secondary={
                            <Box>
                              {activity.student && (
                                <Typography variant="caption" color="text.secondary">
                                  {activity.student} • {activity.course}
                                </Typography>
                              )}
                              {!activity.student && (
                                <Typography variant="caption" color="text.secondary">
                                  {activity.course}
                                </Typography>
                              )}
                              <Typography variant="caption" color="text.secondary" display="block">
                                {activity.time}
                              </Typography>
                            </Box>
                          }
                        />
                        <IconButton size="small">
                          <MoreVertOutlined />
                        </IconButton>
                      </ListItem>
                      {index < (recentActivities.length > 0 ? recentActivities : mockData.recentActivities).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Course Performance */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <Card sx={{ height: '100%', border: '1px solid #f1f5f9' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="bold" mb={3}>
                  Course Performance
                </Typography>
                <Box sx={{ height: 250, position: 'relative' }}>
                  <Doughnut data={performanceData} options={chartOptions} />
                </Box>
                <Box mt={2}>
                  {mockData.coursePerformance.map((course) => (
                    <Box key={course.course} display="flex" justifyContent="space-between" alignItems="center" py={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            bgcolor: course.color,
                          }}
                        />
                        <Typography variant="body2">{course.course}</Typography>
                      </Box>
                      <Typography variant="body2" fontWeight="medium">
                        {course.performance}%
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Upcoming Deadlines */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Card sx={{ height: '100%', border: '1px solid #f1f5f9' }}>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="bold">
                    Upcoming Deadlines
                  </Typography>
                  <Button
                    size="small"
                    startIcon={<AddOutlined />}
                    sx={{ textTransform: 'none' }}
                    onClick={() => navigate('/lecturer/assignments/create')}
                  >
                    Add Assignment
                  </Button>
                </Box>
                {mockData.upcomingDeadlines.map((deadline) => {
                  const submissionRate = (deadline.submissions / deadline.total) * 100;
                  const daysLeft = Math.ceil((new Date(deadline.dueDate) - new Date()) / (1000 * 60 * 60 * 24));

                  return (
                    <Box key={deadline.id} mb={3}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" fontWeight="medium">
                          {deadline.title}
                        </Typography>
                        <Chip
                          label={`${daysLeft} days left`}
                          size="small"
                          color={daysLeft <= 2 ? 'error' : daysLeft <= 5 ? 'warning' : 'success'}
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary" display="block" mb={1}>
                        {deadline.course} • Due {new Date(deadline.dueDate).toLocaleDateString()}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="caption">
                          {deadline.submissions}/{deadline.total} submissions
                        </Typography>
                        <Typography variant="caption" fontWeight="medium">
                          {Math.round(submissionRate)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={submissionRate}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: '#f1f5f9',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 3,
                            background: submissionRate >= 80
                              ? 'linear-gradient(90deg, #10b981 0%, #059669 100%)'
                              : submissionRate >= 60
                              ? 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)'
                              : 'linear-gradient(90deg, #ef4444 0%, #dc2626 100%)',
                          },
                        }}
                      />
                    </Box>
                  );
                })}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <Card sx={{ height: '100%', border: '1px solid #f1f5f9' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="bold" mb={3}>
                  Quick Actions
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<AddOutlined />}
                      onClick={() => navigate('/lecturer/assignments/create')}
                      sx={{
                        py: 2,
                        textTransform: 'none',
                        borderColor: '#3b82f6',
                        color: '#3b82f6',
                        '&:hover': {
                          borderColor: '#2563eb',
                          bgcolor: '#3b82f615',
                        },
                      }}
                    >
                      Create Assignment
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<AssessmentOutlined />}
                      onClick={() => navigate('/lecturer/assessments/create')}
                      sx={{
                        py: 2,
                        textTransform: 'none',
                        borderColor: '#10b981',
                        color: '#10b981',
                        '&:hover': {
                          borderColor: '#059669',
                          bgcolor: '#10b98115',
                        },
                      }}
                    >
                      Create Assessment
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<BookOutlined />}
                      onClick={() => navigate('/lecturer/courses')}
                      sx={{
                        py: 2,
                        textTransform: 'none',
                        borderColor: '#f59e0b',
                        color: '#f59e0b',
                        '&:hover': {
                          borderColor: '#d97706',
                          bgcolor: '#f59e0b15',
                        },
                      }}
                    >
                      Manage Courses
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<TimelineOutlined />}
                      onClick={() => navigate('/lecturer/analytics')}
                      sx={{
                        py: 2,
                        textTransform: 'none',
                        borderColor: '#8b5cf6',
                        color: '#8b5cf6',
                        '&:hover': {
                          borderColor: '#7c3aed',
                          bgcolor: '#8b5cf615',
                        },
                      }}
                    >
                      View Analytics
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LecturerDashboard;
