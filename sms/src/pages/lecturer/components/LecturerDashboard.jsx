import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import LecturerSummary from "./LecturerSummary";

function LecturerDashboard() {
  const { user, role } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      window.location.replace("/lecturer/login");
    }
    if (role === "student") {
      navigate("/student");
    }
    if (role === "admin") {
      navigate("/admin");
    }
  }, [user, role, navigate]);

  return (
    <div className="w-full flex flex-col items-center justify-center">
      <LecturerSummary />
    </div>
  );
}

export default LecturerDashboard;
