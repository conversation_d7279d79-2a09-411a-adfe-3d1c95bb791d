import {
  useGetLecturerCoursesQuery,
  useGetLecturerStatsQuery,
} from "@app";
import { Spinner } from "@components";
import {
  AssignmentOutlined,
  BookOutlined,
  GroupOutlined,
  TrendingUpOutlined,
  AssessmentOutlined,
  NotificationsOutlined,
} from "@mui/icons-material";
import {
  Card,
  Grid,
  Typography,
  Box,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Badge,
} from "@mui/material";
import { motion } from "framer-motion";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const LecturerDashboard = () => {
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  const { data: courses, isLoading: isCoursesLoading } = useGetLecturerCoursesQuery(
    user?.id,
    "lecturerCourses"
  );

  const { data: stats, isLoading: isStatsLoading } = useGetLecturerStatsQuery(
    user?.id,
    "lecturerStats"
  );

  const StatCard = ({ title, value, icon, color, trend, onClick }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card
        sx={{
          p: 3,
          height: "100%",
          cursor: onClick ? "pointer" : "default",
          background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
          border: `1px solid ${color}20`,
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: `0 8px 32px ${color}20`,
            transform: onClick ? "translateY(-2px)" : "none",
          },
        }}
        onClick={onClick}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h4" fontWeight="bold" color={color}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary" mt={1}>
              {title}
            </Typography>
            {trend && (
              <Box display="flex" alignItems="center" mt={1}>
                <TrendingUpOutlined sx={{ fontSize: 16, color: "#22c55e", mr: 0.5 }} />
                <Typography variant="caption" color="#22c55e">
                  +{trend}% this month
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: color,
              width: 56,
              height: 56,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </Card>
    </motion.div>
  );

  const CourseCard = ({ course }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card
        sx={{
          p: 3,
          height: "100%",
          cursor: "pointer",
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            transform: "translateY(-2px)",
          },
        }}
        onClick={() => navigate(`/lecturer/courses/${course.id}`)}
      >
        <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
          <Typography variant="h6" fontWeight="bold" noWrap>
            {course.name}
          </Typography>
          <Chip
            label={course.code}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>
        
        <Typography variant="body2" color="text.secondary" mb={2}>
          {course.description || "No description available"}
        </Typography>

        <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
          <Box display="flex" alignItems="center">
            <GroupOutlined sx={{ fontSize: 16, mr: 1, color: "text.secondary" }} />
            <Typography variant="caption" color="text.secondary">
              {course.students_count || 0} Students
            </Typography>
          </Box>
          <Box display="flex" alignItems="center">
            <AssignmentOutlined sx={{ fontSize: 16, mr: 1, color: "text.secondary" }} />
            <Typography variant="caption" color="text.secondary">
              {course.assignments_count || 0} Assignments
            </Typography>
          </Box>
        </Box>

        <Box>
          <Box display="flex" justifyContent="between" alignItems="center" mb={1}>
            <Typography variant="caption" color="text.secondary">
              Course Progress
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {course.progress || 0}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={course.progress || 0}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: "#f3f4f6",
              "& .MuiLinearProgress-bar": {
                borderRadius: 3,
                background: "linear-gradient(90deg, #22c55e 0%, #16a34a 100%)",
              },
            }}
          />
        </Box>
      </Card>
    </motion.div>
  );

  if (isCoursesLoading || isStatsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Spinner />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" alignItems="center" justifyContent="between" mb={4}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Welcome back, {user?.first_name}!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Here's what's happening with your courses today.
            </Typography>
          </Box>
          <IconButton>
            <Badge badgeContent={3} color="error">
              <NotificationsOutlined />
            </Badge>
          </IconButton>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Courses"
            value={stats?.total_courses || courses?.length || 0}
            icon={<BookOutlined />}
            color="#3b82f6"
            trend={12}
            onClick={() => navigate("/lecturer/courses")}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Students"
            value={stats?.total_students || 0}
            icon={<GroupOutlined />}
            color="#22c55e"
            trend={8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Assignments"
            value={stats?.active_assignments || 0}
            icon={<AssignmentOutlined />}
            color="#f59e0b"
            onClick={() => navigate("/lecturer/assignments")}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Grading"
            value={stats?.pending_grading || 0}
            icon={<AssessmentOutlined />}
            color="#ef4444"
            onClick={() => navigate("/lecturer/grading")}
          />
        </Grid>
      </Grid>

      {/* Courses Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Typography variant="h5" fontWeight="bold" mb={3}>
          Your Courses
        </Typography>
        
        {courses?.length === 0 ? (
          <Card sx={{ p: 6, textAlign: "center" }}>
            <BookOutlined sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No courses assigned
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Contact the administrator to get courses assigned to you.
            </Typography>
          </Card>
        ) : (
          <Grid container spacing={3}>
            {courses?.map((course) => (
              <Grid item xs={12} sm={6} md={4} key={course.id}>
                <CourseCard course={course} />
              </Grid>
            ))}
          </Grid>
        )}
      </motion.div>
    </Box>
  );
};

export default LecturerDashboard;
