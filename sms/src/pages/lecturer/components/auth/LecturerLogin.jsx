import { useLecturerLoginMutation } from "@app";
import sideImage from "@assets/images/image.jpg";
import Logo from "@assets/images/logo.jpg";
import { Spinner } from "@components";
import { setUser } from "@features";
import KeyOutlinedIcon from "@mui/icons-material/KeyOutlined";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import { motion } from "framer-motion";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

function LecturerLogin() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading }] = useLecturerLoginMutation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleEmailChange = (event) => {
    setEmail(event.target.value);
  };

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleLogin = async (event) => {
    event.preventDefault();
    try {
      const { success, data } = await login({ email, password }).unwrap();
      if (success) {
          dispatch(
            setUser({
              user: data?.user ?? data?.lecturer,
              token: data?.token,
              role: data?.role || 'lecturer',
              data,
            })
          );
          navigate("/lecturer");
      } else {
        toast.error("Login failed. Please check your credentials.");
      }
    } catch (error) {
      console.log(error);
      toast.error(
        error?.data?.message ?? "An error occurred when trying to login"
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form Section */}
      <motion.div
        className="flex-1 flex items-center justify-center p-8"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-12">
            <motion.div
              className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </motion.div>
            <motion.h2
              className="text-3xl font-bold text-gray-900 mb-3"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              Lecturer Portal
            </motion.h2>
            <motion.p
              className="text-base text-gray-600"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              Sign in to manage your courses and students
            </motion.p>
          </div>

          {/* Login Form */}
          <motion.form
            onSubmit={handleLogin}
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.4 }}
          >
            <div>
              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={email}
                onChange={handleEmailChange}
                required
                variant="outlined"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "12px",
                    backgroundColor: "white",
                  },
                }}
              />
            </div>

            <div>
              <TextField
                fullWidth
                label="Password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={handlePasswordChange}
                required
                variant="outlined"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                  startAdornment: (
                    <InputAdornment position="start">
                      <KeyOutlinedIcon className="text-gray-400" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "12px",
                    backgroundColor: "white",
                  },
                }}
              />
            </div>

            <div className="flex items-center justify-between">
              <Link
                to="/lecturer/forgot-password"
                className="text-sm text-green hover:text-green-dark transition-colors"
              >
                Forgot your password?
              </Link>
            </div>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{
                backgroundColor: "#16a34a",
                "&:hover": { backgroundColor: "#15803d" },
                borderRadius: "12px",
                py: 1.5,
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
              }}
            >
              {isLoading ? <Spinner size={24} /> : "Sign In"}
            </Button>
          </motion.form>
        </div>
      </motion.div>

      {/* Right Side - Image Section */}
      <motion.div
        className="hidden lg:flex flex-1 relative overflow-hidden"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green-dark/90 z-10" />
        <img
          src={sideImage}
          alt="Education"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex items-center justify-center p-12">
          <div className="text-center text-white">
            <motion.h3
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              Empower Your Teaching
            </motion.h3>
            <motion.p
              className="text-xl leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              Manage courses, create assignments, track student progress, and enhance learning experiences with our comprehensive LMS platform.
            </motion.p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default LecturerLogin;
