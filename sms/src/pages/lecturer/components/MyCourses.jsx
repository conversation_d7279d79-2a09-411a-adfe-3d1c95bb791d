import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  Avatar,
  Divider,
  TextField,
  InputAdornment,
  Fab,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  BookOutlined,
  GroupOutlined,
  AssignmentOutlined,
  MoreVertOutlined,
  AddOutlined,
  VisibilityOutlined,
  EditOutlined,
  AnalyticsOutlined,
  SearchOutlined,
  FilterListOutlined,
  SchoolOutlined,
  TrendingUpOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useGetMyLecturerCoursesQuery } from '@app';

const MyCourses = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');

  // Use RTK Query hook to fetch courses
  const {
    data: coursesResponse,
    isLoading: loading,
    error,
    refetch: loadCourses,
  } = useGetMyLecturerCoursesQuery();

  // Extract courses from response or use mock data if API fails
  const courses = coursesResponse?.data || [
    {
      id: 1,
      name: "Computer Science 101",
      code: "CS101",
      description: "Introduction to Programming with Python",
      students_count: 45,
      assignments_count: 3,
      materials_count: 12,
      progress: 75,
      semester: "Fall 2024",
      level: "Level 1",
      color: "#3b82f6",
      status: "active",
    },
    {
      id: 2,
      name: "Database Systems",
      code: "DB201",
      description: "Database Design and Management",
      students_count: 32,
      assignments_count: 2,
      materials_count: 8,
      progress: 60,
      semester: "Fall 2024",
      level: "Level 2",
      color: "#10b981",
      status: "active",
    },
    {
      id: 3,
      name: "Data Structures",
      code: "DS301",
      description: "Advanced Data Structures and Algorithms",
      students_count: 28,
      assignments_count: 4,
      materials_count: 15,
      progress: 85,
      semester: "Fall 2024",
      level: "Level 3",
      color: "#f59e0b",
      status: "active",
    },
    {
      id: 4,
      name: "Web Development",
      code: "WD401",
      description: "Modern Web Development with React",
      students_count: 38,
      assignments_count: 5,
      materials_count: 20,
      progress: 40,
      semester: "Fall 2024",
      level: "Level 4",
      color: "#8b5cf6",
      status: "active",
    },
  ];



  const handleMenuOpen = (event, course) => {
    setAnchorEl(event.currentTarget);
    setSelectedCourse(course);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCourse(null);
  };

  const handleViewCourse = (courseId) => {
    navigate(`/lecturer/courses/${courseId}`);
    handleMenuClose();
  };

  const handleManageMaterials = (courseId) => {
    navigate(`/lecturer/courses/${courseId}/materials`);
    handleMenuClose();
  };

  const handleViewAnalytics = (courseId) => {
    navigate(`/lecturer/courses/${courseId}/analytics`);
    handleMenuClose();
  };

  // Filter courses based on search term
  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterBy === 'all') return matchesSearch;
    if (filterBy === 'active') return matchesSearch && course.status === 'active';
    if (filterBy === 'high-progress') return matchesSearch && course.progress >= 70;
    if (filterBy === 'needs-attention') return matchesSearch && course.progress < 50;

    return matchesSearch;
  });

  const CourseCard = ({ course }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card
        sx={{
          height: "100%",
          borderRadius: 3,
          overflow: "hidden",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          border: "1px solid #f1f5f9",
          "&:hover": {
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
            transform: "translateY(-4px)",
          },
        }}
      >
        {/* Course Header with Color */}
        <Box
          sx={{
            height: 6,
            background: `linear-gradient(90deg, ${course.color}, ${course.color}80)`,
          }}
        />

        <CardContent sx={{ p: 3 }}>
          {/* Header Section */}
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Box flex={1}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Avatar
                  sx={{
                    bgcolor: course.color,
                    width: 48,
                    height: 48,
                    fontSize: "1.2rem",
                    fontWeight: "bold",
                  }}
                >
                  {course.code.substring(0, 2)}
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="700" sx={{ mb: 0.5 }}>
                    {course.name}
                  </Typography>
                  <Chip
                    label={course.code}
                    size="small"
                    sx={{
                      bgcolor: `${course.color}15`,
                      color: course.color,
                      fontWeight: 600,
                      fontSize: "0.75rem",
                    }}
                  />
                </Box>
              </Box>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mb: 2,
                  lineHeight: 1.5,
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical",
                  overflow: "hidden",
                }}
              >
                {course.description}
              </Typography>
            </Box>
            <IconButton
              onClick={(e) => handleMenuOpen(e, course)}
              size="small"
              sx={{
                bgcolor: "grey.50",
                "&:hover": { bgcolor: "grey.100" },
              }}
            >
              <MoreVertOutlined />
            </IconButton>
          </Box>

          {/* Stats Section */}
          <Box
            sx={{
              bgcolor: "grey.50",
              borderRadius: 2,
              p: 2,
              mb: 3,
              border: "1px solid #f1f5f9",
            }}
          >
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Avatar sx={{ bgcolor: "#3b82f6", width: 32, height: 32 }}>
                    <GroupOutlined sx={{ fontSize: 16 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="700" sx={{ fontSize: "1rem" }}>
                      {course.students_count}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Students
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Avatar sx={{ bgcolor: "#f59e0b", width: 32, height: 32 }}>
                    <AssignmentOutlined sx={{ fontSize: 16 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="700" sx={{ fontSize: "1rem" }}>
                      {course.assignments_count}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Tasks
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Avatar sx={{ bgcolor: "#10b981", width: 32, height: 32 }}>
                    <BookOutlined sx={{ fontSize: 16 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="700" sx={{ fontSize: "1rem" }}>
                      {course.materials_count}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Files
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* Progress Section */}
          <Box mb={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Box display="flex" alignItems="center" gap={1}>
                <TrendingUpOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Course Progress
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography
                  variant="body2"
                  fontWeight="700"
                  sx={{
                    color: course.progress >= 70 ? "#10b981" : course.progress >= 40 ? "#f59e0b" : "#ef4444"
                  }}
                >
                  {course.progress}%
                </Typography>
                <Chip
                  label={course.progress >= 70 ? "On Track" : course.progress >= 40 ? "In Progress" : "Needs Attention"}
                  size="small"
                  sx={{
                    bgcolor: course.progress >= 70 ? "#10b98115" : course.progress >= 40 ? "#f59e0b15" : "#ef444415",
                    color: course.progress >= 70 ? "#10b981" : course.progress >= 40 ? "#f59e0b" : "#ef4444",
                    fontWeight: 600,
                    fontSize: "0.7rem",
                  }}
                />
              </Box>
            </Box>
            <LinearProgress
              variant="determinate"
              value={course.progress}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: "#f1f5f9",
                "& .MuiLinearProgress-bar": {
                  borderRadius: 3,
                  background: course.progress >= 70
                    ? "linear-gradient(90deg, #10b981 0%, #059669 100%)"
                    : course.progress >= 40
                    ? "linear-gradient(90deg, #f59e0b 0%, #d97706 100%)"
                    : "linear-gradient(90deg, #ef4444 0%, #dc2626 100%)",
                },
              }}
            />
          </Box>

          {/* Action Buttons */}
          <Divider sx={{ mb: 2 }} />
          <Box display="flex" gap={1}>
            <Button
              variant="contained"
              size="medium"
              startIcon={<VisibilityOutlined />}
              onClick={() => handleViewCourse(course.id)}
              sx={{
                flex: 1,
                bgcolor: course.color,
                "&:hover": { bgcolor: course.color, filter: "brightness(0.9)" },
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 600,
              }}
            >
              Open Course
            </Button>
            <IconButton
              onClick={() => handleViewAnalytics(course.id)}
              sx={{
                bgcolor: "grey.100",
                "&:hover": { bgcolor: "grey.200" },
                borderRadius: 2,
              }}
            >
              <AnalyticsOutlined />
            </IconButton>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  if (loading) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh", display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error?.data?.message || error?.message || 'Failed to load courses. Please try again.'}
        </Alert>
        <Button variant="contained" onClick={loadCourses}>
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight="800"
                sx={{
                  background: "linear-gradient(135deg, #1e293b 0%, #475569 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  mb: 1,
                }}
              >
                My Courses
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ fontSize: "1.1rem" }}>
                Manage your courses, track progress, and engage with students.
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddOutlined />}
              sx={{
                bgcolor: "#3b82f6",
                "&:hover": { bgcolor: "#2563eb" },
                borderRadius: 2,
                px: 3,
                py: 1.5,
                textTransform: "none",
                fontWeight: 600,
                boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)",
              }}
            >
              Create Course
            </Button>
          </Box>

          {/* Search and Filters */}
          <Box
            sx={{
              bgcolor: "white",
              borderRadius: 3,
              p: 3,
              mb: 4,
              border: "1px solid #e2e8f0",
              boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search courses by name, code, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlined sx={{ color: "text.secondary" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                      bgcolor: "#f8fafc",
                      "&:hover": { bgcolor: "#f1f5f9" },
                      "&.Mui-focused": { bgcolor: "white" },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  select
                  fullWidth
                  label="Filter by"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <FilterListOutlined sx={{ color: "text.secondary" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                    },
                  }}
                >
                  <MenuItem value="all">All Courses</MenuItem>
                  <MenuItem value="active">Active Courses</MenuItem>
                  <MenuItem value="high-progress">High Progress (70%+)</MenuItem>
                  <MenuItem value="needs-attention">Needs Attention (&lt;50%)</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} md={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body2" color="text.secondary">
                    {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </motion.div>

      {/* Courses Grid */}
      {filteredCourses.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Grid container spacing={3}>
            {filteredCourses.map((course, index) => (
              <Grid item xs={12} md={6} lg={4} key={course.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <CourseCard course={course} />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              textAlign: "center",
              py: 8,
              px: 4,
              bgcolor: "white",
              borderRadius: 3,
              border: "1px solid #e2e8f0",
            }}
          >
            <SchoolOutlined sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
            <Typography variant="h5" fontWeight="600" gutterBottom>
              {searchTerm ? "No courses found" : "No courses yet"}
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              {searchTerm
                ? `No courses match "${searchTerm}". Try adjusting your search or filters.`
                : "Start by creating your first course to begin teaching."
              }
            </Typography>
            {!searchTerm && (
              <Button
                variant="contained"
                startIcon={<AddOutlined />}
                sx={{
                  bgcolor: "#3b82f6",
                  "&:hover": { bgcolor: "#2563eb" },
                  borderRadius: 2,
                  px: 4,
                  py: 1.5,
                  textTransform: "none",
                  fontWeight: 600,
                }}
              >
                Create Your First Course
              </Button>
            )}
          </Box>
        </motion.div>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        sx={{
          position: "fixed",
          bottom: 24,
          right: 24,
          display: { xs: "flex", md: "none" },
          bgcolor: "#3b82f6",
          "&:hover": { bgcolor: "#2563eb" },
        }}
      >
        <AddOutlined />
      </Fab>

      {/* Course Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleViewCourse(selectedCourse?.id)}>
          <VisibilityOutlined sx={{ mr: 1 }} />
          View Course
        </MenuItem>
        <MenuItem onClick={() => handleManageMaterials(selectedCourse?.id)}>
          <BookOutlined sx={{ mr: 1 }} />
          Manage Materials
        </MenuItem>
        <MenuItem onClick={() => handleViewAnalytics(selectedCourse?.id)}>
          <AnalyticsOutlined sx={{ mr: 1 }} />
          View Analytics
        </MenuItem>
        <MenuItem>
          <EditOutlined sx={{ mr: 1 }} />
          Edit Course
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default MyCourses;
