import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
} from '@mui/material';
import {
  BookOutlined,
  GroupOutlined,
  AssignmentOutlined,
  MoreVertOutlined,
  AddOutlined,
  VisibilityOutlined,
  EditOutlined,
  AnalyticsOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const MyCourses = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);

  // Mock data - replace with actual API call
  const courses = [
    {
      id: 1,
      name: "Computer Science 101",
      code: "CS101",
      description: "Introduction to Programming with Python",
      students_count: 45,
      assignments_count: 3,
      materials_count: 12,
      progress: 75,
      semester: "Fall 2024",
      level: "Level 1",
    },
    {
      id: 2,
      name: "Database Systems",
      code: "DB201",
      description: "Database Design and Management",
      students_count: 32,
      assignments_count: 2,
      materials_count: 8,
      progress: 60,
      semester: "Fall 2024",
      level: "Level 2",
    },
    {
      id: 3,
      name: "Data Structures",
      code: "DS301",
      description: "Advanced Data Structures and Algorithms",
      students_count: 28,
      assignments_count: 4,
      materials_count: 15,
      progress: 85,
      semester: "Fall 2024",
      level: "Level 3",
    },
  ];

  const handleMenuOpen = (event, course) => {
    setAnchorEl(event.currentTarget);
    setSelectedCourse(course);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCourse(null);
  };

  const handleViewCourse = (courseId) => {
    navigate(`/lecturer/courses/${courseId}`);
    handleMenuClose();
  };

  const handleManageMaterials = (courseId) => {
    navigate(`/lecturer/courses/${courseId}/materials`);
    handleMenuClose();
  };

  const handleViewAnalytics = (courseId) => {
    navigate(`/lecturer/courses/${courseId}/analytics`);
    handleMenuClose();
  };

  const CourseCard = ({ course }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card
        sx={{
          height: "100%",
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
          },
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
            <Box flex={1}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Typography variant="h6" fontWeight="bold">
                  {course.name}
                </Typography>
                <Chip label={course.code} size="small" color="primary" />
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                {course.description}
              </Typography>
              <Box display="flex" gap={1} mb={2}>
                <Chip label={course.semester} size="small" variant="outlined" />
                <Chip label={course.level} size="small" variant="outlined" />
              </Box>
            </Box>
            <IconButton
              onClick={(e) => handleMenuOpen(e, course)}
              size="small"
            >
              <MoreVertOutlined />
            </IconButton>
          </Box>

          <Grid container spacing={2} mb={3}>
            <Grid item xs={4}>
              <Box textAlign="center">
                <GroupOutlined color="primary" sx={{ mb: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  {course.students_count}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Students
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box textAlign="center">
                <AssignmentOutlined color="warning" sx={{ mb: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  {course.assignments_count}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Assignments
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box textAlign="center">
                <BookOutlined color="success" sx={{ mb: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  {course.materials_count}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Materials
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box mb={2}>
            <Box display="flex" justifyContent="between" alignItems="center" mb={1}>
              <Typography variant="body2" color="text.secondary">
                Course Progress
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {course.progress}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={course.progress}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: "#f3f4f6",
                "& .MuiLinearProgress-bar": {
                  borderRadius: 4,
                  background: "linear-gradient(90deg, #22c55e 0%, #16a34a 100%)",
                },
              }}
            />
          </Box>

          <Box display="flex" gap={1}>
            <Button
              variant="contained"
              size="small"
              startIcon={<VisibilityOutlined />}
              onClick={() => handleViewCourse(course.id)}
              fullWidth
            >
              View Course
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<AnalyticsOutlined />}
              onClick={() => handleViewAnalytics(course.id)}
              fullWidth
            >
              Analytics
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="between" alignItems="center" mb={4}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              My Courses
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your courses, assignments, and student progress.
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddOutlined />}
            sx={{ height: "fit-content" }}
          >
            Add Course
          </Button>
        </Box>
      </motion.div>

      {/* Courses Grid */}
      <Grid container spacing={3}>
        {courses.map((course) => (
          <Grid item xs={12} md={6} lg={4} key={course.id}>
            <CourseCard course={course} />
          </Grid>
        ))}
      </Grid>

      {/* Course Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleViewCourse(selectedCourse?.id)}>
          <VisibilityOutlined sx={{ mr: 1 }} />
          View Course
        </MenuItem>
        <MenuItem onClick={() => handleManageMaterials(selectedCourse?.id)}>
          <BookOutlined sx={{ mr: 1 }} />
          Manage Materials
        </MenuItem>
        <MenuItem onClick={() => handleViewAnalytics(selectedCourse?.id)}>
          <AnalyticsOutlined sx={{ mr: 1 }} />
          View Analytics
        </MenuItem>
        <MenuItem>
          <EditOutlined sx={{ mr: 1 }} />
          Edit Course
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default MyCourses;
