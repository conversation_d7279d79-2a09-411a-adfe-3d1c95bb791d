import {
  AccountCircleOutlined,
  AnalyticsOutlined,
  AssignmentOutlined,
  AssessmentOutlined,
  BookOutlined,
  GridViewRounded,
  LogoutOutlined,
  GradeOutlined,
} from "@mui/icons-material";

import LogOutModal from "@components/LogOutModal";
import MenuIcon from "@mui/icons-material/Menu";
import { Box, List, ListItem, ListItemIcon, ListItemText } from "@mui/material";
import AppBar from "@mui/material/AppBar";
import CssBaseline from "@mui/material/CssBaseline";
import Divider from "@mui/material/Divider";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import Toolbar from "@mui/material/Toolbar";
import Head from "@pages/admin/components/partials/Header";
import { PropTypes } from "prop-types";
import * as React from "react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "../../../assets/images/Logo.png";
import "../../../assets/styles/styles-2.css";

const drawerWidth = 240;

export default function DashboardLayout({ children }) {
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleDrawerClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerToggle = () => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };

  const role = "lecturer";
  const user = {
    role: "lecturer",
  };
  const { pathname } = useLocation();

  const [openLogoutModal, setOpenLogoutModal] = useState(false);
  const handleLogout = () => {
    setOpenLogoutModal(true);
  };

  const lecturerPaths = [
    {
      tabName: "Dashboard",
      link: `/${user.role}`,
      icon: <GridViewRounded />,
    },
    {
      tabName: "My Courses",
      link: `/${user.role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Assignments",
      link: `/${user.role}/assignments`,
      icon: <AssignmentOutlined />,
    },
    {
      tabName: "Assessments",
      link: `/${user.role}/assessments`,
      icon: <AssessmentOutlined />,
    },
    {
      tabName: "Grading",
      link: `/${user.role}/grading`,
      icon: <GradeOutlined />,
    },
    {
      tabName: "Analytics",
      link: `/${user.role}/analytics`,
      icon: <AnalyticsOutlined />,
    },
    {
      tabName: "Account",
      link: `/${user.role}/profile`,
      icon: <AccountCircleOutlined />,
    },
    {
      tabName: "Logout",
      link: "",
      icon: <LogoutOutlined />,
    },
  ];

  const sideBarItems = lecturerPaths;

  const drawer = (
    <div>
      <div>
        <Link to={`/${role}`} className="w-full flex items-center gap-3 p-5">
          <img src={logo} style={{ height: "44px", width: "40px" }} />
          <div className="flex flex-col">
            <span className="text-lg font-bold text-green">EHIST</span>
            <span className="text-xs text-gray-500">Lecturer Portal</span>
          </div>
        </Link>
      </div>
      <Divider />
      <List>
        {sideBarItems.map((item, index) => {
          const isActive = pathname === item.link;
          return (
            <ListItem key={index} disablePadding>
              <Link
                to={item.link}
                className={`w-full flex items-center gap-3 p-3 mx-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? "bg-green text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={item.tabName === "Logout" ? handleLogout : undefined}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? "white" : "#374151",
                    minWidth: "auto",
                    mr: 1,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.tabName}
                  primaryTypographyProps={{
                    fontSize: "0.875rem",
                    fontWeight: isActive ? 600 : 400,
                    sx: {
                      color: isActive ? "white" : "#374151",
                      letterSpacing: 0.2,
                      transition: "color 0.2s",
                    },
                  }}
                />
              </Link>
            </ListItem>
          );
        })}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: "white",
          boxShadow: "0 1px 3px 0 rgb(0 0 0 / 0.1)",
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: "none" }, color: "#374151" }}
          >
            <MenuIcon />
          </IconButton>
          <Head />
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onTransitionEnd={handleDrawerTransitionEnd}
          onClose={handleDrawerClose}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              backgroundColor: "#fafafa",
              borderRight: "1px solid #e5e7eb",
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", sm: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              backgroundColor: "#fafafa",
              borderRight: "1px solid #e5e7eb",
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          backgroundColor: "#f9fafb",
          minHeight: "100vh",
        }}
      >
        <Toolbar />
        {children}
      </Box>
      <LogOutModal
        open={openLogoutModal}
        setOpen={setOpenLogoutModal}
        role={role}
      />
    </Box>
  );
}

DashboardLayout.propTypes = {
  children: PropTypes.node.isRequired,
};
