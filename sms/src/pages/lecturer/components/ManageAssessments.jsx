import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  TextField,
  InputAdornment,
  Fab,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  AddOutlined,
  AssessmentOutlined,
  MoreVertOutlined,
  EditOutlined,
  DeleteOutlined,
  VisibilityOutlined,
  SearchOutlined,
  TimerOutlined,
  QuizOutlined,
  GroupOutlined,
  PlayArrowOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useGetLecturerAssessmentsQuery } from '@app';

const ManageAssessments = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Use RTK Query hook to fetch assessments
  const {
    data: assessmentsResponse,
    isLoading: loading,
    error,
    refetch: loadAssessments,
  } = useGetLecturerAssessmentsQuery();

  // Extract assessments from response or use mock data if API fails
  const assessments = assessmentsResponse?.data || [
    {
      id: 1,
      title: "Python Fundamentals Quiz",
      course: "Computer Science 101",
      courseCode: "CS101",
      type: "Quiz",
      duration: 30,
      questions: 20,
      attempts: 42,
      totalStudents: 45,
      status: "active",
      startDate: "2024-02-10",
      endDate: "2024-02-15",
      color: "#3b82f6",
    },
    {
      id: 2,
      title: "Database Design Exam",
      course: "Database Systems",
      courseCode: "DB201",
      type: "Exam",
      duration: 120,
      questions: 50,
      attempts: 28,
      totalStudents: 32,
      status: "active",
      startDate: "2024-02-18",
      endDate: "2024-02-20",
      color: "#10b981",
    },
    {
      id: 3,
      title: "Algorithm Analysis Test",
      course: "Data Structures",
      courseCode: "DS301",
      type: "Test",
      duration: 60,
      questions: 15,
      attempts: 28,
      totalStudents: 28,
      status: "completed",
      startDate: "2024-01-25",
      endDate: "2024-01-30",
      color: "#f59e0b",
    },
    {
      id: 4,
      title: "React Components Quiz",
      course: "Web Development",
      courseCode: "WD401",
      type: "Quiz",
      duration: 45,
      questions: 25,
      attempts: 20,
      totalStudents: 38,
      status: "draft",
      startDate: "2024-02-22",
      endDate: "2024-02-25",
      color: "#8b5cf6",
    },
  ];

  const handleMenuOpen = (event, assessment) => {
    setAnchorEl(event.currentTarget);
    setSelectedAssessment(assessment);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAssessment(null);
  };

  const handleViewResults = (assessmentId) => {
    navigate(`/lecturer/assessments/${assessmentId}/results`);
    handleMenuClose();
  };

  const handleEditAssessment = (assessmentId) => {
    navigate(`/lecturer/assessments/${assessmentId}/edit`);
    handleMenuClose();
  };

  const filteredAssessments = assessments.filter(assessment =>
    assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assessment.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assessment.courseCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const AssessmentCard = ({ assessment }) => {
    const completionRate = (assessment.attempts / assessment.totalStudents) * 100;
    const isActive = assessment.status === 'active';
    const isDraft = assessment.status === 'draft';

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ scale: 1.02 }}
      >
        <Card
          sx={{
            height: "100%",
            borderRadius: 3,
            overflow: "hidden",
            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
            border: "1px solid #f1f5f9",
            "&:hover": {
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              transform: "translateY(-4px)",
            },
          }}
        >
          {/* Assessment Header */}
          <Box
            sx={{
              height: 6,
              background: `linear-gradient(90deg, ${assessment.color}, ${assessment.color}80)`,
            }}
          />

          <CardContent sx={{ p: 3 }}>
            {/* Header Section */}
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
              <Box flex={1}>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Avatar
                    sx={{
                      bgcolor: assessment.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    <AssessmentOutlined />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="700" sx={{ mb: 0.5 }}>
                      {assessment.title}
                    </Typography>
                    <Box display="flex" gap={1}>
                      <Chip
                        label={assessment.courseCode}
                        size="small"
                        sx={{
                          bgcolor: `${assessment.color}15`,
                          color: assessment.color,
                          fontWeight: 600,
                        }}
                      />
                      <Chip
                        label={assessment.type}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>
              <IconButton
                onClick={(e) => handleMenuOpen(e, assessment)}
                size="small"
                sx={{
                  bgcolor: "grey.50",
                  "&:hover": { bgcolor: "grey.100" },
                }}
              >
                <MoreVertOutlined />
              </IconButton>
            </Box>

            {/* Course Info */}
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 3, fontWeight: 500 }}
            >
              {assessment.course}
            </Typography>

            {/* Assessment Details */}
            <Box
              sx={{
                bgcolor: "grey.50",
                borderRadius: 2,
                p: 2,
                mb: 3,
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <TimerOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Duration
                      </Typography>
                      <Typography variant="body2" fontWeight="600">
                        {assessment.duration} min
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <QuizOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Questions
                      </Typography>
                      <Typography variant="body2" fontWeight="600">
                        {assessment.questions}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Completion Stats */}
            {!isDraft && (
              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <GroupOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                    <Typography variant="body2" color="text.secondary" fontWeight={500}>
                      Completion
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" fontWeight="700">
                      {assessment.attempts}/{assessment.totalStudents}
                    </Typography>
                    <Chip
                      label={`${Math.round(completionRate)}%`}
                      size="small"
                      sx={{
                        bgcolor: completionRate >= 80 ? "#10b98115" : completionRate >= 60 ? "#f59e0b15" : "#ef444415",
                        color: completionRate >= 80 ? "#10b981" : completionRate >= 60 ? "#f59e0b" : "#ef4444",
                        fontWeight: 600,
                        fontSize: "0.7rem",
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            )}

            {/* Date Range */}
            <Box mb={3}>
              <Typography variant="caption" color="text.secondary">
                Available: {new Date(assessment.startDate).toLocaleDateString()} - {new Date(assessment.endDate).toLocaleDateString()}
              </Typography>
            </Box>

            {/* Status and Actions */}
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Chip
                label={
                  isDraft ? 'Draft' :
                  isActive ? 'Active' :
                  'Completed'
                }
                size="small"
                sx={{
                  bgcolor: isDraft ? "#6b728015" : isActive ? "#10b98115" : "#3b82f615",
                  color: isDraft ? "#6b7280" : isActive ? "#10b981" : "#3b82f6",
                  fontWeight: 600,
                }}
              />
              <Button
                variant="contained"
                size="small"
                startIcon={isDraft ? <PlayArrowOutlined /> : <VisibilityOutlined />}
                onClick={() => isDraft ? handleEditAssessment(assessment.id) : handleViewResults(assessment.id)}
                sx={{
                  bgcolor: assessment.color,
                  "&:hover": { bgcolor: assessment.color, filter: "brightness(0.9)" },
                  borderRadius: 2,
                  textTransform: "none",
                  fontWeight: 600,
                }}
              >
                {isDraft ? 'Publish' : 'View Results'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh", display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error?.data?.message || error?.message || 'Failed to load assessments. Please try again.'}
        </Alert>
        <Button variant="contained" onClick={loadAssessments}>
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight="800"
                sx={{
                  background: "linear-gradient(135deg, #1e293b 0%, #475569 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  mb: 1,
                }}
              >
                Manage Assessments
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ fontSize: "1.1rem" }}>
                Create and manage online quizzes, tests, and exams for your courses.
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddOutlined />}
              onClick={() => navigate('/lecturer/assessments/create')}
              sx={{
                bgcolor: "#3b82f6",
                "&:hover": { bgcolor: "#2563eb" },
                borderRadius: 2,
                px: 3,
                py: 1.5,
                textTransform: "none",
                fontWeight: 600,
                boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)",
              }}
            >
              Create Assessment
            </Button>
          </Box>

          {/* Search */}
          <Box
            sx={{
              bgcolor: "white",
              borderRadius: 3,
              p: 3,
              mb: 4,
              border: "1px solid #e2e8f0",
              boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  placeholder="Search assessments by title, course, or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlined sx={{ color: "text.secondary" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                      bgcolor: "#f8fafc",
                      "&:hover": { bgcolor: "#f1f5f9" },
                      "&.Mui-focused": { bgcolor: "white" },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">
                  {filteredAssessments.length} assessment{filteredAssessments.length !== 1 ? 's' : ''} found
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </motion.div>

      {/* Assessments Grid */}
      {filteredAssessments.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Grid container spacing={3}>
            {filteredAssessments.map((assessment, index) => (
              <Grid item xs={12} md={6} lg={4} key={assessment.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <AssessmentCard assessment={assessment} />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              textAlign: "center",
              py: 8,
              px: 4,
              bgcolor: "white",
              borderRadius: 3,
              border: "1px solid #e2e8f0",
            }}
          >
            <AssessmentOutlined sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
            <Typography variant="h5" fontWeight="600" gutterBottom>
              {searchTerm ? "No assessments found" : "No assessments yet"}
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              {searchTerm
                ? `No assessments match "${searchTerm}". Try adjusting your search.`
                : "Start by creating your first online assessment for your students."
              }
            </Typography>
            {!searchTerm && (
              <Button
                variant="contained"
                startIcon={<AddOutlined />}
                onClick={() => navigate('/lecturer/assessments/create')}
                sx={{
                  bgcolor: "#3b82f6",
                  "&:hover": { bgcolor: "#2563eb" },
                  borderRadius: 2,
                  px: 4,
                  py: 1.5,
                  textTransform: "none",
                  fontWeight: 600,
                }}
              >
                Create Your First Assessment
              </Button>
            )}
          </Box>
        </motion.div>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        onClick={() => navigate('/lecturer/assessments/create')}
        sx={{
          position: "fixed",
          bottom: 24,
          right: 24,
          display: { xs: "flex", md: "none" },
          bgcolor: "#3b82f6",
          "&:hover": { bgcolor: "#2563eb" },
        }}
      >
        <AddOutlined />
      </Fab>

      {/* Assessment Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleViewResults(selectedAssessment?.id)}>
          <VisibilityOutlined sx={{ mr: 1 }} />
          View Results
        </MenuItem>
        <MenuItem onClick={() => handleEditAssessment(selectedAssessment?.id)}>
          <EditOutlined sx={{ mr: 1 }} />
          Edit Assessment
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <DeleteOutlined sx={{ mr: 1 }} />
          Delete Assessment
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ManageAssessments;
