import { useGetLecturerCoursesQuery, useGetLecturerStatsQuery } from "@app";
import { Spinner } from "@components";
import {
  BookOutlined,
  GroupOutlined,
  AssignmentOutlined,
  GradeOutlined,
} from "@mui/icons-material";
import { Box, Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const StatCard = ({ icon, count, title, color }) => {
  const navigate = useNavigate();
  return (
    <div
      className={`w-full flex justify-center items-center gap-4 bg-${color} text-white py-8 rounded-[10px] cursor-pointer`}
      onClick={() => navigate(`/lecturer/${title.toLowerCase()}`)}
    >
      <div
        className={`flex items-center p-2 rounded-full bg-white text-${color}`}
      >
        {icon}
      </div>
      <div>
        <p className="text-lg font-bold">{count}</p>
        <p className="text-[10.5px]">{title}</p>
      </div>
    </div>
  );
};

function LecturerSummary() {
  const { user } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  // Mock data for now - replace with actual API calls
  const [stats, setStats] = useState({
    total_courses: 3,
    total_students: 105,
    active_assignments: 5,
    pending_grading: 12
  });

  const [isLoading, setIsLoading] = useState(false);

  const statCards = [
    {
      icon: <BookOutlined />,
      count: stats?.total_courses || 0,
      title: "Courses",
      color: "green",
    },
    {
      icon: <GroupOutlined />,
      count: stats?.total_students || 0,
      title: "Students",
      color: "blue",
    },
    {
      icon: <AssignmentOutlined />,
      count: stats?.active_assignments || 0,
      title: "Assignments",
      color: "yellow",
    },
    {
      icon: <GradeOutlined />,
      count: stats?.pending_grading || 0,
      title: "Grading",
      color: "red",
    },
  ];

  if (isLoading) {
    return (
      <div className="w-full flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.first_name || 'Lecturer'}!
        </h1>
        <p className="text-gray-600">
          Here's an overview of your teaching activities.
        </p>
      </div>

      {/* Stats Cards */}
      <Grid container spacing={3} className="mb-8">
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard
              icon={card.icon}
              count={card.count}
              title={card.title}
              color={card.color}
            />
          </Grid>
        ))}
      </Grid>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/lecturer/courses')}
              className="w-full text-left p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors"
            >
              <div className="flex items-center gap-3">
                <BookOutlined className="text-green-600" />
                <span className="text-green-800 font-medium">Manage Courses</span>
              </div>
            </button>
            <button
              onClick={() => navigate('/lecturer/assignments')}
              className="w-full text-left p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <div className="flex items-center gap-3">
                <AssignmentOutlined className="text-blue-600" />
                <span className="text-blue-800 font-medium">Create Assignment</span>
              </div>
            </button>
            <button
              onClick={() => navigate('/lecturer/grading')}
              className="w-full text-left p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 transition-colors"
            >
              <div className="flex items-center gap-3">
                <GradeOutlined className="text-yellow-600" />
                <span className="text-yellow-800 font-medium">Grade Submissions</span>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>New assignment submissions</span>
              <span className="font-medium text-green-600">8</span>
            </div>
            <div className="flex justify-between">
              <span>Pending grades</span>
              <span className="font-medium text-yellow-600">12</span>
            </div>
            <div className="flex justify-between">
              <span>Course materials uploaded</span>
              <span className="font-medium text-blue-600">3</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Upcoming Deadlines</h3>
          <div className="space-y-3 text-sm">
            <div className="p-3 bg-red-50 rounded-lg">
              <div className="font-medium text-red-800">Assignment Due</div>
              <div className="text-red-600">Programming Project - Tomorrow</div>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <div className="font-medium text-yellow-800">Exam Schedule</div>
              <div className="text-yellow-600">Database Systems - Next Week</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LecturerSummary;
