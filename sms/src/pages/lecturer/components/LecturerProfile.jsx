import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Avatar,
  Divider,
  Tab,
  Tabs,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
  IconButton,
  Paper,
} from '@mui/material';
import {
  EditOutlined,
  SaveOutlined,
  CameraAltOutlined,
  SecurityOutlined,
  NotificationsOutlined,
  PersonOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import lecturerApi from '../../../services/lecturerApi';

const LecturerProfile = () => {
  const { user } = useSelector((state) => state.auth);
  const [activeTab, setActiveTab] = useState(0);
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [profileData, setProfileData] = useState({
    first_name: user?.first_name || '',
    last_name: user?.last_name || '',
    email: user?.email || '',
    phone_number: user?.phone_number || '',
    bio: '',
    department: '',
    office_location: '',
    office_hours: '',
    specializations: '',
    profile_image: user?.profile_image || null,
  });
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  const [notifications, setNotifications] = useState({
    email_assignments: true,
    email_submissions: true,
    email_messages: true,
    push_notifications: true,
    weekly_reports: false,
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      // For now using user data from Redux. Replace with actual API call:
      // const response = await lecturerApi.getProfile();
      // setProfileData(response.data);
      console.log('Loading profile data...');
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationChange = (field, value) => {
    setNotifications(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // await lecturerApi.updateProfile(profileData);
      console.log('Saving profile:', profileData);
      setEditing(false);
      setSnackbar({ open: true, message: 'Profile updated successfully!', severity: 'success' });
    } catch (error) {
      console.error('Error saving profile:', error);
      setSnackbar({ open: true, message: 'Failed to update profile.', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      setSnackbar({ open: true, message: 'Passwords do not match.', severity: 'error' });
      return;
    }

    setLoading(true);
    try {
      // await lecturerApi.changePassword(passwordData);
      console.log('Changing password...');
      setPasswordData({ current_password: '', new_password: '', confirm_password: '' });
      setSnackbar({ open: true, message: 'Password changed successfully!', severity: 'success' });
    } catch (error) {
      console.error('Error changing password:', error);
      setSnackbar({ open: true, message: 'Failed to change password.', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('image', file);

    try {
      // await lecturerApi.uploadProfileImage(formData);
      console.log('Uploading image:', file);
      setSnackbar({ open: true, message: 'Profile image updated!', severity: 'success' });
    } catch (error) {
      console.error('Error uploading image:', error);
      setSnackbar({ open: true, message: 'Failed to upload image.', severity: 'error' });
    }
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );

  const PersonalInfoTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card sx={{ textAlign: 'center', p: 3 }}>
          <Box position="relative" display="inline-block">
            <Avatar
              src={profileData.profile_image}
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 2,
                fontSize: '3rem',
              }}
            >
              {profileData.first_name?.[0]}{profileData.last_name?.[0]}
            </Avatar>
            <IconButton
              component="label"
              sx={{
                position: 'absolute',
                bottom: 8,
                right: 8,
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' },
              }}
            >
              <CameraAltOutlined />
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={handleImageUpload}
              />
            </IconButton>
          </Box>
          <Typography variant="h6" fontWeight="bold">
            {profileData.first_name} {profileData.last_name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Lecturer
          </Typography>
        </Card>
      </Grid>
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight="bold">
                Personal Information
              </Typography>
              <Button
                variant={editing ? "contained" : "outlined"}
                startIcon={editing ? <SaveOutlined /> : <EditOutlined />}
                onClick={editing ? handleSaveProfile : () => setEditing(true)}
                disabled={loading}
              >
                {editing ? 'Save Changes' : 'Edit Profile'}
              </Button>
            </Box>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={profileData.first_name}
                  onChange={(e) => handleProfileChange('first_name', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={profileData.last_name}
                  onChange={(e) => handleProfileChange('last_name', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Email"
                  value={profileData.email}
                  onChange={(e) => handleProfileChange('email', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={profileData.phone_number}
                  onChange={(e) => handleProfileChange('phone_number', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Department"
                  value={profileData.department}
                  onChange={(e) => handleProfileChange('department', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Office Location"
                  value={profileData.office_location}
                  onChange={(e) => handleProfileChange('office_location', e.target.value)}
                  disabled={!editing}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Office Hours"
                  value={profileData.office_hours}
                  onChange={(e) => handleProfileChange('office_hours', e.target.value)}
                  disabled={!editing}
                  placeholder="e.g., Monday-Friday 9:00 AM - 5:00 PM"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Bio"
                  value={profileData.bio}
                  onChange={(e) => handleProfileChange('bio', e.target.value)}
                  disabled={!editing}
                  placeholder="Tell students about yourself..."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Specializations"
                  value={profileData.specializations}
                  onChange={(e) => handleProfileChange('specializations', e.target.value)}
                  disabled={!editing}
                  placeholder="e.g., Machine Learning, Web Development, Database Systems"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const SecurityTab = () => (
    <Card>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" fontWeight="bold" mb={3}>
          Change Password
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              type="password"
              label="Current Password"
              value={passwordData.current_password}
              onChange={(e) => handlePasswordChange('current_password', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="password"
              label="New Password"
              value={passwordData.new_password}
              onChange={(e) => handlePasswordChange('new_password', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="password"
              label="Confirm New Password"
              value={passwordData.confirm_password}
              onChange={(e) => handlePasswordChange('confirm_password', e.target.value)}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              onClick={handleChangePassword}
              disabled={loading || !passwordData.current_password || !passwordData.new_password}
              sx={{
                bgcolor: "#ef4444",
                "&:hover": { bgcolor: "#dc2626" },
              }}
            >
              Change Password
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const NotificationsTab = () => (
    <Card>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" fontWeight="bold" mb={3}>
          Notification Preferences
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="medium" mb={2}>
              Email Notifications
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={notifications.email_assignments}
                    onChange={(e) => handleNotificationChange('email_assignments', e.target.checked)}
                  />
                }
                label="New assignment submissions"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notifications.email_submissions}
                    onChange={(e) => handleNotificationChange('email_submissions', e.target.checked)}
                  />
                }
                label="Student questions and messages"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notifications.email_messages}
                    onChange={(e) => handleNotificationChange('email_messages', e.target.checked)}
                  />
                }
                label="Course announcements"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notifications.weekly_reports}
                    onChange={(e) => handleNotificationChange('weekly_reports', e.target.checked)}
                  />
                }
                label="Weekly activity reports"
              />
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" fontWeight="medium" mb={2}>
              Push Notifications
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={notifications.push_notifications}
                  onChange={(e) => handleNotificationChange('push_notifications', e.target.checked)}
                />
              }
              label="Enable push notifications"
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              onClick={() => {
                console.log('Saving notification preferences:', notifications);
                setSnackbar({ open: true, message: 'Notification preferences saved!', severity: 'success' });
              }}
              sx={{
                bgcolor: "#10b981",
                "&:hover": { bgcolor: "#059669" },
              }}
            >
              Save Preferences
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Typography
            variant="h4"
            fontWeight="800"
            sx={{
              background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            Profile Settings
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
            Manage your personal information, security settings, and preferences.
          </Typography>
        </Box>
      </motion.div>

      {/* Tabs */}
      <Card sx={{ border: "1px solid #f1f5f9" }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab icon={<PersonOutlined />} label="Personal Info" />
            <Tab icon={<SecurityOutlined />} label="Security" />
            <Tab icon={<NotificationsOutlined />} label="Notifications" />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <PersonalInfoTab />
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          <SecurityTab />
        </TabPanel>
        <TabPanel value={activeTab} index={2}>
          <NotificationsTab />
        </TabPanel>
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LecturerProfile;
