import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  Divider,
  <PERSON>ert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  ArrowBackOutlined,
  SaveOutlined,
  PublishOutlined,
  AttachFileOutlined,
  DeleteOutlined,
  AddOutlined,
  CalendarTodayOutlined,
  AssignmentOutlined,
  GroupOutlined,
  TimerOutlined,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useGetCoursesQuery, useCreateAssignmentMutation } from '@app';

const CreateAssignment = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    courseId: '',
    type: 'individual',
    points: 100,
    dueDate: new Date(),
    allowLateSubmissions: true,
    latePenalty: 10,
    maxAttempts: 1,
    allowGroupSubmissions: false,
    maxGroupSize: 3,
    instructions: '',
    rubric: '',
    attachments: [],
    submissionTypes: ['text'],
    maxFileSize: 10,
    maxFiles: 5,
  });
  const [errors, setErrors] = useState({});

  const steps = ['Basic Information', 'Settings & Requirements', 'Instructions & Rubric', 'Review & Publish'];

  // Use RTK Query hooks
  const { data: coursesResponse } = useGetCoursesQuery();
  const [createAssignment, { isLoading: loading }] = useCreateAssignmentMutation();

  // Extract courses from response or use mock data
  const courses = coursesResponse?.data || [
    { id: 1, name: 'Computer Science 101', code: 'CS101' },
    { id: 2, name: 'Database Systems', code: 'DB201' },
    { id: 3, name: 'Data Structures', code: 'DS301' },
    { id: 4, name: 'Web Development', code: 'WD401' },
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 0:
        if (!formData.title.trim()) newErrors.title = 'Title is required';
        if (!formData.description.trim()) newErrors.description = 'Description is required';
        if (!formData.courseId) newErrors.courseId = 'Course is required';
        if (formData.points <= 0) newErrors.points = 'Points must be greater than 0';
        break;
      case 1:
        if (!formData.dueDate) newErrors.dueDate = 'Due date is required';
        if (formData.dueDate <= new Date()) newErrors.dueDate = 'Due date must be in the future';
        break;
      case 2:
        if (!formData.instructions.trim()) newErrors.instructions = 'Instructions are required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async (isDraft = false) => {
    if (!isDraft && !validateStep(activeStep)) return;

    try {
      const assignmentData = {
        ...formData,
        status: isDraft ? 'draft' : 'published',
      };

      await createAssignment(assignmentData).unwrap();
      console.log('Creating assignment:', assignmentData);

      navigate('/lecturer/assignments');
    } catch (error) {
      console.error('Error creating assignment:', error);
    }
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeAttachment = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const BasicInformationStep = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Assignment Title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          error={!!errors.title}
          helperText={errors.title}
          placeholder="e.g., Python Programming Assignment 1"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          multiline
          rows={3}
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          error={!!errors.description}
          helperText={errors.description}
          placeholder="Brief description of the assignment..."
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth error={!!errors.courseId}>
          <InputLabel>Course</InputLabel>
          <Select
            value={formData.courseId}
            onChange={(e) => handleInputChange('courseId', e.target.value)}
            label="Course"
          >
            {courses.map((course) => (
              <MenuItem key={course.id} value={course.id}>
                {course.code} - {course.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Points"
          value={formData.points}
          onChange={(e) => handleInputChange('points', parseInt(e.target.value))}
          error={!!errors.points}
          helperText={errors.points}
          InputProps={{ inputProps: { min: 1 } }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Assignment Type</InputLabel>
          <Select
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            label="Assignment Type"
          >
            <MenuItem value="individual">Individual</MenuItem>
            <MenuItem value="group">Group</MenuItem>
            <MenuItem value="project">Project</MenuItem>
            <MenuItem value="essay">Essay</MenuItem>
            <MenuItem value="programming">Programming</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DateTimePicker
            label="Due Date"
            value={formData.dueDate}
            onChange={(date) => handleInputChange('dueDate', date)}
            renderInput={(params) => (
              <TextField
                {...params}
                fullWidth
                error={!!errors.dueDate}
                helperText={errors.dueDate}
              />
            )}
          />
        </LocalizationProvider>
      </Grid>
    </Grid>
  );

  const SettingsStep = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Submission Settings
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.allowLateSubmissions}
              onChange={(e) => handleInputChange('allowLateSubmissions', e.target.checked)}
            />
          }
          label="Allow Late Submissions"
        />
      </Grid>
      {formData.allowLateSubmissions && (
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            type="number"
            label="Late Penalty (%)"
            value={formData.latePenalty}
            onChange={(e) => handleInputChange('latePenalty', parseInt(e.target.value))}
            InputProps={{ inputProps: { min: 0, max: 100 } }}
          />
        </Grid>
      )}
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Maximum Attempts"
          value={formData.maxAttempts}
          onChange={(e) => handleInputChange('maxAttempts', parseInt(e.target.value))}
          InputProps={{ inputProps: { min: 1 } }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Maximum File Size (MB)"
          value={formData.maxFileSize}
          onChange={(e) => handleInputChange('maxFileSize', parseInt(e.target.value))}
          InputProps={{ inputProps: { min: 1 } }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Maximum Files"
          value={formData.maxFiles}
          onChange={(e) => handleInputChange('maxFiles', parseInt(e.target.value))}
          InputProps={{ inputProps: { min: 1 } }}
        />
      </Grid>
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Group Settings
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.allowGroupSubmissions}
              onChange={(e) => handleInputChange('allowGroupSubmissions', e.target.checked)}
            />
          }
          label="Allow Group Submissions"
        />
      </Grid>
      {formData.allowGroupSubmissions && (
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            type="number"
            label="Maximum Group Size"
            value={formData.maxGroupSize}
            onChange={(e) => handleInputChange('maxGroupSize', parseInt(e.target.value))}
            InputProps={{ inputProps: { min: 2 } }}
          />
        </Grid>
      )}
    </Grid>
  );

  const InstructionsStep = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          multiline
          rows={8}
          label="Assignment Instructions"
          value={formData.instructions}
          onChange={(e) => handleInputChange('instructions', e.target.value)}
          error={!!errors.instructions}
          helperText={errors.instructions || 'Provide detailed instructions for students'}
          placeholder="Provide detailed instructions for the assignment..."
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          multiline
          rows={6}
          label="Grading Rubric (Optional)"
          value={formData.rubric}
          onChange={(e) => handleInputChange('rubric', e.target.value)}
          placeholder="Define grading criteria and point distribution..."
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Attachments
        </Typography>
        <Button
          variant="outlined"
          component="label"
          startIcon={<AttachFileOutlined />}
          sx={{ mb: 2 }}
        >
          Upload Files
          <input
            type="file"
            hidden
            multiple
            onChange={handleFileUpload}
          />
        </Button>
        {formData.attachments.length > 0 && (
          <List>
            {formData.attachments.map((file, index) => (
              <ListItem key={index}>
                <ListItemText
                  primary={file.name}
                  secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                />
                <ListItemSecondaryAction>
                  <IconButton onClick={() => removeAttachment(index)}>
                    <DeleteOutlined />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Grid>
    </Grid>
  );

  const ReviewStep = () => {
    const selectedCourse = courses.find(c => c.id === formData.courseId);

    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 3 }}>
            Please review all assignment details before publishing. Once published, students will be able to see and submit to this assignment.
          </Alert>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Title</Typography>
                <Typography variant="body1">{formData.title}</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Course</Typography>
                <Typography variant="body1">
                  {selectedCourse ? `${selectedCourse.code} - ${selectedCourse.name}` : 'Not selected'}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Points</Typography>
                <Typography variant="body1">{formData.points}</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Due Date</Typography>
                <Typography variant="body1">
                  {formData.dueDate ? formData.dueDate.toLocaleString() : 'Not set'}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Type</Typography>
                <Chip label={formData.type} size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Settings
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Late Submissions</Typography>
                <Typography variant="body1">
                  {formData.allowLateSubmissions ? `Allowed (${formData.latePenalty}% penalty)` : 'Not allowed'}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Maximum Attempts</Typography>
                <Typography variant="body1">{formData.maxAttempts}</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Group Submissions</Typography>
                <Typography variant="body1">
                  {formData.allowGroupSubmissions ? `Allowed (max ${formData.maxGroupSize} members)` : 'Individual only'}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">File Limits</Typography>
                <Typography variant="body1">
                  Max {formData.maxFiles} files, {formData.maxFileSize}MB each
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Attachments</Typography>
                <Typography variant="body1">
                  {formData.attachments.length} file(s) attached
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <BasicInformationStep />;
      case 1:
        return <SettingsStep />;
      case 2:
        return <InstructionsStep />;
      case 3:
        return <ReviewStep />;
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Button
            startIcon={<ArrowBackOutlined />}
            onClick={() => navigate('/lecturer/assignments')}
            sx={{ mb: 2, textTransform: "none" }}
          >
            Back to Assignments
          </Button>

          <Typography
            variant="h4"
            fontWeight="800"
            sx={{
              background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            Create New Assignment
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
            Set up a new assignment for your students with detailed instructions and requirements.
          </Typography>
        </Box>
      </motion.div>

      {/* Stepper */}
      <Card sx={{ mb: 4, border: "1px solid #f1f5f9" }}>
        <CardContent sx={{ p: 3 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card sx={{ border: "1px solid #f1f5f9" }}>
        <CardContent sx={{ p: 4 }}>
          {getStepContent(activeStep)}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
          sx={{ textTransform: "none" }}
        >
          Back
        </Button>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => handleSubmit(true)}
            startIcon={<SaveOutlined />}
            disabled={loading}
            sx={{ textTransform: "none" }}
          >
            Save as Draft
          </Button>

          {activeStep === steps.length - 1 ? (
            <Button
              variant="contained"
              onClick={() => handleSubmit(false)}
              startIcon={<PublishOutlined />}
              disabled={loading}
              sx={{
                bgcolor: "#10b981",
                "&:hover": { bgcolor: "#059669" },
                textTransform: "none",
              }}
            >
              Publish Assignment
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleNext}
              sx={{
                bgcolor: "#3b82f6",
                "&:hover": { bgcolor: "#2563eb" },
                textTransform: "none",
              }}
            >
              Next
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default CreateAssignment;
