import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  LinearProgress,
  TextField,
  InputAdornment,
  Fab,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  AddOutlined,
  AssignmentOutlined,
  MoreVertOutlined,
  EditOutlined,
  DeleteOutlined,
  VisibilityOutlined,
  SearchOutlined,
  FilterListOutlined,
  CalendarTodayOutlined,
  GroupOutlined,
  TrendingUpOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import lecturerApi from '../../../services/lecturerApi';

const ManageAssignments = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAssignments();
  }, []);

  const loadAssignments = async () => {
    try {
      setLoading(true);
      // For now using mock data. Replace with actual API call:
      // const response = await lecturerApi.getAssignments();
      // setAssignments(response.data);

      // Mock data
      const mockAssignments = [
    {
      id: 1,
      title: "Python Programming Basics",
      course: "Computer Science 101",
      courseCode: "CS101",
      dueDate: "2024-02-15",
      submissions: 35,
      totalStudents: 45,
      status: "active",
      type: "Programming",
      points: 100,
      color: "#3b82f6",
    },
    {
      id: 2,
      title: "Database Design Project",
      course: "Database Systems",
      courseCode: "DB201",
      dueDate: "2024-02-20",
      submissions: 28,
      totalStudents: 32,
      status: "active",
      type: "Project",
      points: 150,
      color: "#10b981",
    },
    {
      id: 3,
      title: "Algorithm Analysis",
      course: "Data Structures",
      courseCode: "DS301",
      dueDate: "2024-01-30",
      submissions: 28,
      totalStudents: 28,
      status: "completed",
      type: "Theory",
      points: 80,
      color: "#f59e0b",
    },
    {
      id: 4,
      title: "React Component Development",
      course: "Web Development",
      courseCode: "WD401",
      dueDate: "2024-02-25",
      submissions: 15,
      totalStudents: 38,
      status: "active",
      type: "Programming",
      points: 120,
      color: "#8b5cf6",
    },
      ];

      setAssignments(mockAssignments);
    } catch (err) {
      setError('Failed to load assignments. Please try again.');
      console.error('Error loading assignments:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event, assignment) => {
    setAnchorEl(event.currentTarget);
    setSelectedAssignment(assignment);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAssignment(null);
  };

  const handleViewSubmissions = (assignmentId) => {
    navigate(`/lecturer/assignments/${assignmentId}/submissions`);
    handleMenuClose();
  };

  const handleEditAssignment = (assignmentId) => {
    navigate(`/lecturer/assignments/${assignmentId}/edit`);
    handleMenuClose();
  };

  const filteredAssignments = assignments.filter(assignment =>
    assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.courseCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const AssignmentCard = ({ assignment }) => {
    const submissionRate = (assignment.submissions / assignment.totalStudents) * 100;
    const isOverdue = new Date(assignment.dueDate) < new Date() && assignment.status === 'active';

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ scale: 1.02 }}
      >
        <Card
          sx={{
            height: "100%",
            borderRadius: 3,
            overflow: "hidden",
            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
            border: "1px solid #f1f5f9",
            "&:hover": {
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              transform: "translateY(-4px)",
            },
          }}
        >
          {/* Assignment Header */}
          <Box
            sx={{
              height: 6,
              background: `linear-gradient(90deg, ${assignment.color}, ${assignment.color}80)`,
            }}
          />

          <CardContent sx={{ p: 3 }}>
            {/* Header Section */}
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
              <Box flex={1}>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Avatar
                    sx={{
                      bgcolor: assignment.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    <AssignmentOutlined />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="700" sx={{ mb: 0.5 }}>
                      {assignment.title}
                    </Typography>
                    <Box display="flex" gap={1}>
                      <Chip
                        label={assignment.courseCode}
                        size="small"
                        sx={{
                          bgcolor: `${assignment.color}15`,
                          color: assignment.color,
                          fontWeight: 600,
                        }}
                      />
                      <Chip
                        label={assignment.type}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>
              <IconButton
                onClick={(e) => handleMenuOpen(e, assignment)}
                size="small"
                sx={{
                  bgcolor: "grey.50",
                  "&:hover": { bgcolor: "grey.100" },
                }}
              >
                <MoreVertOutlined />
              </IconButton>
            </Box>

            {/* Course Info */}
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 3, fontWeight: 500 }}
            >
              {assignment.course}
            </Typography>

            {/* Stats Section */}
            <Box
              sx={{
                bgcolor: "grey.50",
                borderRadius: 2,
                p: 2,
                mb: 3,
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CalendarTodayOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Due Date
                      </Typography>
                      <Typography variant="body2" fontWeight="600">
                        {new Date(assignment.dueDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <GroupOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Points
                      </Typography>
                      <Typography variant="body2" fontWeight="600">
                        {assignment.points} pts
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Submission Progress */}
            <Box mb={3}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingUpOutlined sx={{ fontSize: 16, color: "text.secondary" }} />
                  <Typography variant="body2" color="text.secondary" fontWeight={500}>
                    Submissions
                  </Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body2" fontWeight="700">
                    {assignment.submissions}/{assignment.totalStudents}
                  </Typography>
                  <Chip
                    label={`${Math.round(submissionRate)}%`}
                    size="small"
                    sx={{
                      bgcolor: submissionRate >= 80 ? "#10b98115" : submissionRate >= 60 ? "#f59e0b15" : "#ef444415",
                      color: submissionRate >= 80 ? "#10b981" : submissionRate >= 60 ? "#f59e0b" : "#ef4444",
                      fontWeight: 600,
                      fontSize: "0.7rem",
                    }}
                  />
                </Box>
              </Box>
              <LinearProgress
                variant="determinate"
                value={submissionRate}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: "#f1f5f9",
                  "& .MuiLinearProgress-bar": {
                    borderRadius: 3,
                    background: submissionRate >= 80
                      ? "linear-gradient(90deg, #10b981 0%, #059669 100%)"
                      : submissionRate >= 60
                      ? "linear-gradient(90deg, #f59e0b 0%, #d97706 100%)"
                      : "linear-gradient(90deg, #ef4444 0%, #dc2626 100%)",
                  },
                }}
              />
            </Box>

            {/* Status and Actions */}
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Chip
                label={assignment.status === 'active' ? (isOverdue ? 'Overdue' : 'Active') : 'Completed'}
                size="small"
                sx={{
                  bgcolor: assignment.status === 'active'
                    ? (isOverdue ? "#ef444415" : "#10b98115")
                    : "#6b728015",
                  color: assignment.status === 'active'
                    ? (isOverdue ? "#ef4444" : "#10b981")
                    : "#6b7280",
                  fontWeight: 600,
                }}
              />
              <Button
                variant="contained"
                size="small"
                startIcon={<VisibilityOutlined />}
                onClick={() => handleViewSubmissions(assignment.id)}
                sx={{
                  bgcolor: assignment.color,
                  "&:hover": { bgcolor: assignment.color, filter: "brightness(0.9)" },
                  borderRadius: 2,
                  textTransform: "none",
                  fontWeight: 600,
                }}
              >
                View
              </Button>
            </Box>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh", display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={loadAssignments}>
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight="800"
                sx={{
                  background: "linear-gradient(135deg, #1e293b 0%, #475569 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  mb: 1,
                }}
              >
                Manage Assignments
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ fontSize: "1.1rem" }}>
                Create, manage, and track assignment submissions across all your courses.
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddOutlined />}
              onClick={() => navigate('/lecturer/assignments/create')}
              sx={{
                bgcolor: "#3b82f6",
                "&:hover": { bgcolor: "#2563eb" },
                borderRadius: 2,
                px: 3,
                py: 1.5,
                textTransform: "none",
                fontWeight: 600,
                boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)",
              }}
            >
              Create Assignment
            </Button>
          </Box>

          {/* Search */}
          <Box
            sx={{
              bgcolor: "white",
              borderRadius: 3,
              p: 3,
              mb: 4,
              border: "1px solid #e2e8f0",
              boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  placeholder="Search assignments by title, course, or code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlined sx={{ color: "text.secondary" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                      bgcolor: "#f8fafc",
                      "&:hover": { bgcolor: "#f1f5f9" },
                      "&.Mui-focused": { bgcolor: "white" },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="text.secondary">
                  {filteredAssignments.length} assignment{filteredAssignments.length !== 1 ? 's' : ''} found
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </motion.div>

      {/* Assignments Grid */}
      {filteredAssignments.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Grid container spacing={3}>
            {filteredAssignments.map((assignment, index) => (
              <Grid item xs={12} md={6} lg={4} key={assignment.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <AssignmentCard assignment={assignment} />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              textAlign: "center",
              py: 8,
              px: 4,
              bgcolor: "white",
              borderRadius: 3,
              border: "1px solid #e2e8f0",
            }}
          >
            <AssignmentOutlined sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
            <Typography variant="h5" fontWeight="600" gutterBottom>
              {searchTerm ? "No assignments found" : "No assignments yet"}
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              {searchTerm
                ? `No assignments match "${searchTerm}". Try adjusting your search.`
                : "Start by creating your first assignment for your students."
              }
            </Typography>
            {!searchTerm && (
              <Button
                variant="contained"
                startIcon={<AddOutlined />}
                onClick={() => navigate('/lecturer/assignments/create')}
                sx={{
                  bgcolor: "#3b82f6",
                  "&:hover": { bgcolor: "#2563eb" },
                  borderRadius: 2,
                  px: 4,
                  py: 1.5,
                  textTransform: "none",
                  fontWeight: 600,
                }}
              >
                Create Your First Assignment
              </Button>
            )}
          </Box>
        </motion.div>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        onClick={() => navigate('/lecturer/assignments/create')}
        sx={{
          position: "fixed",
          bottom: 24,
          right: 24,
          display: { xs: "flex", md: "none" },
          bgcolor: "#3b82f6",
          "&:hover": { bgcolor: "#2563eb" },
        }}
      >
        <AddOutlined />
      </Fab>

      {/* Assignment Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleViewSubmissions(selectedAssignment?.id)}>
          <VisibilityOutlined sx={{ mr: 1 }} />
          View Submissions
        </MenuItem>
        <MenuItem onClick={() => handleEditAssignment(selectedAssignment?.id)}>
          <EditOutlined sx={{ mr: 1 }} />
          Edit Assignment
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <DeleteOutlined sx={{ mr: 1 }} />
          Delete Assignment
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ManageAssignments;
