import {
  AccountCircleOutlined,
  AnalyticsOutlined,
  AssignmentOutlined,
  AssessmentOutlined,
  BookOutlined,
  DashboardOutlined,
  FolderOutlined,
  GradeOutlined,
  LogoutOutlined,
} from "@mui/icons-material";
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import { useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";

const LecturerSidebar = () => {
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const role = "lecturer";

  const menuItems = [
    {
      tabName: "Dashboard",
      link: `/${role}`,
      icon: <DashboardOutlined />,
    },
    {
      tabName: "My Courses",
      link: `/${role}/courses`,
      icon: <BookOutlined />,
    },
    {
      tabName: "Assignments",
      link: `/${role}/assignments`,
      icon: <AssignmentOutlined />,
    },
    {
      tabName: "Assessments",
      link: `/${role}/assessments`,
      icon: <AssessmentOutlined />,
    },
    {
      tabName: "Grading",
      link: `/${role}/grading`,
      icon: <GradeOutlined />,
    },
    {
      tabName: "Analytics",
      link: `/${role}/analytics`,
      icon: <AnalyticsOutlined />,
    },
    {
      tabName: "Profile",
      link: `/${role}/profile`,
      icon: <AccountCircleOutlined />,
    },
  ];

  const handleLogout = () => {
    // Implement logout logic
    console.log("Logging out...");
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 280,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: 280,
          boxSizing: "border-box",
          backgroundColor: "#1e293b",
          color: "white",
        },
      }}
    >
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" fontWeight="bold" color="white">
          Lecturer Portal
        </Typography>
      </Box>

      <Divider sx={{ borderColor: "rgba(255,255,255,0.1)" }} />

      {/* User Info */}
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Avatar
          sx={{
            width: 64,
            height: 64,
            mx: "auto",
            mb: 2,
            backgroundColor: "#3b82f6",
          }}
        >
          {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
        </Avatar>
        <Typography variant="subtitle1" fontWeight="medium" color="white">
          {user?.first_name} {user?.last_name}
        </Typography>
        <Typography variant="body2" color="rgba(255,255,255,0.7)">
          Lecturer
        </Typography>
      </Box>

      <Divider sx={{ borderColor: "rgba(255,255,255,0.1)" }} />

      {/* Navigation Menu */}
      <List sx={{ px: 2, py: 1 }}>
        {menuItems.map((item) => {
          const isActive = location.pathname === item.link;
          return (
            <ListItem key={item.tabName} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                component={Link}
                to={item.link}
                sx={{
                  borderRadius: 2,
                  color: isActive ? "#3b82f6" : "rgba(255,255,255,0.8)",
                  backgroundColor: isActive ? "rgba(59,130,246,0.1)" : "transparent",
                  "&:hover": {
                    backgroundColor: "rgba(255,255,255,0.05)",
                    color: "white",
                  },
                  py: 1.5,
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? "#3b82f6" : "rgba(255,255,255,0.8)",
                    minWidth: 40,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.tabName}
                  primaryTypographyProps={{
                    fontSize: "0.875rem",
                    fontWeight: isActive ? 600 : 400,
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      {/* Logout */}
      <Box sx={{ mt: "auto", p: 2 }}>
        <ListItemButton
          onClick={handleLogout}
          sx={{
            borderRadius: 2,
            color: "rgba(255,255,255,0.8)",
            "&:hover": {
              backgroundColor: "rgba(239,68,68,0.1)",
              color: "#ef4444",
            },
            py: 1.5,
          }}
        >
          <ListItemIcon
            sx={{
              color: "inherit",
              minWidth: 40,
            }}
          >
            <LogoutOutlined />
          </ListItemIcon>
          <ListItemText
            primary="Logout"
            primaryTypographyProps={{
              fontSize: "0.875rem",
            }}
          />
        </ListItemButton>
      </Box>
    </Drawer>
  );
};

export default LecturerSidebar;
