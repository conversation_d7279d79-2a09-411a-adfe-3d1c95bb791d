import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Avatar,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  ArrowBackOutlined,
  EditOutlined,
  GroupOutlined,
  AssignmentOutlined,
  FolderOutlined,
  AnalyticsOutlined,
  AddOutlined,
  MoreVertOutlined,
  DownloadOutlined,
  VisibilityOutlined,
  PersonAddOutlined,
  EmailOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import lecturerApi from '../../../services/lecturerApi';

const CourseDetails = () => {
  const navigate = useNavigate();
  const { courseId } = useParams();
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openAddStudent, setOpenAddStudent] = useState(false);
  const [courseData, setCourseData] = useState({
    course: {
      id: 1,
      name: "Computer Science 101",
      code: "CS101",
      description: "Introduction to Programming with Python",
      semester: "Fall 2024",
      level: "Level 1",
      credits: 3,
      color: "#3b82f6",
      enrolledStudents: 45,
      totalAssignments: 8,
      totalMaterials: 12,
      averageGrade: 85.5,
    },
    students: [
      {
        id: 1,
        name: "John Doe",
        matricule: "CS2024001",
        email: "<EMAIL>",
        avatar: "JD",
        grade: 88,
        attendance: 95,
        assignments: 7,
        totalAssignments: 8,
      },
      {
        id: 2,
        name: "Jane Smith",
        matricule: "CS2024002",
        email: "<EMAIL>",
        avatar: "JS",
        grade: 92,
        attendance: 100,
        assignments: 8,
        totalAssignments: 8,
      },
      {
        id: 3,
        name: "Mike Johnson",
        matricule: "CS2024003",
        email: "<EMAIL>",
        avatar: "MJ",
        grade: 76,
        attendance: 85,
        assignments: 6,
        totalAssignments: 8,
      },
    ],
    assignments: [
      {
        id: 1,
        title: "Python Basics Assignment",
        dueDate: "2024-02-15",
        submissions: 42,
        total: 45,
        status: "active",
      },
      {
        id: 2,
        title: "Data Structures Project",
        dueDate: "2024-02-20",
        submissions: 38,
        total: 45,
        status: "active",
      },
    ],
    materials: [
      {
        id: 1,
        name: "Python Programming Guide.pdf",
        type: "PDF",
        size: "2.5 MB",
        uploadDate: "2024-01-15",
        downloads: 42,
      },
      {
        id: 2,
        name: "Lecture 1 - Introduction.pptx",
        type: "PowerPoint",
        size: "5.2 MB",
        uploadDate: "2024-01-10",
        downloads: 45,
      },
    ],
    loading: false,
  });

  useEffect(() => {
    loadCourseData();
  }, [courseId]);

  const loadCourseData = async () => {
    try {
      // For now using mock data. Replace with actual API calls:
      // const courseResponse = await lecturerApi.getCourseById(courseId);
      // const studentsResponse = await lecturerApi.getCourseStudents(courseId);
      // const materialsResponse = await lecturerApi.getCourseMaterials(courseId);

      // Mock data is already set in state
      console.log('Loading course data for:', courseId);
    } catch (error) {
      console.error('Error loading course data:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleStudentMenuOpen = (event, student) => {
    setAnchorEl(event.currentTarget);
    setSelectedStudent(student);
  };

  const handleStudentMenuClose = () => {
    setAnchorEl(null);
    setSelectedStudent(null);
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );

  const StudentsTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" fontWeight="bold">
          Enrolled Students ({courseData.students.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<PersonAddOutlined />}
          onClick={() => setOpenAddStudent(true)}
          sx={{
            bgcolor: "#3b82f6",
            "&:hover": { bgcolor: "#2563eb" },
            textTransform: "none",
          }}
        >
          Add Student
        </Button>
      </Box>

      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: "#f8fafc" }}>
              <TableCell>Student</TableCell>
              <TableCell>Matricule</TableCell>
              <TableCell>Grade</TableCell>
              <TableCell>Attendance</TableCell>
              <TableCell>Assignments</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {courseData.students.map((student) => (
              <TableRow key={student.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar sx={{ bgcolor: "#3b82f6" }}>{student.avatar}</Avatar>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {student.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {student.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{student.matricule}</TableCell>
                <TableCell>
                  <Chip
                    label={`${student.grade}%`}
                    size="small"
                    sx={{
                      bgcolor: student.grade >= 80 ? "#10b98115" : student.grade >= 60 ? "#f59e0b15" : "#ef444415",
                      color: student.grade >= 80 ? "#10b981" : student.grade >= 60 ? "#f59e0b" : "#ef4444",
                      fontWeight: 600,
                    }}
                  />
                </TableCell>
                <TableCell>{student.attendance}%</TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2">
                      {student.assignments}/{student.totalAssignments}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(student.assignments / student.totalAssignments) * 100}
                      sx={{ width: 60, height: 4, borderRadius: 2 }}
                    />
                  </Box>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={(e) => handleStudentMenuOpen(e, student)}
                  >
                    <MoreVertOutlined />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const AssignmentsTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" fontWeight="bold">
          Course Assignments ({courseData.assignments.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddOutlined />}
          onClick={() => navigate('/lecturer/assignments/create')}
          sx={{
            bgcolor: "#10b981",
            "&:hover": { bgcolor: "#059669" },
            textTransform: "none",
          }}
        >
          Create Assignment
        </Button>
      </Box>

      <Grid container spacing={3}>
        {courseData.assignments.map((assignment) => {
          const submissionRate = (assignment.submissions / assignment.total) * 100;
          return (
            <Grid item xs={12} md={6} key={assignment.id}>
              <Card sx={{ border: "1px solid #f1f5f9" }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="start" mb={2}>
                    <Typography variant="h6" fontWeight="bold">
                      {assignment.title}
                    </Typography>
                    <Chip
                      label={assignment.status}
                      size="small"
                      color={assignment.status === 'active' ? 'success' : 'default'}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    Due: {new Date(assignment.dueDate).toLocaleDateString()}
                  </Typography>
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">Submissions</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {assignment.submissions}/{assignment.total}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={submissionRate}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<VisibilityOutlined />}
                    onClick={() => navigate(`/lecturer/assignments/${assignment.id}/submissions`)}
                    sx={{ textTransform: "none" }}
                  >
                    View Submissions
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );

  const MaterialsTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" fontWeight="bold">
          Course Materials ({courseData.materials.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddOutlined />}
          onClick={() => navigate(`/lecturer/courses/${courseId}/materials`)}
          sx={{
            bgcolor: "#f59e0b",
            "&:hover": { bgcolor: "#d97706" },
            textTransform: "none",
          }}
        >
          Upload Material
        </Button>
      </Box>

      <Grid container spacing={3}>
        {courseData.materials.map((material) => (
          <Grid item xs={12} md={6} key={material.id}>
            <Card sx={{ border: "1px solid #f1f5f9" }}>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Avatar sx={{ bgcolor: "#f59e0b15", color: "#f59e0b" }}>
                    <FolderOutlined />
                  </Avatar>
                  <Box flex={1}>
                    <Typography variant="body1" fontWeight="medium">
                      {material.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {material.type} • {material.size}
                    </Typography>
                  </Box>
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="caption" color="text.secondary">
                    Uploaded: {new Date(material.uploadDate).toLocaleDateString()}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {material.downloads} downloads
                  </Typography>
                </Box>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<DownloadOutlined />}
                  sx={{ textTransform: "none" }}
                >
                  Download
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const AnalyticsTab = () => (
    <Box>
      <Typography variant="h6" fontWeight="bold" mb={3}>
        Course Analytics
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Card sx={{ textAlign: "center", p: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="#3b82f6">
              {courseData.course.averageGrade}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Average Grade
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ textAlign: "center", p: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="#10b981">
              92%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Attendance Rate
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ textAlign: "center", p: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="#f59e0b">
              87%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Assignment Completion
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ textAlign: "center", p: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="#8b5cf6">
              4.2
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Course Rating
            </Typography>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  if (courseData.loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading course details...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Button
            startIcon={<ArrowBackOutlined />}
            onClick={() => navigate('/lecturer/courses')}
            sx={{ mb: 2, textTransform: "none" }}
          >
            Back to Courses
          </Button>

          <Card sx={{ border: "1px solid #f1f5f9" }}>
            <CardContent sx={{ p: 4 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item>
                  <Avatar
                    sx={{
                      bgcolor: courseData.course.color,
                      width: 80,
                      height: 80,
                      fontSize: "2rem",
                      fontWeight: "bold",
                    }}
                  >
                    {courseData.course.code.substring(0, 2)}
                  </Avatar>
                </Grid>
                <Grid item flex={1}>
                  <Typography variant="h4" fontWeight="bold" gutterBottom>
                    {courseData.course.name}
                  </Typography>
                  <Typography variant="body1" color="text.secondary" mb={2}>
                    {courseData.course.description}
                  </Typography>
                  <Box display="flex" gap={1} mb={2}>
                    <Chip label={courseData.course.code} sx={{ bgcolor: `${courseData.course.color}15`, color: courseData.course.color }} />
                    <Chip label={courseData.course.semester} variant="outlined" />
                    <Chip label={courseData.course.level} variant="outlined" />
                    <Chip label={`${courseData.course.credits} Credits`} variant="outlined" />
                  </Box>
                </Grid>
                <Grid item>
                  <Button
                    variant="contained"
                    startIcon={<EditOutlined />}
                    sx={{
                      bgcolor: courseData.course.color,
                      "&:hover": { bgcolor: courseData.course.color, filter: "brightness(0.9)" },
                      textTransform: "none",
                    }}
                  >
                    Edit Course
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: "center", p: 3, border: "1px solid #f1f5f9" }}>
            <GroupOutlined sx={{ fontSize: 40, color: "#3b82f6", mb: 1 }} />
            <Typography variant="h5" fontWeight="bold">
              {courseData.course.enrolledStudents}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Enrolled Students
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: "center", p: 3, border: "1px solid #f1f5f9" }}>
            <AssignmentOutlined sx={{ fontSize: 40, color: "#10b981", mb: 1 }} />
            <Typography variant="h5" fontWeight="bold">
              {courseData.course.totalAssignments}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Assignments
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: "center", p: 3, border: "1px solid #f1f5f9" }}>
            <FolderOutlined sx={{ fontSize: 40, color: "#f59e0b", mb: 1 }} />
            <Typography variant="h5" fontWeight="bold">
              {courseData.course.totalMaterials}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Course Materials
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: "center", p: 3, border: "1px solid #f1f5f9" }}>
            <AnalyticsOutlined sx={{ fontSize: 40, color: "#8b5cf6", mb: 1 }} />
            <Typography variant="h5" fontWeight="bold">
              {courseData.course.averageGrade}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Average Grade
            </Typography>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card sx={{ border: "1px solid #f1f5f9" }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Students" />
            <Tab label="Assignments" />
            <Tab label="Materials" />
            <Tab label="Analytics" />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <StudentsTab />
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          <AssignmentsTab />
        </TabPanel>
        <TabPanel value={activeTab} index={2}>
          <MaterialsTab />
        </TabPanel>
        <TabPanel value={activeTab} index={3}>
          <AnalyticsTab />
        </TabPanel>
      </Card>

      {/* Student Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleStudentMenuClose}
      >
        <MenuItem onClick={handleStudentMenuClose}>
          <VisibilityOutlined sx={{ mr: 1 }} />
          View Profile
        </MenuItem>
        <MenuItem onClick={handleStudentMenuClose}>
          <EmailOutlined sx={{ mr: 1 }} />
          Send Message
        </MenuItem>
        <MenuItem onClick={handleStudentMenuClose}>
          <AnalyticsOutlined sx={{ mr: 1 }} />
          View Progress
        </MenuItem>
      </Menu>

      {/* Add Student Dialog */}
      <Dialog open={openAddStudent} onClose={() => setOpenAddStudent(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Student to Course</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Student Email or Matricule"
            margin="normal"
            placeholder="Enter student email or matricule number"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddStudent(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setOpenAddStudent(false)}>
            Add Student
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{
          position: "fixed",
          bottom: 24,
          right: 24,
          display: { xs: "flex", md: "none" },
        }}
      >
        <AddOutlined />
      </Fab>
    </Box>
  );
};

export default CourseDetails;
