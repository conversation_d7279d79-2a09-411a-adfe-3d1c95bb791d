import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Chip,
  LinearProgress,
  Button,
} from '@mui/material';
import {
  TrendingUpOutlined,
  TrendingDownOutlined,
  GroupOutlined,
  AssignmentOutlined,
  SchoolOutlined,
  AnalyticsOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import lecturerApi from '../../../services/lecturerApi';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const StudentAnalytics = () => {
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalStudents: 143,
      averageGrade: 85.2,
      completionRate: 87,
      activeStudents: 128,
    },
    courses: [
      { id: 'all', name: 'All Courses' },
      { id: 1, name: 'Computer Science 101' },
      { id: 2, name: 'Database Systems' },
      { id: 3, name: 'Data Structures' },
      { id: 4, name: 'Web Development' },
    ],
    topPerformers: [
      { id: 1, name: 'Jane Smith', course: 'CS101', grade: 98, avatar: 'JS' },
      { id: 2, name: 'John Doe', course: 'DB201', grade: 95, avatar: 'JD' },
      { id: 3, name: 'Alice Johnson', course: 'DS301', grade: 94, avatar: 'AJ' },
      { id: 4, name: 'Bob Wilson', course: 'WD401', grade: 92, avatar: 'BW' },
      { id: 5, name: 'Carol Brown', course: 'CS101', grade: 91, avatar: 'CB' },
    ],
    strugglingStudents: [
      { id: 6, name: 'David Lee', course: 'DB201', grade: 45, avatar: 'DL' },
      { id: 7, name: 'Emma Davis', course: 'DS301', grade: 52, avatar: 'ED' },
      { id: 8, name: 'Frank Miller', course: 'WD401', grade: 58, avatar: 'FM' },
    ],
    loading: false,
  });

  useEffect(() => {
    loadAnalytics();
  }, [selectedCourse]);

  const loadAnalytics = async () => {
    try {
      // For now using mock data. Replace with actual API call:
      // const response = await lecturerApi.getStudentAnalytics({ courseId: selectedCourse });
      console.log('Loading analytics for course:', selectedCourse);
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  };

  const performanceData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
    datasets: [
      {
        label: 'Average Grade',
        data: [78, 82, 85, 83, 87, 85],
        borderColor: '#3b82f6',
        backgroundColor: '#3b82f615',
        tension: 0.4,
      },
      {
        label: 'Completion Rate',
        data: [85, 88, 90, 87, 92, 89],
        borderColor: '#10b981',
        backgroundColor: '#10b98115',
        tension: 0.4,
      },
    ],
  };

  const gradeDistribution = {
    labels: ['A (90-100)', 'B (80-89)', 'C (70-79)', 'D (60-69)', 'F (0-59)'],
    datasets: [
      {
        data: [35, 45, 38, 15, 10],
        backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#6b7280'],
        borderWidth: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  const StatCard = ({ title, value, icon, color, trend }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card sx={{ height: '100%', border: '1px solid #f1f5f9' }}>
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {title}
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={color}>
                {value}
              </Typography>
              {trend && (
                <Box display="flex" alignItems="center" mt={1}>
                  {trend > 0 ? (
                    <TrendingUpOutlined sx={{ fontSize: 16, color: '#10b981', mr: 0.5 }} />
                  ) : (
                    <TrendingDownOutlined sx={{ fontSize: 16, color: '#ef4444', mr: 0.5 }} />
                  )}
                  <Typography variant="caption" color={trend > 0 ? '#10b981' : '#ef4444'}>
                    {Math.abs(trend)}% from last week
                  </Typography>
                </Box>
              )}
            </Box>
            <Avatar
              sx={{
                bgcolor: `${color}15`,
                color: color,
                width: 56,
                height: 56,
              }}
            >
              {icon}
            </Avatar>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: "#fafbfc", minHeight: "100vh" }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box mb={4}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight="800"
                sx={{
                  background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                }}
              >
                Student Analytics
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
                Track student performance, engagement, and progress across your courses.
              </Typography>
            </Box>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Course</InputLabel>
              <Select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                label="Course"
              >
                {analyticsData.courses.map((course) => (
                  <MenuItem key={course.id} value={course.id}>
                    {course.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Students"
            value={analyticsData.overview.totalStudents}
            icon={<GroupOutlined />}
            color="#3b82f6"
            trend={5}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Average Grade"
            value={`${analyticsData.overview.averageGrade}%`}
            icon={<SchoolOutlined />}
            color="#10b981"
            trend={2}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Completion Rate"
            value={`${analyticsData.overview.completionRate}%`}
            icon={<AssignmentOutlined />}
            color="#f59e0b"
            trend={-1}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Students"
            value={analyticsData.overview.activeStudents}
            icon={<AnalyticsOutlined />}
            color="#8b5cf6"
            trend={3}
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card sx={{ border: "1px solid #f1f5f9" }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3}>
                Performance Trends
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line data={performanceData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ border: "1px solid #f1f5f9" }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3}>
                Grade Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut data={gradeDistribution} options={doughnutOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Student Lists */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card sx={{ border: "1px solid #f1f5f9" }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3} color="#10b981">
                Top Performers
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student</TableCell>
                      <TableCell>Course</TableCell>
                      <TableCell>Grade</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.topPerformers.map((student) => (
                      <TableRow key={student.id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar sx={{ bgcolor: "#10b981", width: 32, height: 32 }}>
                              {student.avatar}
                            </Avatar>
                            <Typography variant="body2" fontWeight="medium">
                              {student.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={student.course} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${student.grade}%`}
                            size="small"
                            sx={{
                              bgcolor: "#10b98115",
                              color: "#10b981",
                              fontWeight: 600,
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ border: "1px solid #f1f5f9" }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3} color="#ef4444">
                Students Needing Support
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student</TableCell>
                      <TableCell>Course</TableCell>
                      <TableCell>Grade</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.strugglingStudents.map((student) => (
                      <TableRow key={student.id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar sx={{ bgcolor: "#ef4444", width: 32, height: 32 }}>
                              {student.avatar}
                            </Avatar>
                            <Typography variant="body2" fontWeight="medium">
                              {student.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={student.course} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${student.grade}%`}
                            size="small"
                            sx={{
                              bgcolor: "#ef444415",
                              color: "#ef4444",
                              fontWeight: 600,
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            variant="outlined"
                            sx={{
                              textTransform: "none",
                              borderColor: "#3b82f6",
                              color: "#3b82f6",
                            }}
                          >
                            Contact
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default StudentAnalytics;
