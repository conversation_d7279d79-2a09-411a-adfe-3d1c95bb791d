import { createContext, useContext, useState } from "react";
import PropTypes from "prop-types";

const LecturerContext = createContext();

export const useLecturerContext = () => {
  const context = useContext(LecturerContext);
  if (!context) {
    throw new Error("useLecturerContext must be used within a LecturerProvider");
  }
  return context;
};

export function LecturerProvider({ children }) {
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const value = {
    selectedCourse,
    setSelectedCourse,
    selectedAssessment,
    setSelectedAssessment,
    sidebarOpen,
    setSidebarOpen,
  };

  return (
    <LecturerContext.Provider value={value}>
      {children}
    </LecturerContext.Provider>
  );
}

LecturerProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
