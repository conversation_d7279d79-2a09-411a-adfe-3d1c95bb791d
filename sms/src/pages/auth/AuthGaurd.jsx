import useAuth from "@hooks/useAuth";
import { Navigate, useLocation } from "react-router-dom";

function AuthGuard({ children, allowedRoles }) {
  const { role } = useAuth();
  const { pathname } = useLocation();

  if (!role) {
    if (pathname?.includes("admin")) {
      return <Navigate to="/admin/login" replace />;
    }
    return <Navigate to="/login" replace />;
  }

  if (!allowedRoles.includes(role)) {
    return <Navigate to={`/${role}`} replace />;
  }

  return <>{children}</>;
}

export default AuthGuard;
