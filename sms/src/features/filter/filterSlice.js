import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  query: "",
  filter: {
    academic_year: null,
    level: 1,
    semester: 1,
  },
};

export const filterSlice = createSlice({
  name: "filter",
  initialState,
  reducers: {
    setFilter: (state, { payload }) => {
      const { academic_year, level, semester } = payload;
      // if any value is null then use the previous value
      state.filter.academic_year = academic_year ?? state.filter.academic_year;
      state.filter.level = level ?? state.filter.level;
      state.filter.semester = semester ?? state.filter.semester;
      // console.log(state.filter);
    },
    setQuery: (state, { payload }) => {
      state.query = payload;
      // console.log(state.query);
    },
  },
});

export const { setFilter, setQuery } = filterSlice.actions;

export const filterReducer = filterSlice.reducer;
