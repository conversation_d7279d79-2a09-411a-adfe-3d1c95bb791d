import { LocalStorage } from "@lib/util";
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  user: LocalStorage.load("user") ?? null,
  token: LocalStorage.load("token") ?? null,
  role: LocalStorage.load("role") ?? null,
  data: LocalStorage.load("data") ?? null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, { payload }) => {
      const { user, token, role, data } = payload;
      state.user = user;
      state.token = token;
      state.role = role;
      state.data = data;
      LocalStorage.save("role", role);
      LocalStorage.save("user", user);
      LocalStorage.save("token", token);
      LocalStorage.save("data", data);
    },
    logout: (state) => {
      state.token = null;
      state.user = null;
      state.data = null;
      LocalStorage.remove("user");
      LocalStorage.remove("token");
    },
  },
});

export const { setUser, logout } = authSlice.actions;

export const authReducer = authSlice.reducer;
