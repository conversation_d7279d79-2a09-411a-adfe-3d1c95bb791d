import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  current_semester: null,
  current_academic_year_id: null,
  current_academic_year_name: null,
};

export const systemSlice = createSlice({
  name: "system",
  initialState,
  reducers: {
    setSystemSettings: (state, { payload }) => {
      const {
        current_semester,
        current_academic_year_id,
        current_academic_year_name,
      } = payload;

      state.current_semester = current_semester;
      state.current_academic_year_id = current_academic_year_id;
      state.current_academic_year_name = current_academic_year_name;
    },
  },
});

export const { setSystemSettings } = systemSlice.actions;

export const systemReducer = systemSlice.reducer;
