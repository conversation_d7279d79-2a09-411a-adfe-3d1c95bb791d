import { preparedHeaders } from "@app/services/helper.service";
import { BASE_URL } from "@lib/config";
import { FormWizard } from "@lib/util";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    studentLogin: builder.mutation({
      query: (body) => ({ url: "/student/login", body, method: "POST" }),
    }),
    adminLogin: builder.mutation({
      query: (body) => ({ url: "/admin/login", body, method: "POST" }),
    }),
    lecturerLogin: builder.mutation({
      query: (body) => ({ url: "/lecturer/login", body, method: "POST" }),
    }),
    logout: builder.mutation({
      query: () => ({ url: "/logout", method: "POST" }),
    }),

    updateProfile: builder.mutation({
      query: (formData) => FormWizard.postFormData("users/update", formData),
    }),

    updateProfileImage: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("users/profile-image", formData),
    }),

    // Forgot Password endpoints
    forgotPassword: builder.mutation({
      query: (body) => ({ url: "/forgot-password", body, method: "POST" }),
    }),

    resetPassword: builder.mutation({
      query: (body) => ({ url: "/reset-password", body, method: "POST" }),
    }),

    // Role-specific forgot password endpoints
    studentForgotPassword: builder.mutation({
      query: (body) => ({ url: "/student/forgot-password", body, method: "POST" }),
    }),

    lecturerForgotPassword: builder.mutation({
      query: (body) => ({ url: "/lecturer/forgot-password", body, method: "POST" }),
    }),

    adminForgotPassword: builder.mutation({
      query: (body) => ({ url: "/admin/forgot-password", body, method: "POST" }),
    }),
  }),
});

export const {
  useStudentLoginMutation,
  useAdminLoginMutation,
  useLecturerLoginMutation,
  useLogoutMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useStudentForgotPasswordMutation,
  useLecturerForgotPasswordMutation,
  useAdminForgotPasswordMutation,
} = authApi;
