import { preparedHeaders } from "@app/services/helper.service";
import { BASE_URL } from "@lib/config";
import { FormWizard } from "@lib/util";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const adminApi = createApi({
  reducerPath: "adminApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    importPrograms: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("programs/import-programs", formData),
    }),

    getAllPrograms: builder.query({
      query: () => ({ url: "programs" }),
    }),
    getProgramById: builder.query({
      query: (programId) => ({ url: `programs/${programId}` }),
    }),

    //delete program
    deleteProgram: builder.mutation({
      query: (programId) => ({
        url: `programs/${programId}`,
        method: "DELETE",
      }),
    }),

    getEditProgramForm: builder.query({
      query: (programId) => ({ url: `programs/${programId}/edit` }),
    }),

    createProgram: builder.mutation({
      query: (program) => ({ url: "programs", method: "POST", body: program }),
    }),

    updateProgram: builder.mutation({
      query: ({ id, body }) => ({
        url: `programs/${id}`,
        method: "POST",
        body,
      }),
    }),

    importSchools: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("schools/import-schools", formData),
    }),

    getAllSchools: builder.query({
      query: () => ({ url: "schools" }),
    }),

    getSingleSchool: builder.query({
      query: (id) => ({ url: `schools/${id}` }),
    }),

    createSchool: builder.mutation({
      query: (formData) => FormWizard.postFormData("schools", formData),
    }),

    deleteSchool: builder.mutation({
      query: (schoolId) => ({
        url: `schools/${schoolId}`,
        method: "DELETE",
      }),
    }),

    updateSchool: builder.mutation({
      query: ({ id, body }) => ({
        url: `schools/${id}`,
        method: "PUT",
        body,
      }),
    }),

    getSchoolById: builder.query({
      query: (schoolId) => ({ url: `schools/${schoolId}` }),
    }),

    importDepartments: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("departments/import-departments", formData),
    }),

    getAllDepartments: builder.query({
      query: (page = 1) => ({ url: `departments/?page=${page}` }),
    }),

    getDepartmentById: builder.query({
      query: (departmentId) => ({ url: `departments/${departmentId}` }),
    }),

    createDepartment: builder.mutation({
      query: (department) => ({
        url: "departments",
        method: "POST",
        body: department,
      }),
    }),
    // update a department
    updateDepartment: builder.mutation({
      query: ({ id, body }) => ({
        url: `departments/${id}`,
        method: "PUT",
        body,
      }),
    }),
    // delete a department
    deleteDepartment: builder.mutation({
      query: (departmentId) => ({
        url: `departments/${departmentId}`,
        method: "DELETE",
      }),
    }),
    // get department by school id
    getDepartmentBySchoolId: builder.query({
      query: (schoolId) => ({ url: `departments/school/${schoolId}` }),
    }),

    // search department, departments/search?query=Department%3
    searchDepartments: builder.query({
      query: (query) => ({ url: `departments/search?query=${query}` }),
    }),

    importLecturers: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("lecturers/import-lecturers", formData),
    }),
    // get all lecturers
    getAllLecturers: builder.query({
      query: () => ({ url: `lecturers` }),
    }),
    // get lecturer by id
    getLecturerById: builder.query({
      query: (lecturerId) => ({ url: `lecturers/${lecturerId}` }),
    }),
    // create a new lecturer
    createLecturer: builder.mutation({
      query: (lecturer) => FormWizard.postFormData("lecturers", lecturer),
    }),
    // update a lecturer
    updateLecturer: builder.mutation({
      query: ({ id, body }) => FormWizard.postFormData(`lecturers/${id}`, body),
    }),
    // delete a lecturer
    deleteLecturer: builder.mutation({
      query: (lecturerId) => ({
        url: `lecturers/${lecturerId}`,
        method: "DELETE",
      }),
    }),
    // get lecturer by department id
    getLecturerByDepartmentId: builder.query({
      query: (departmentId) => ({
        url: `lecturers/department/${departmentId}`,
      }),
    }),

    importLevels: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("levels/import-levels", formData),
    }),
    // get all levels
    getAllLevels: builder.query({
      query: () => ({ url: "levels" }),
    }),
    // get level by id
    getLevelById: builder.query({
      query: (levelId) => ({ url: `levels/${levelId}` }),
    }),
    // create a new level
    createLevel: builder.mutation({
      query: (level) => ({
        url: "levels",
        method: "POST",
        body: level,
      }),
    }),
    // update a level
    updateLevel: builder.mutation({
      query: ({ id, body }) => ({
        url: `levels/${id}`,
        method: "PUT",
        body,
      }),
    }),

    // delete a level
    deleteLevel: builder.mutation({
      query: (levelId) => ({
        url: `levels/${levelId}`,
        method: "DELETE",
      }),
    }),
    // get level by department id
    getLevelByDepartmentId: builder.query({
      query: (departmentId) => ({
        url: `levels/department/${departmentId}`,
      }),
    }),

    importCourses: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("courses/import-courses", formData),
    }),
    // get all courses
    getAllCourses: builder.query({
      query: (query) => ({ url: `courses?${query}` }),
    }),
    // get course by id
    getCourseById: builder.query({
      query: (courseId) => ({ url: `courses/${courseId}` }),
    }),
    // create a new course
    createCourse: builder.mutation({
      query: (course) => ({
        url: "courses",
        method: "POST",
        body: course,
      }),
    }),
    // update a course
    updateCourse: builder.mutation({
      query: ({ id, body }) => ({
        url: `courses/${id}`,
        method: "PUT",
        body,
      }),
    }),

    // delete a course
    deleteCourse: builder.mutation({
      query: (courseId) => ({
        url: `courses/${courseId}`,
        method: "DELETE",
      }),
    }),
    // get course by department id
    getCourseByDepartmentId: builder.query({
      query: (departmentId) => ({
        url: `courses/department/${departmentId}`,
      }),
    }),

    importStudents: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("students/import-students", formData),
    }),
    // get all students
    getAllStudents: builder.query({
      query: (page) => ({ url: `students?page=${page}` }),
    }),
    // get student by id
    getStudentById: builder.query({
      query: (studentId) => ({ url: `students/${studentId}` }),
    }),

    createStudent: builder.mutation({
      query: (student) => FormWizard.postFormData("students", student),
    }),

    updateStudent: builder.mutation({
      query: ({ id, body }) => ({
        url: `students/${id}`,
        method: "PUT",
        body,
      }),
    }),

    deleteStudent: builder.mutation({
      query: (studentId) => ({
        url: `students/${studentId}`,
        method: "DELETE",
      }),
    }),
    getStudentByDepartmentId: builder.query({
      query: (departmentId) => ({
        url: `students/department/${departmentId}`,
      }),
    }),

    importCaMarks: builder.mutation({
      query: (formData) => FormWizard.postFormData("import-ca-marks", formData),
    }),

    updateCaMarks: builder.mutation({
      query: ({ studentId, caMarks }) => ({
        url: `students/${studentId}/ca-marks`,
        method: "PUT",
        body: caMarks,
      }),
    }),

    uodateCaNarks: builder.mutation({
      query: (formData) => FormWizard.postFormData("ca-marks", formData),
    }),
    importExamMarks: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("import-exam-marks", formData),
    }),

    updateExamMarks: builder.mutation({
      query: ({ studentId, examMarks }) => ({
        url: `students/${studentId}/exam-marks`,
        method: "PUT",
        body: examMarks,
      }),
    }),

    getAllAcademicYears: builder.query({
      query: () => ({ url: "academic-years" }),
      providesTags: ["academic-years"],
    }),
    // get academic-year by id
    getAcademicYearById: builder.query({
      query: (academicYearId) => ({ url: `academic-years/${academicYearId}` }),
    }),

    createAcademicYear: builder.mutation({
      query: (academicYear) => ({
        url: "academic-years",
        method: "POST",
        body: academicYear,
      }),
      invalidatesTags: ["academic-years"],
    }),

    updateAcademicYear: builder.mutation({
      query: ({ id, body }) => ({
        url: `academic-years/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["academic-years"],
    }),

    deleteAcademicYear: builder.mutation({
      query: (academicYearId) => ({
        url: `academic-years/${academicYearId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["academic-years"],
    }),
    searchAcademicYears: builder.query({
      query: (query) => ({ url: `/academic-years/search?query=${query}` }),
    }),

    getAllFeeTypes: builder.query({
      query: () => ({ url: "fee-types" }),
    }),
    getFeeTypeById: builder.query({
      query: (feeTypeId) => ({ url: `fee-types/${feeTypeId}` }),
    }),

    createFeeType: builder.mutation({
      query: (feeType) => ({
        url: "fee-types",
        method: "POST",
        body: feeType,
      }),
    }),
    // update a fee-type
    updateFeeType: builder.mutation({
      query: ({ id, body }) => ({
        url: `fee-types/${id}`,
        method: "PUT",
        body,
      }),
    }),
    // delete a fee-type
    deleteFeeType: builder.mutation({
      query: (feeTypeId) => ({
        url: `fee-types/${feeTypeId}`,
        method: "DELETE",
      }),
    }),

    // search a fee-type
    searchFeeTypes: builder.query({
      query: (query) => ({ url: `/fee-types/search?query=${query}` }),
    }),

    // upload results import-exam-marks
    importResults: builder.mutation({
      query: (formData) =>
        FormWizard.postFormData("import-exam-marks", formData),
    }),
    // post results esults/exam-mark
    postResults: builder.mutation({
      query: (body) => ({
        url: `results/exam-marks`,
        method: "POST",
        body,
      }),
    }),
    // results/exam-marks/
    getExamMarks: builder.query({
      query: (query) => ({
        url: `results/exam-marks?${query}`,
      }),
    }),

    getAdminStudentExamMarks: builder.query({
      query: (query) => ({
        url: `results?${query}`,
      }),
    }),

    getCaMarks: builder.query({
      query: (query) => ({
        url: `results/ca-marks/?${query}`,
      }),
    }),

    getStatistics: builder.query({
      query: () => ({
        url: `statistics`,
      }),
    }),

    getSystemSettings: builder.query({
      query: () => ({
        url: `system`,
      }),
    }),
    updateSystemSettings: builder.mutation({
      query: (body) => ({
        url: `system`,
        body,
        method: "PUT",
      }),
    }),

    // Calendar Events
    getAllCalendarEvents: builder.query({
      query: (query) => ({
        url: `calendar-events?${query}`,
      }),
      providesTags: ["calendar-events"],
    }),

    getCalendarEventById: builder.query({
      query: (id) => ({
        url: `calendar-events/${id}`,
      }),
    }),

    createCalendarEvent: builder.mutation({
      query: (event) => ({
        url: "calendar-events",
        method: "POST",
        body: event,
      }),
      invalidatesTags: ["calendar-events"],
    }),

    createBulkCalendarEvents: builder.mutation({
      query: (events) => ({
        url: "calendar-events",
        method: "POST",
        body: { events },
      }),
      invalidatesTags: ["calendar-events"],
    }),

    updateCalendarEvent: builder.mutation({
      query: ({ id, body }) => ({
        url: `calendar-events/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["calendar-events"],
    }),

    deleteCalendarEvent: builder.mutation({
      query: (id) => ({
        url: `calendar-events/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["calendar-events"],
    }),
  }),
});

export const {
  useGetSystemSettingsQuery,
  useUpdateSystemSettingsMutation,
  //stats
  useGetStatisticsQuery,

  // Calendar Events
  useGetAllCalendarEventsQuery,
  useGetCalendarEventByIdQuery,
  useCreateCalendarEventMutation,
  useCreateBulkCalendarEventsMutation,
  useUpdateCalendarEventMutation,
  useDeleteCalendarEventMutation,

  // results hooks
  usePostResultsMutation,
  useImportResultsMutation,
  useGetExamMarksQuery,
  useGetAdminStudentExamMarksQuery,
  useGetCaMarksQuery,

  // programs
  useImportProgramsMutation,
  useGetAllProgramsQuery,
  useGetProgramByIdQuery,
  useDeleteProgramMutation,
  useUpdateProgramMutation,

  // level
  useCreateLevelMutation,
  useDeleteLevelMutation,
  useGetAllLevelsQuery,
  useGetLevelByIdQuery,
  useGetLevelByDepartmentIdQuery,
  useImportLevelsMutation,
  useUpdateLevelMutation,

  // course
  useImportCoursesMutation,
  useUpdateCourseMutation,
  useGetAllCoursesQuery,
  useGetCourseByIdQuery,
  useDeleteCourseMutation,
  useCreateCourseMutation,

  // student
  useUpdateStudentMutation,
  useImportStudentsMutation,
  useCreateStudentMutation,
  useDeleteStudentMutation,
  useGetStudentByDepartmentIdQuery,
  useGetStudentByIdQuery,
  useGetAllStudentsQuery,

  // exam
  useImportCaMarksMutation,
  useImportExamMarksMutation,
  useUpdateCaMarksMutation,
  useUpdateExamMarksMutation,

  // academic-year hooks
  useGetAllAcademicYearsQuery,
  useGetAcademicYearByIdQuery,
  useCreateAcademicYearMutation,
  useUpdateAcademicYearMutation,
  useDeleteAcademicYearMutation,
  useSearchAcademicYearsQuery,

  // fee-type hooks
  useGetAllFeeTypesQuery,
  useGetFeeTypeByIdQuery,
  useCreateFeeTypeMutation,
  useUpdateFeeTypeMutation,
  useDeleteFeeTypeMutation,
  useSearchFeeTypesQuery,
  useCreateProgramMutation,

  // school
  useCreateSchoolMutation,
  useGetAllSchoolsQuery,
  useGetSingleSchoolQuery,
  useImportSchoolsMutation,
  useDeleteSchoolMutation,
  useUpdateSchoolMutation,

  // department hooks
  useCreateDepartmentMutation,
  useGetAllDepartmentsQuery,
  useGetDepartmentByIdQuery,
  useUpdateDepartmentMutation,
  useDeleteDepartmentMutation,
  useSearchDepartmentsQuery,
  useImportDepartmentsMutation,

  // use get all lecturers
  useGetAllLecturersQuery,
  useGetLecturerByIdQuery,
  useDeleteLecturerMutation,
  useUpdateLecturerMutation,
  useCreateLecturerMutation,
  useImportLecturersMutation,
} = adminApi;
