import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const baseQuery = fetchBaseQuery({
  baseUrl: `${import.meta.env.VITE_BASE_URL}/api`,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.token;
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

export const lmsApi = createApi({
  reducerPath: "lmsApi",
  baseQuery,
  tagTypes: [
    "CourseMaterials",
    "Assignments", 
    "AssignmentSubmissions",
    "Assessments",
    "AssessmentAttempts",
    "Notifications",
    "LecturerCourses",
    "LecturerStats"
  ],
  endpoints: (builder) => ({
    // Course Materials
    getCourseMaterials: builder.query({
      query: (courseId) => `/courses/${courseId}/materials`,
      providesTags: ["CourseMaterials"],
    }),

    uploadCourseMaterial: builder.mutation({
      query: ({ courseId, formData }) => ({
        url: `/courses/${courseId}/materials`,
        method: "POST",
        body: formData,
      }),
      invalidatesTags: ["CourseMaterials"],
    }),

    deleteCourseMaterial: builder.mutation({
      query: ({ courseId, materialId }) => ({
        url: `/courses/${courseId}/materials/${materialId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["CourseMaterials"],
    }),

    downloadCourseMaterial: builder.query({
      query: ({ courseId, materialId }) => `/courses/${courseId}/materials/${materialId}/download`,
    }),

    // Assignments
    getCourseAssignments: builder.query({
      query: (courseId) => `/courses/${courseId}/assignments`,
      providesTags: ["Assignments"],
    }),

    getAssignment: builder.query({
      query: ({ courseId, assignmentId }) => `/courses/${courseId}/assignments/${assignmentId}`,
      providesTags: ["Assignments"],
    }),

    createAssignment: builder.mutation({
      query: ({ courseId, assignmentData }) => ({
        url: `/courses/${courseId}/assignments`,
        method: "POST",
        body: assignmentData,
      }),
      invalidatesTags: ["Assignments"],
    }),

    updateAssignment: builder.mutation({
      query: ({ courseId, assignmentId, assignmentData }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}`,
        method: "PUT",
        body: assignmentData,
      }),
      invalidatesTags: ["Assignments"],
    }),

    deleteAssignment: builder.mutation({
      query: ({ courseId, assignmentId }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Assignments"],
    }),

    publishAssignment: builder.mutation({
      query: ({ courseId, assignmentId }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}/publish`,
        method: "POST",
      }),
      invalidatesTags: ["Assignments"],
    }),

    getAssignmentSubmissions: builder.query({
      query: ({ courseId, assignmentId }) => `/courses/${courseId}/assignments/${assignmentId}/submissions`,
      providesTags: ["AssignmentSubmissions"],
    }),

    // Assignment Submissions
    submitAssignment: builder.mutation({
      query: ({ courseId, assignmentId, submissionData }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}/submissions`,
        method: "POST",
        body: submissionData,
      }),
      invalidatesTags: ["AssignmentSubmissions"],
    }),

    getSubmission: builder.query({
      query: ({ courseId, assignmentId, submissionId }) => 
        `/courses/${courseId}/assignments/${assignmentId}/submissions/${submissionId}`,
      providesTags: ["AssignmentSubmissions"],
    }),

    updateSubmission: builder.mutation({
      query: ({ courseId, assignmentId, submissionId, submissionData }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}/submissions/${submissionId}`,
        method: "PUT",
        body: submissionData,
      }),
      invalidatesTags: ["AssignmentSubmissions"],
    }),

    gradeSubmission: builder.mutation({
      query: ({ courseId, assignmentId, submissionId, gradeData }) => ({
        url: `/courses/${courseId}/assignments/${assignmentId}/submissions/${submissionId}`,
        method: "PUT",
        body: gradeData,
      }),
      invalidatesTags: ["AssignmentSubmissions"],
    }),

    getMySubmissions: builder.query({
      query: () => "/student/submissions",
      providesTags: ["AssignmentSubmissions"],
    }),

    // Assessments
    getCourseAssessments: builder.query({
      query: (courseId) => `/courses/${courseId}/assessments`,
      providesTags: ["Assessments"],
    }),

    getAssessment: builder.query({
      query: ({ courseId, assessmentId }) => `/courses/${courseId}/assessments/${assessmentId}`,
      providesTags: ["Assessments"],
    }),

    createAssessment: builder.mutation({
      query: ({ courseId, assessmentData }) => ({
        url: `/courses/${courseId}/assessments`,
        method: "POST",
        body: assessmentData,
      }),
      invalidatesTags: ["Assessments"],
    }),

    updateAssessment: builder.mutation({
      query: ({ courseId, assessmentId, assessmentData }) => ({
        url: `/courses/${courseId}/assessments/${assessmentId}`,
        method: "PUT",
        body: assessmentData,
      }),
      invalidatesTags: ["Assessments"],
    }),

    publishAssessment: builder.mutation({
      query: ({ courseId, assessmentId }) => ({
        url: `/courses/${courseId}/assessments/${assessmentId}/publish`,
        method: "POST",
      }),
      invalidatesTags: ["Assessments"],
    }),

    // Assessment Attempts
    startAssessment: builder.mutation({
      query: ({ courseId, assessmentId }) => ({
        url: `/courses/${courseId}/assessments/${assessmentId}/attempts`,
        method: "POST",
      }),
      invalidatesTags: ["AssessmentAttempts"],
    }),

    submitAssessment: builder.mutation({
      query: ({ courseId, assessmentId, attemptId, answers }) => ({
        url: `/courses/${courseId}/assessments/${assessmentId}/attempts/${attemptId}/submit`,
        method: "POST",
        body: { answers },
      }),
      invalidatesTags: ["AssessmentAttempts"],
    }),

    getAssessmentAttempt: builder.query({
      query: ({ courseId, assessmentId, attemptId }) => 
        `/courses/${courseId}/assessments/${assessmentId}/attempts/${attemptId}`,
      providesTags: ["AssessmentAttempts"],
    }),

    // Notifications
    getNotifications: builder.query({
      query: (params = {}) => ({
        url: "/notifications",
        params,
      }),
      providesTags: ["Notifications"],
    }),

    getUnreadNotificationCount: builder.query({
      query: () => "/notifications/unread-count",
      providesTags: ["Notifications"],
    }),

    markNotificationAsRead: builder.mutation({
      query: (notificationId) => ({
        url: `/notifications/${notificationId}/read`,
        method: "PUT",
      }),
      invalidatesTags: ["Notifications"],
    }),

    markAllNotificationsAsRead: builder.mutation({
      query: () => ({
        url: "/notifications/mark-all-read",
        method: "PUT",
      }),
      invalidatesTags: ["Notifications"],
    }),

    deleteNotification: builder.mutation({
      query: (notificationId) => ({
        url: `/notifications/${notificationId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Notifications"],
    }),

    // Lecturer specific endpoints
    getLecturerCourses: builder.query({
      query: (lecturerId) => `/lecturer/${lecturerId}/courses`,
      providesTags: ["LecturerCourses"],
    }),

    getLecturerStats: builder.query({
      query: (lecturerId) => `/lecturer/${lecturerId}/stats`,
      providesTags: ["LecturerStats"],
    }),

    // File Management
    uploadFile: builder.mutation({
      query: (formData) => ({
        url: "/files/upload-single",
        method: "POST",
        body: formData,
      }),
    }),

    uploadMultipleFiles: builder.mutation({
      query: (formData) => ({
        url: "/files/upload",
        method: "POST",
        body: formData,
      }),
    }),

    deleteFile: builder.mutation({
      query: (filePath) => ({
        url: "/files/delete",
        method: "DELETE",
        body: { path: filePath },
      }),
    }),

    downloadFile: builder.query({
      query: (filePath) => ({
        url: "/files/download",
        params: { path: filePath },
      }),
    }),
  }),
});

export const {
  // Course Materials
  useGetCourseMaterialsQuery,
  useUploadCourseMaterialMutation,
  useDeleteCourseMaterialMutation,
  useDownloadCourseMaterialQuery,

  // Assignments
  useGetCourseAssignmentsQuery,
  useGetAssignmentQuery,
  useCreateAssignmentMutation,
  useUpdateAssignmentMutation,
  useDeleteAssignmentMutation,
  usePublishAssignmentMutation,
  useGetAssignmentSubmissionsQuery,

  // Assignment Submissions
  useSubmitAssignmentMutation,
  useGetSubmissionQuery,
  useUpdateSubmissionMutation,
  useGradeSubmissionMutation,
  useGetMySubmissionsQuery,

  // Assessments
  useGetCourseAssessmentsQuery,
  useGetAssessmentQuery,
  useCreateAssessmentMutation,
  useUpdateAssessmentMutation,
  usePublishAssessmentMutation,

  // Assessment Attempts
  useStartAssessmentMutation,
  useSubmitAssessmentMutation,
  useGetAssessmentAttemptQuery,

  // Notifications
  useGetNotificationsQuery,
  useGetUnreadNotificationCountQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
  useDeleteNotificationMutation,

  // Lecturer
  useGetLecturerCoursesQuery,
  useGetLecturerStatsQuery,

  // File Management
  useUploadFileMutation,
  useUploadMultipleFilesMutation,
  useDeleteFileMutation,
  useDownloadFileQuery,
} = lmsApi;
