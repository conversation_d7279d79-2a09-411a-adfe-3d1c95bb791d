import { preparedHeaders } from "@app/services/helper.service";
import { BASE_URL } from "@lib/config";
import { FormWizard } from "@lib/util";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const lecturerApi = createApi({
  reducerPath: "lecturerApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  tagTypes: ['Course', 'Assignment', 'Assessment', 'Student', 'Material'],
  endpoints: (builder) => ({
    // Dashboard
    getDashboardStats: builder.query({
      query: () => "/lecturer/dashboard/stats",
    }),
    getRecentActivities: builder.query({
      query: () => "/lecturer/dashboard/activities",
    }),

    // Courses
    getLecturerCourses: builder.query({
      query: (params) => ({
        url: "/lecturer/courses",
        params,
      }),
      providesTags: ['Course'],
    }),
    getLecturerCourseById: builder.query({
      query: (id) => `/lecturer/courses/${id}`,
      providesTags: (result, error, id) => [{ type: 'Course', id }],
    }),
    createLecturerCourse: builder.mutation({
      query: (data) => ({
        url: "/lecturer/courses",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ['Course'],
    }),
    updateLecturerCourse: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `/lecturer/courses/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Course', id }],
    }),
    deleteLecturerCourse: builder.mutation({
      query: (id) => ({
        url: `/lecturer/courses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ['Course'],
    }),
    getCourseStudents: builder.query({
      query: (id) => `/lecturer/courses/${id}/students`,
      providesTags: (result, error, id) => [{ type: 'Student', id: `course-${id}` }],
    }),

    // Course Materials
    getCourseMaterials: builder.query({
      query: (courseId) => `/lecturer/courses/${courseId}/materials`,
      providesTags: (result, error, courseId) => [{ type: 'Material', id: `course-${courseId}` }],
    }),
    uploadMaterial: builder.mutation({
      query: ({ courseId, formData }) => FormWizard.postFormData(`lecturer/courses/${courseId}/materials`, formData),
      invalidatesTags: (result, error, { courseId }) => [{ type: 'Material', id: `course-${courseId}` }],
    }),
    updateMaterial: builder.mutation({
      query: ({ courseId, materialId, ...data }) => ({
        url: `/lecturer/courses/${courseId}/materials/${materialId}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { courseId }) => [{ type: 'Material', id: `course-${courseId}` }],
    }),
    deleteMaterial: builder.mutation({
      query: ({ courseId, materialId }) => ({
        url: `/lecturer/courses/${courseId}/materials/${materialId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { courseId }) => [{ type: 'Material', id: `course-${courseId}` }],
    }),

    // Assignments
    getLecturerAssignments: builder.query({
      query: (params) => ({
        url: "/lecturer/assignments",
        params,
      }),
      providesTags: ['Assignment'],
    }),
    getLecturerAssignmentById: builder.query({
      query: (id) => `/lecturer/assignments/${id}`,
      providesTags: (result, error, id) => [{ type: 'Assignment', id }],
    }),
    createLecturerAssignment: builder.mutation({
      query: (data) => ({
        url: "/lecturer/assignments",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ['Assignment'],
    }),
    updateLecturerAssignment: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `/lecturer/assignments/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Assignment', id }],
    }),
    deleteLecturerAssignment: builder.mutation({
      query: (id) => ({
        url: `/lecturer/assignments/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ['Assignment'],
    }),
    getAssignmentSubmissions: builder.query({
      query: (id) => `/lecturer/assignments/${id}/submissions`,
    }),
    gradeSubmission: builder.mutation({
      query: ({ assignmentId, submissionId, ...data }) => ({
        url: `/lecturer/assignments/${assignmentId}/submissions/${submissionId}/grade`,
        method: "PUT",
        body: data,
      }),
    }),

    // Assessments
    getLecturerAssessments: builder.query({
      query: (params) => ({
        url: "/lecturer/assessments",
        params,
      }),
      providesTags: ['Assessment'],
    }),
    getLecturerAssessmentById: builder.query({
      query: (id) => `/lecturer/assessments/${id}`,
      providesTags: (result, error, id) => [{ type: 'Assessment', id }],
    }),
    createLecturerAssessment: builder.mutation({
      query: (data) => ({
        url: "/lecturer/assessments",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ['Assessment'],
    }),
    updateLecturerAssessment: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `/lecturer/assessments/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Assessment', id }],
    }),
    deleteLecturerAssessment: builder.mutation({
      query: (id) => ({
        url: `/lecturer/assessments/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ['Assessment'],
    }),
    getAssessmentResults: builder.query({
      query: (id) => `/lecturer/assessments/${id}/results`,
    }),

    // Analytics
    getStudentAnalytics: builder.query({
      query: (params) => ({
        url: "/lecturer/analytics/students",
        params,
      }),
    }),
    getCoursePerformance: builder.query({
      query: (courseId) => `/lecturer/analytics/courses/${courseId}/performance`,
    }),

    // Profile
    getProfile: builder.query({
      query: () => "/lecturer/profile",
    }),
    updateProfile: builder.mutation({
      query: (data) => ({
        url: "/lecturer/profile",
        method: "PUT",
        body: data,
      }),
    }),
    changePassword: builder.mutation({
      query: (data) => ({
        url: "/lecturer/profile/password",
        method: "PUT",
        body: data,
      }),
    }),
    uploadProfileImage: builder.mutation({
      query: (formData) => FormWizard.postFormData("lecturer/profile/image", formData),
    }),

    // Notifications
    getNotifications: builder.query({
      query: () => "/lecturer/notifications",
    }),
    markNotificationRead: builder.mutation({
      query: (id) => ({
        url: `/lecturer/notifications/${id}/read`,
        method: "PUT",
      }),
    }),
    markAllNotificationsRead: builder.mutation({
      query: () => ({
        url: "/lecturer/notifications/read-all",
        method: "PUT",
      }),
    }),

    // Students
    getLecturerStudents: builder.query({
      query: (params) => ({
        url: "/lecturer/students",
        params,
      }),
      providesTags: ['Student'],
    }),
    getLecturerStudentById: builder.query({
      query: (id) => `/lecturer/students/${id}`,
      providesTags: (result, error, id) => [{ type: 'Student', id }],
    }),
    getLecturerStudentProgress: builder.query({
      query: ({ studentId, courseId }) => `/lecturer/students/${studentId}/progress/${courseId}`,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Dashboard
  useGetDashboardStatsQuery,
  useGetRecentActivitiesQuery,
  
  // Courses
  useGetLecturerCoursesQuery,
  useGetLecturerCourseByIdQuery,
  useCreateLecturerCourseMutation,
  useUpdateLecturerCourseMutation,
  useDeleteLecturerCourseMutation,
  useGetCourseStudentsQuery,
  
  // Course Materials
  useGetCourseMaterialsQuery,
  useUploadMaterialMutation,
  useUpdateMaterialMutation,
  useDeleteMaterialMutation,
  
  // Assignments
  useGetLecturerAssignmentsQuery,
  useGetLecturerAssignmentByIdQuery,
  useCreateLecturerAssignmentMutation,
  useUpdateLecturerAssignmentMutation,
  useDeleteLecturerAssignmentMutation,
  useGetAssignmentSubmissionsQuery,
  useGradeSubmissionMutation,
  
  // Assessments
  useGetLecturerAssessmentsQuery,
  useGetLecturerAssessmentByIdQuery,
  useCreateLecturerAssessmentMutation,
  useUpdateLecturerAssessmentMutation,
  useDeleteLecturerAssessmentMutation,
  useGetAssessmentResultsQuery,
  
  // Analytics
  useGetStudentAnalyticsQuery,
  useGetCoursePerformanceQuery,
  
  // Profile
  useGetProfileQuery,
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useUploadProfileImageMutation,
  
  // Notifications
  useGetNotificationsQuery,
  useMarkNotificationReadMutation,
  useMarkAllNotificationsReadMutation,
  
  // Students
  useGetLecturerStudentsQuery,
  useGetLecturerStudentByIdQuery,
  useGetLecturerStudentProgressQuery,
} = lecturerApi;
