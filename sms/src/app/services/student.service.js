import { preparedHeaders } from "@app/services/helper.service";
import { BASE_URL } from "@lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const studentApi = createApi({
  reducerPath: "studentApi",
  tagTypes: ["results"],
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    registerCourse: builder.mutation({
      query: (body) => ({
        url: "/course-registration/register-course",
        body,
        method: "POST",
      }),
    }),

    unregisterCourse: builder.mutation({
      query: (body) => ({
        url: "/course-registration/unregister-course",
        body,
      }),
    }),
    getRegisteredCourses: builder.query({
      query: (query) => ({
        url: `/course-registration/registered-courses?${query}`,
      }),
    }),

    downloadFormB: builder.query({
      query: (query) => ({
        url: `/results/formb/?${query}`,
      }),
    }),

    getResults: builder.query({
      query: (query) => ({
        url: `/results/?${query}`,
      }),
    }),

    getCaResults: builder.query({
      query: (query) => ({
        url: `/results/ca-marks/student?${query}`,
      }),
    }),

    getStudentExamMarks: builder.query({
      query: (query) => ({
        url: `/results?${query}`,
      }),
      providesTags: ["results"],
    }),

    generatePdf: builder.query({
      query: (query) => ({
        url: `/results/generate-pdf/?${query}`,
      }),
    }),

    getAllFeesByStudent: builder.query({
      query: (studentId) => ({
        url: `/fees/all-fees-by-student/${studentId}`,
      }),
    }),
    // download receipt
    downloadReceipt: builder.query({
      query: (transactionId) => ({
        url: `/fees/get-receipt/${transactionId}`,
      }),
    }),
  }),
});

export const {
  useRegisterCourseMutation,
  useUnregisterCourseMutation,
  useGetRegisteredCoursesQuery,
  useDownloadFormBQuery,
  useGetResultsQuery,
  useGeneratePdfQuery,
  useGetCaResultsQuery,
  useGetAllFeesByStudentQuery,
  useGetStudentExamMarksQuery,
  // useGetExamMarksQuery,
} = studentApi;
