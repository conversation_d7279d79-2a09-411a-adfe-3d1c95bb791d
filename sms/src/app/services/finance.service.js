import { preparedHeaders } from "@app/services/helper.service";
import { BASE_URL } from "@lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const financeApi = createApi({
  reducerPath: "financeApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    creatFees: builder.mutation({
      query: (body) => ({ url: "/fees", method: "POST", body }),
    }),
    getAllFees: builder.query({
      query: () => ({ url: "/fees", method: "GET" }),
    }),
    // /fees/{fee_id} to get a fee by id
    getSingleFee: builder.query({
      query: (id) => ({ url: `/fees/${id}` }),
    }),
    // /fees/{fee_id}/edit to edit a fee by id
    editFee: builder.mutation({
      query: (body, id) => ({
        url: `/fees/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /fees/search?query=fee_name to search fees by name
    searchFees: builder.query({
      query: (query) => ({ url: `/fees/search?query=${query}` }),
    }),
    // /fees/{fee_id}/register to register a fee by id
    registerFee: builder.mutation({
      query: (body) => ({
        url: `/fees`,
        body,
        method: "POST",
      }),
    }),
    // /fees/{fee_id}/ to unregister a fee by id
    unregisterFee: builder.mutation({
      query: (id) => ({
        url: `/fees/${id}`,
        method: "DELETE",
      }),
    }),

    // get all transactions for endpoint transactions/
    getAllTransactions: builder.query({
      query: () => ({ url: "/transactions", method: "GET" }),
    }),
    // get transaction by reference endpoint transactions/get-transaction-by-reference?reference=TRANS66d0d1fde8403
    getTransactionByReference: builder.query({
      query: (reference) => ({
        url: `/transactions/get-transaction-by-reference?reference=${reference}`,
      }),
    }),

    // get transaction by user id transactions/get-user-transactions?user_id=2
    getUserTransactions: builder.query({
      query: (userId) => ({
        url: `/transactions/get-user-transactions?user_id=${userId}`,
      }),
    }),

    // account section
    // /accounts to get all accounts
    getAllAccounts: builder.query({
      query: () => ({ url: "/accounts", method: "GET" }),
    }),
    // create account section
    createAccount: builder.mutation({
      query: (account) => ({ url: "/accounts", method: "POST", body: account }),
    }),
    // /accounts/{account_id} to get a account by id
    getSingleAccount: builder.query({
      query: (id) => ({ url: `/accounts/${id}` }),
    }),
    deleteAcount: builder.mutation({
      query: (id) => ({ url: `/accounts/${id}`, method: "DELETE" }),
    }),
    // /accounts/{account_id}/edit to edit a account by id
    editAccount: builder.mutation({
      query: ({ body, id }) => ({
        url: `/accounts/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /accounts/search?query=account_name to search accounts by name
    searchAccounts: builder.query({
      query: (query) => ({ url: `/accounts/search?query=${query}` }),
    }),

    // incomes section
    // /incomes to get all incomes
    createIncomes: builder.mutation({
      query: (body) => ({ url: "/incomes", method: "POST", body }),
    }),
    getAllIncomes: builder.query({
      query: () => ({ url: "/incomes", method: "GET" }),
    }),
    // /incomes/{income_id} to get a income by id
    getSingleIncome: builder.query({
      query: (id) => ({ url: `/incomes/${id}` }),
    }),
    // /incomes/{income_id}/edit to edit a income by id
    editIncome: builder.mutation({
      query: ({ body, id }) => ({
        url: `/incomes/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /incomes/search?query=income_name to search incomes by name
    searchIncomes: builder.query({
      query: (query) => ({ url: `/incomes/search?query=${query}` }),
    }),
    // delete income
    deleteIncome: builder.mutation({
      query: (id) => ({ url: `/incomes/${id}`, method: "DELETE" }),
    }),

    // expenditures section. create crude for /expenditures/
    createExpenditure: builder.mutation({
      query: (body) => ({ url: "/expenditures", body, method: "POST" }),
    }),
    // /expenditures/{expenditure_id} to get a expenditure by id
    getAllExpenditures: builder.query({
      query: () => ({ url: `/expenditures` }),
    }),
    getSingleExpenditure: builder.query({
      query: (id) => ({ url: `/expenditures/expenditure/${id}` }),
    }),
    // /expenditures/{expenditure_id}/edit to edit a expenditure by id
    editExpenditure: builder.mutation({
      query: ({ body, id }) => ({
        url: `/expenditures/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /expenditures/search?query=expenditure_name to search expenditures by name
    searchExpenditures: builder.query({
      query: (query) => ({
        url: `/expenditures/search?query=${query}`,
      }),
    }),
    createExpenseCategories: builder.mutation({
      query: (body) => ({ url: "/categories/expense", method: "POST", body }),
    }),

    deleteExpenseCategory: builder.mutation({
      query: (id) => ({ url: `/categories/expense/${id}`, method: "DELETE" }),
    }),
    getAllExpenseCategories: builder.query({
      query: () => ({ url: "/categories/expense", method: "GET" }),
    }),
    // /categories/income/{income_category_id} to get a income category by id
    getSingleExpenseCategory: builder.query({
      query: (id) => ({ url: `/categories/expense/${id}` }),
    }),
    // /categories/income/{income_category_id}/edit to edit a income category by id
    editExpenseCategory: builder.mutation({
      query: ({ body, id }) => ({
        url: `/categories/expense/${id}`,
        body,
        method: "PUT",
      }),
    }),

    // /categories/income to get all income categories
    createIncomeCategories: builder.mutation({
      // CRUD categories/income
      query: (body) => ({ url: "/categories/income", method: "POST", body }),
    }),
    deleteIncomeCategory: builder.mutation({
      query: (id) => ({ url: `/categories/income/${id}`, method: "DELETE" }),
    }),
    getAllIncomeCategories: builder.query({
      query: () => ({ url: "/categories/income", method: "GET" }),
    }),
    // /categories/income/{income_category_id} to get a income category by id
    getSingleIncomeCategory: builder.query({
      query: (id) => ({ url: `/categories/income/${id}` }),
    }),
    // /categories/income/{income_category_id}/edit to edit a income category by id
    editIncomeCategory: builder.mutation({
      query: ({ body, id }) => ({
        url: `/categories/income/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /categories/income/search?query=income_category_name to search income categories by name
    searchIncomeCategories: builder.query({
      query: (query) => ({
        url: `/categories/income/query=${query}`,
      }),
    }),

    // crud for fee-types
    // /fee-types to get all fee-types
    getAllFeeTypes: builder.query({
      query: () => ({ url: "/fee-types", method: "GET" }),
    }),
    // /fee-types/{fee_type_id} to get a fee-type by id
    getSingleFeeType: builder.query({
      query: (id) => ({ url: `/fee-types/${id}` }),
    }),
    // /fee-types/{fee_type_id}/edit to edit a fee-type by id
    editFeeType: builder.mutation({
      query: ({ body, id }) => ({
        url: `/fee-types/${id}`,
        body,
        method: "PUT",
      }),
    }),
    // /fee-types/search?query=fee_type_name to search fee-types by name
    searchFeeTypes: builder.query({
      query: (query) => ({ url: `/fee-types/${query}` }),
    }),

    // get fee status from the endponint fees/status
    getFeeStatus: builder.mutation({
      query: (body) => ({ url: "/fees/status", method: "POST", body }),
    }),

    // transactions/fetch-dashboard-stats
    getDashboardStats: builder.query({
      query: () => ({ url: "/transactions/fetch-dashboard-stats" }),
    }),
  }),
});

export const {
  useGetDashboardStatsQuery,
  // Fees
  useGetAllFeesQuery,
  useGetSingleFeeQuery,
  useCreatFeesMutation,
  useEditFeeMutation,
  useSearchFeesQuery,
  useRegisterFeeMutation,
  useUnregisterFeeMutation,
  useGetFeeStatusMutation,
  // Transactions
  useGetAllTransactionsQuery,
  useGetTransactionByReferenceQuery,
  useGetUserTransactionsQuery,
  // Accounts
  useGetAllAccountsQuery,
  useGetSingleAccountQuery,
  useDeleteAcountMutation,
  useEditAccountMutation,
  useSearchAccountsQuery,
  useCreateAccountMutation,
  // Incomes
  useGetAllIncomesQuery,
  useGetSingleIncomeQuery,
  useEditIncomeMutation,
  useSearchIncomesQuery,
  useDeleteIncomeMutation,
  useCreateIncomesMutation,
  // Expenditures
  useCreateExpenditureMutation,
  useGetSingleExpenditureQuery,
  useEditExpenditureMutation,
  useSearchExpendituresQuery,
  useGetAllExpendituresQuery,
  // Income Categories
  useGetAllIncomeCategoriesQuery,
  useGetSingleIncomeCategoryQuery,
  useEditIncomeCategoryMutation,
  useSearchIncomeCategoriesQuery,
  useDeleteIncomeCategoryMutation,
  useCreateIncomeCategoriesMutation,

  useGetAllExpenseCategoriesQuery,
  useGetSingleExpenseCategoryQuery,
  useEditExpenseCategoryMutation,
  useDeleteExpenseCategoryMutation,
  useCreateExpenseCategoriesMutation,

  useEditFeeTypeMutation,
} = financeApi;
