import { adminApi, authApi, finance<PERSON>pi, lmsApi, studentApi } from "@app/services";
import { authReducer } from "@features/auth";
import { filterReducer } from "@features/filter/filterSlice";
import { systemReducer } from "@features/system/systemSlice";
import { configureStore } from "@reduxjs/toolkit";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    filter: filterReducer,
    system: systemReducer,
    [authApi.reducerPath]: authApi.reducer,
    [studentApi.reducerPath]: studentApi.reducer,
    [adminApi.reducerPath]: adminApi.reducer,
    [financeApi.reducerPath]: financeApi.reducer,
    [lmsApi.reducerPath]: lmsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(authApi.middleware)
      .concat(studentApi.middleware)
      .concat(adminApi.middleware)
      .concat(financeApi.middleware)
      .concat(lmsApi.middleware),
});
