import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON>graphy,
  TextField,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  EmailOutlined,
  ArrowBackOutlined,
  CheckCircleOutlined,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import sideImage from "@assets/images/image.jpg";
import Logo from "@assets/images/logo.jpg";

const ForgotPassword = ({ 
  role = 'student', 
  useForgotPasswordMutation,
  title = 'Reset Password',
  subtitle = 'Enter your email address and we\'ll send you a link to reset your password.',
  loginPath = '/student/login'
}) => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [forgotPassword, { isLoading }] = useForgotPasswordMutation();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      const response = await forgotPassword({ email }).unwrap();
      
      if (response.success) {
        setIsSubmitted(true);
        toast.success('Password reset link sent to your email');
      } else {
        toast.error(response.message || 'Failed to send reset link');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      toast.error(error?.data?.message || 'An error occurred. Please try again.');
    }
  };

  const handleBackToLogin = () => {
    navigate(loginPath);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        {/* Left Side - Success Message */}
        <motion.div
          className="flex-1 flex items-center justify-center p-8"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-full max-w-md text-center">
            <motion.div
              className="inline-flex items-center justify-center p-4 bg-green-100 rounded-full mb-6"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <CheckCircleOutlined sx={{ fontSize: 48, color: '#16a34a' }} />
            </motion.div>
            
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Check Your Email
            </Typography>
            
            <Typography variant="body1" color="text.secondary" mb={4}>
              We've sent a password reset link to <strong>{email}</strong>
            </Typography>
            
            <Alert severity="info" sx={{ mb: 4, textAlign: 'left' }}>
              <Typography variant="body2">
                <strong>Didn't receive the email?</strong><br/>
                • Check your spam/junk folder<br/>
                • Make sure the email address is correct<br/>
                • Wait a few minutes for the email to arrive
              </Typography>
            </Alert>
            
            <Button
              variant="outlined"
              onClick={handleBackToLogin}
              startIcon={<ArrowBackOutlined />}
              fullWidth
              sx={{ mb: 2 }}
            >
              Back to Login
            </Button>
            
            <Button
              variant="text"
              onClick={() => setIsSubmitted(false)}
              size="small"
            >
              Try a different email address
            </Button>
          </div>
        </motion.div>

        {/* Right Side - Image */}
        <motion.div
          className="hidden lg:flex flex-1 relative overflow-hidden"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green-dark/90 z-10" />
          <img
            src={sideImage}
            alt="Education"
            className="w-full h-full object-cover"
          />
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form */}
      <motion.div
        className="flex-1 flex items-center justify-center p-8"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="w-full max-w-md">
          {/* Logo and Title */}
          <div className="text-center mb-8">
            <motion.div
              className="inline-flex items-center justify-center p-4 bg-white rounded-2xl shadow-sm mb-6"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <img src={Logo} alt="Logo" className="w-12 h-12" />
              <span className="text-2xl font-bold text-green ml-3">EHIST</span>
            </motion.div>
            
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {title}
            </Typography>
            
            <Typography variant="body1" color="text.secondary">
              {subtitle}
            </Typography>
          </div>

          {/* Form */}
          <motion.form
            onSubmit={handleSubmit}
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <EmailOutlined sx={{ mr: 1, color: 'text.secondary' }} />
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  backgroundColor: "white",
                },
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{
                backgroundColor: "#16a34a",
                "&:hover": { backgroundColor: "#15803d" },
                borderRadius: "12px",
                py: 1.5,
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
              }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Send Reset Link'
              )}
            </Button>
          </motion.form>

          {/* Back to Login */}
          <motion.div
            className="text-center mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
          >
            <Link
              to={loginPath}
              className="inline-flex items-center text-green hover:text-green-dark transition-colors"
            >
              <ArrowBackOutlined sx={{ mr: 1, fontSize: 18 }} />
              Back to Login
            </Link>
          </motion.div>
        </div>
      </motion.div>

      {/* Right Side - Image */}
      <motion.div
        className="hidden lg:flex flex-1 relative overflow-hidden"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green/90 to-green-dark/90 z-10" />
        <img
          src={sideImage}
          alt="Education"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 z-20 flex items-center justify-center p-12">
          <div className="text-center text-white">
            <motion.h3
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              Secure Account Recovery
            </motion.h3>
            <motion.p
              className="text-xl leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              We'll help you regain access to your account safely and securely.
            </motion.p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ForgotPassword;
