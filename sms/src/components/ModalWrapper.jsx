import { Box, Modal } from "@mui/material";
import PropTypes from "prop-types";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: { xs: "90vw", sm: 400 },
  bgcolor: "background.paper",
  borderRadius: "16px",
  boxShadow: 24,
  p: 0,
  overflowX: "hidden",
  // width: "fit-content",
};

function ModalWrapper({ openModal, closeModal, children, ...props }) {
  return (
    <Modal
      open={openModal}
      onClose={closeModal}
      aria-labelledby="modal"
      aria-describedby="my-modal"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backdropFilter: "blur(2px)",
        backgroundColor: "rgba(0,0,0,0.4)",
      }}
    >
      <Box sx={style} {...props}>
        {children}
      </Box>
    </Modal>
  );
}

ModalWrapper.propTypes = {
  openModal: PropTypes.bool.isRequired, // Validate openModal
  closeModal: PropTypes.func.isRequired, // Validate closeModal
  children: PropTypes.node,
};

export default ModalWrapper;
