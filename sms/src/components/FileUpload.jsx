import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Paper,
} from '@mui/material';
import {
  CloudUploadOutlined,
  AttachFileOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
} from '@mui/icons-material';
import { useUploadFileMutation } from '@app';

const FileUpload = ({ onUploadComplete, acceptedTypes = "*", maxSize = 10 * 1024 * 1024 }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadStatus, setUploadStatus] = useState({});
  
  const [uploadFile] = useUploadFileMutation();

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        setUploadStatus(prev => ({
          ...prev,
          [file.name]: { error: `File too large. Max size: ${formatFileSize(maxSize)}` }
        }));
        return false;
      }
      return true;
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);
    
    // Reset file input
    event.target.value = '';
  };

  const handleRemoveFile = (fileName) => {
    setSelectedFiles(prev => prev.filter(file => file.name !== fileName));
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[fileName];
      return newProgress;
    });
    setUploadStatus(prev => {
      const newStatus = { ...prev };
      delete newStatus[fileName];
      return newStatus;
    });
  };

  const handleUpload = async () => {
    for (const file of selectedFiles) {
      try {
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
        setUploadStatus(prev => ({ ...prev, [file.name]: { uploading: true } }));

        const formData = new FormData();
        formData.append('file', file);

        // Simulate upload progress (in real implementation, you'd track actual progress)
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            const currentProgress = prev[file.name] || 0;
            if (currentProgress < 90) {
              return { ...prev, [file.name]: currentProgress + 10 };
            }
            return prev;
          });
        }, 200);

        const result = await uploadFile(formData).unwrap();
        
        clearInterval(progressInterval);
        setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
        setUploadStatus(prev => ({ 
          ...prev, 
          [file.name]: { success: true, url: result.url } 
        }));

        if (onUploadComplete) {
          onUploadComplete(result);
        }

      } catch (error) {
        setUploadStatus(prev => ({ 
          ...prev, 
          [file.name]: { error: error.message || 'Upload failed' } 
        }));
      }
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    // You can add more specific icons based on file type
    return <AttachFileOutlined />;
  };

  return (
    <Box>
      {/* Upload Area */}
      <Paper
        sx={{
          border: '2px dashed #ccc',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          cursor: 'pointer',
          '&:hover': {
            borderColor: 'primary.main',
            backgroundColor: 'action.hover',
          },
        }}
        component="label"
      >
        <input
          type="file"
          multiple
          hidden
          accept={acceptedTypes}
          onChange={handleFileSelect}
        />
        <CloudUploadOutlined sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Click to upload files
        </Typography>
        <Typography variant="body2" color="text.secondary">
          or drag and drop files here
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block" mt={1}>
          Max file size: {formatFileSize(maxSize)}
        </Typography>
      </Paper>

      {/* Selected Files List */}
      {selectedFiles.length > 0 && (
        <Box mt={3}>
          <Typography variant="h6" gutterBottom>
            Selected Files ({selectedFiles.length})
          </Typography>
          <List>
            {selectedFiles.map((file) => {
              const progress = uploadProgress[file.name] || 0;
              const status = uploadStatus[file.name] || {};
              
              return (
                <ListItem key={file.name} divider>
                  <ListItemIcon>
                    {status.success ? (
                      <CheckCircleOutlined color="success" />
                    ) : (
                      getFileIcon(file.name)
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={file.name}
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {formatFileSize(file.size)}
                        </Typography>
                        {status.uploading && (
                          <Box mt={1}>
                            <LinearProgress variant="determinate" value={progress} />
                            <Typography variant="caption" color="text.secondary">
                              {progress}% uploaded
                            </Typography>
                          </Box>
                        )}
                        {status.error && (
                          <Alert severity="error" sx={{ mt: 1 }}>
                            {status.error}
                          </Alert>
                        )}
                        {status.success && (
                          <Alert severity="success" sx={{ mt: 1 }}>
                            Upload completed successfully!
                          </Alert>
                        )}
                      </Box>
                    }
                  />
                  {!status.success && (
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveFile(file.name)}
                      color="error"
                    >
                      <DeleteOutlined />
                    </IconButton>
                  )}
                </ListItem>
              );
            })}
          </List>

          {/* Upload Button */}
          <Box mt={2}>
            <Button
              variant="contained"
              onClick={handleUpload}
              disabled={selectedFiles.length === 0 || Object.values(uploadStatus).some(s => s.uploading)}
              startIcon={<CloudUploadOutlined />}
              fullWidth
            >
              {Object.values(uploadStatus).some(s => s.uploading) ? 'Uploading...' : 'Upload Files'}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default FileUpload;
