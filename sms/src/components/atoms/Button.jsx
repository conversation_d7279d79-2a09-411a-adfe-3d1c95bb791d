import PropTypes from "prop-types";

function Button({
  title,
  type,
  disabled,
  className = "",
  children = <></>,
  ...props
}) {
  switch (type) {
    case "gray":
      return (
        <button
          disabled={disabled}
          type="button"
          className={`rounded-lg p-2 w-fit ${
            disabled
              ? "bg-gray-200 cursor-not-allowed text-gray-400"
              : "bg-gray-500 text-white"
          } `.concat(className)}
          {...props}
        >
          {children}
          {title}
        </button>
      );
    case "primary":
      return (
        <button
          disabled={disabled}
          type="button"
          className={`rounded-lg p-2 w-fit ${
            disabled
              ? "bg-blue-200 cursor-not-allowed text-gray-300"
              : "bg-[#1F7F1F]  text-white"
          } `.concat(className)}
          {...props}
        >
          {children}
          {title}
        </button>
      );
    case "danger":
      return (
        <button
          disabled={disabled}
          type="button"
          className={`rounded-lg p-2 w-fit ${
            disabled
              ? "bg-blue-200 cursor-not-allowed text-gray-300"
              : "bg-[#D60A0B]  text-white"
          } `.concat(className)}
          {...props}
        >
          {children}
          {title}
        </button>
      );

    default:
      return (
        <button
          type="button"
          className={`rounded-lg p-2 w-fit ${
            disabled
              ? "bg-blue-200 cursor-not-allowed text-gray-300"
              : "bg-blue-500 text-white"
          }`}
          {...props}
        >
          {title}
        </button>
      );
  }
}

export default Button;

// Add prop types validation
Button.propTypes = {
  title: PropTypes.string.isRequired,
  type: PropTypes.oneOf(["gray", "primary", "danger"]),
  disabled: PropTypes.bool,
  className: PropTypes.string,
  children: PropTypes.node,
};
