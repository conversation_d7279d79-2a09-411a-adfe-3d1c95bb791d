import { CircularProgress } from "@mui/material";
import PropTypes from "prop-types"; // Add this import

const Spinner = ({ ...props }) => {
  return (
    <div className="flex items-center justify-center " {...props}>
      <CircularProgress color="success" size={props?.size ?? "30px"} />
    </div>
  );
};

// Add prop types validation
Spinner.propTypes = {
  size: PropTypes.string, // Validate the size prop
};

export default Spinner;
