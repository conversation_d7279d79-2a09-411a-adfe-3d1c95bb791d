import { Box } from "@mui/material";
import React from "react";

function SuccessSvg() {
  return (
    <Box className="text-center">
      <svg
        width="150"
        height="200"
        viewBox="0 0 314 314"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M228.601 91.2256L242.399 105.024L127.562 219.861L71.6006 163.899L85.3994 150.101L127.562 192.264L228.601 91.2256ZM157 0C171.412 0 185.313 1.83984 198.703 5.51953C212.093 9.19922 224.614 14.4632 236.267 21.3115C247.919 28.1598 258.498 36.3369 268.004 45.8428C277.51 55.3486 285.687 65.9788 292.535 77.7334C299.383 89.488 304.647 102.009 308.327 115.297C312.007 128.585 313.898 142.486 314 157C314 171.412 312.16 185.313 308.48 198.703C304.801 212.093 299.537 224.614 292.688 236.267C285.84 247.919 277.663 258.498 268.157 268.004C258.651 277.51 248.021 285.687 236.267 292.535C224.512 299.383 211.991 304.647 198.703 308.327C185.415 312.007 171.514 313.898 157 314C142.588 314 128.687 312.16 115.297 308.48C101.907 304.801 89.3857 299.537 77.7334 292.688C66.0811 285.84 55.502 277.663 45.9961 268.157C36.4902 258.651 28.3132 248.021 21.4648 236.267C14.6165 224.512 9.35254 212.042 5.67285 198.856C1.99316 185.671 0.102214 171.719 0 157C0 142.588 1.83984 128.687 5.51953 115.297C9.19922 101.907 14.4632 89.3857 21.3115 77.7334C28.1598 66.0811 36.3369 55.502 45.8428 45.9961C55.3486 36.4902 65.9788 28.3132 77.7334 21.4648C89.488 14.6165 101.958 9.35254 115.144 5.67285C128.329 1.99316 142.281 0.102214 157 0ZM157 294.375C169.572 294.375 181.685 292.74 193.337 289.469C204.989 286.198 215.926 281.598 226.147 275.67C236.369 269.742 245.67 262.535 254.052 254.052C262.433 245.568 269.588 236.318 275.517 226.301C281.445 216.284 286.096 205.347 289.469 193.49C292.842 181.633 294.477 169.47 294.375 157C294.375 144.428 292.74 132.315 289.469 120.663C286.198 109.011 281.598 98.0739 275.67 87.8525C269.742 77.6312 262.535 68.3298 254.052 59.9482C245.568 51.5667 236.318 44.4118 226.301 38.4834C216.284 32.555 205.347 27.9043 193.49 24.5312C181.633 21.1582 169.47 19.5228 157 19.625C144.428 19.625 132.315 21.2604 120.663 24.5312C109.011 27.8021 98.0739 32.4017 87.8525 38.3301C77.6312 44.2585 68.3298 51.4645 59.9482 59.9482C51.5667 68.432 44.4118 77.6823 38.4834 87.6992C32.555 97.7161 27.9043 108.653 24.5312 120.51C21.1582 132.367 19.5228 144.53 19.625 157C19.625 169.572 21.2604 181.685 24.5312 193.337C27.8021 204.989 32.4017 215.926 38.3301 226.147C44.2585 236.369 51.4645 245.67 59.9482 254.052C68.432 262.433 77.6823 269.588 87.6992 275.517C97.7161 281.445 108.653 286.096 120.51 289.469C132.367 292.842 144.53 294.477 157 294.375Z"
          fill="#0FA958"
        />
      </svg>
    </Box>
  );
}

export default SuccessSvg;
