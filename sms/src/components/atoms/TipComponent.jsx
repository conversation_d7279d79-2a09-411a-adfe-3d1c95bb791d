import { QuestionMark } from "@mui/icons-material";
import { Tooltip } from "@mui/material";
import PropTypes from "prop-types";

export const TipComponent = ({ tip, Icon, ...props }) => (
  <Tooltip
    sx={{
      cursor: "pointer",
      background: "#000",
      color: "white",
      borderRadius: "50%",
      padding: "2px",
    }}
    title={tip ?? "School"}
    {...props}
  >
    {Icon ? Icon : <QuestionMark />}
  </Tooltip>
);

// Add prop types validation
TipComponent.propTypes = {
  tip: PropTypes.string, // Validate 'tip' as a string
  Icon: PropTypes.elementType, // Validate 'Icon' as a React component
};
