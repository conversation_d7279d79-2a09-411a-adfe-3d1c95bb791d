import ModalWrapper from "@components/ModalWrapper";
import { Button, FailureSvg, SuccessSvg } from "@components/atoms";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";

function MessageModal({ openModal, closeModal, type, message }) {
  const navigate = useNavigate();

  {
    /* This is a bad practice for a complex component. Consider moving it to a seperate component */
  }
  const render = () => {
    switch (type) {
      case "info":
        return <SuccessSvg />;
      case "error":
        return <FailureSvg />;
    }
  };
  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
      <div className="grid place-items-center">
        {render()}
        <p>{message}</p>
        <div className="flex flex-row justify-between items-center gap-x-2">
          <Button
            title={"Return Home"}
            type={"gray"}
            onClick={() => navigate("/")}
          />
          <Button
            title={"Submit another"}
            type={"primary"}
            onClick={closeModal}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}

MessageModal.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  type: PropTypes.string.isRequired,
  message: PropTypes.string.isRequired,
};

export default MessageModal;
