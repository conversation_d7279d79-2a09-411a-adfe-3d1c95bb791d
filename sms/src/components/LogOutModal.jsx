import { useLogoutMutation } from "@app";
import { <PERSON><PERSON>, Spin<PERSON> } from "@components";
import ModalWrapper from "@components/ModalWrapper";
import { logout } from "@features";
import { RequestInterceptor } from "@lib/util";
import LogoutIcon from "@mui/icons-material/Logout";
import { Stack } from "@mui/material";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

function LogOutModal({ openModal, closeModal }) {
  const navigate = useNavigate();
  const { role } = useSelector((state) => state.auth);
  const [logoutUser, { isLoading }] = useLogoutMutation();
  const dispatch = useDispatch();

  const removeUser = async () => {
    try {
      await RequestInterceptor.handleRequest(
        () => logoutUser(),
        {
          onSuccess: () => {
            dispatch(logout());
            navigate(`/${role == "student" ? "student" : "admin"}/login`);
          },
          shouldAlert: false,
        },
        LogOutModal.name
      );
    } catch (error) {
      alert("Error logging out");
    }
  };

  return (
    <ModalWrapper openModal={openModal} closeModal={closeModal}>
        <div className="flex flex-col items-center justify-center gap-2 w-full p-6 rounded-2xl">
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-50 mb-2 shadow">
            <LogoutIcon className="text-red-500" style={{ fontSize: 38 }} />
          </div>
          <h2 className="text-xl font-extrabold text-red-600 text-center">
            Log Out?
          </h2>
          <p className="text-gray-600 text-center text-sm mb-2">
            Are you sure you want to log out of your account?
          </p>
        </div>
        <Stack
          direction={"row"}
          gap={2} 
          className="w-full flex items-center justify-center mt-2 mb-5"
        >
          <Button
            title={"No"}
            type={"primary"}
            onClick={closeModal}
            className="px-6 py-2 rounded-lg font-bold shadow bg-green-50 text-green-700 hover:bg-green-100 hover:text-green-900 border border-green-200 transition-colors duration-200"
          />
          {isLoading ? (
            <Spinner />
          ) : (
            <Button
              title={"Yes, Log Out"}
              type={"danger"}
              onClick={removeUser}
              className="px-6 py-2 rounded-lg font-bold shadow bg-red-500 text-white hover:bg-red-700 border border-red-400 transition-colors duration-200"
            />
          )}
        </Stack>
    </ModalWrapper>
  );
}

LogOutModal.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
};

export default LogOutModal;
