import { <PERSON><PERSON><PERSON> } from "@mui/x-charts";
import PropTypes from "prop-types";

function PieChartComponent({ data }) {
  const series = data?.map((item) => ({
    value: parseFloat(item.balance),
    label: item.name,
  }));
  const size = {
    height: 200,
  };

  return (
    <div className="w-full px-3">
      <PieChart series={[{ data: series, innerRadius: 80 }]} {...size} />
    </div>
  );
}

PieChartComponent.propTypes = {
  data: PropTypes.array.isRequired,
};

export default PieChartComponent;
