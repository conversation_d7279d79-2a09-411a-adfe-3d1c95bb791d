import { LineChart } from "@mui/x-charts/LineChart";
import PropTypes from "prop-types";

const LineChartComponent = ({ labels, label1, data1, label2, data2 }) => {
  return (
    <div className="w-full h-hull ">
      <LineChart
        className="w-full h-full my-3"
        height={300}
        series={[
          { data: data1, label: label1, color: "green" },
          { data: data2, label: label2, color: "red" },
        ]}
        xAxis={[{ scaleType: "point", data: labels }]}
      />
    </div>
  );
};

LineChartComponent.propTypes = {
  labels: PropTypes.array.isRequired,
  label1: PropTypes.string.isRequired,
  data1: PropTypes.array.isRequired,
  label2: PropTypes.string.isRequired,
  data2: PropTypes.array.isRequired,
};

export default LineChartComponent;
