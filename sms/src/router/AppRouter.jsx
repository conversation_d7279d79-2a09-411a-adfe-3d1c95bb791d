import { Admin, ErrorPageNotFound, Home, Student } from "@pages";
import { adminRoutes, authRoutes } from "@pages/admin/router";
import { Finance } from "@pages/Finance";
import { financeRoutes } from "@pages/Finance/router";
import { Lecturer } from "@pages/lecturer";
import { lecturerRoutes, lecturerAuthRoutes } from "@pages/lecturer/router";
import { studentAuthRoutes, studentRoutes } from "@pages/student/router";
import { createBrowserRouter } from "react-router-dom";

export const router = createBrowserRouter([
  {
    element: <Home />,
    path: "",
    errorElement: <ErrorPageNotFound />,
  },
  {
    element: <Admin />,
    path: "admin",
    errorElement: <ErrorPageNotFound />,
    children: adminRoutes,
  },
  ...authRoutes,

  {
    element: <Student />,
    path: "student",
    errorElement: <ErrorPageNotFound />,
    children: studentRoutes,
  },
  ...studentAuthRoutes,

  {
    element: <Lecturer />,
    path: "lecturer",
    errorElement: <ErrorPageNotFound />,
    children: lecturerRoutes,
  },
  ...lecturerAuthRoutes,

  {
    element: <Finance />,
    path: "finance",
    children: financeRoutes,
  },
  ...financeRoutes,
]);
