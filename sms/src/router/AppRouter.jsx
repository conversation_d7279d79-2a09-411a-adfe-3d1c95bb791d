import { Admin, ErrorPageNotFound, Student } from "@pages";
import { adminRoutes, authRoutes } from "@pages/admin/router";
import { Finance } from "@pages/Finance";
import { financeRoutes } from "@pages/Finance/router";
import { studentAuthRoutes, studentRoutes } from "@pages/student/router";
import { createBrowserRouter } from "react-router-dom";

export const router = createBrowserRouter([
  {
    element: <Student />,
    path: "",
    errorElement: <ErrorPageNotFound />,
    children: studentRoutes,
  },
  {
    element: <Admin />,
    path: "admin",
    errorElement: <ErrorPageNotFound />,
    children: adminRoutes,
  },
  ...authRoutes,

  {
    element: <Student />,
    path: "student",
    errorElement: <ErrorPageNotFound />,
    children: studentRoutes,
  },
  ...studentAuthRoutes,

  {
    element: <Finance />,
    path: "finance",
    children: financeRoutes,
  },
  ...financeRoutes,
]);
