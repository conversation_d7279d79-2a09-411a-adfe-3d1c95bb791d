import { BASE_URL } from "@lib/config";
import { useSelector } from "react-redux";

export function useDownload() {
  const { token } = useSelector((state) => state.auth);
  const download = async (path, type = "application/pdf") => {
    const res = await fetch(`${BASE_URL}/${path}`, {
      headers: {
        authorization: `Bearer ${token}`,
      },
    });

    const blob = new Blob([await res.blob()], { endings: "native", type });
    const href = URL.createObjectURL(blob);

    return href;
  };
  return download;
}
