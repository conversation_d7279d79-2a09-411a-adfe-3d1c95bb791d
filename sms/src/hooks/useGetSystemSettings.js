import { useGetSystemSettingsQuery } from "@app";
import { setFilter } from "@features/filter/filterSlice";
import { setSystemSettings } from "@features/system/systemSlice";
import { transformArrayOfKeyValueObjectToPlainObject } from "@lib/util";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export const useGetSystemSettings = () => useSelector((state) => state.system);
export const useLoadSystemSettings = () => {
  const dispatch = useDispatch();
  const { data: systemSettings, status: SystemSettingsStatus } =
    useGetSystemSettingsQuery("system settings");

  useEffect(() => {
    switch (SystemSettingsStatus) {
      case "fulfilled": {
        const formattedData = transformArrayOfKeyValueObjectToPlainObject([
          ...systemSettings?.data,
        ]);
        dispatch(setSystemSettings(formattedData));
        dispatch(
          setFilter({
            academic_year: formattedData.current_academic_year_id,
            semester: formattedData.current_semester,
            level: null,
          })
        );
      }
    }
  }, [SystemSettingsStatus]);
};
