import { toast } from "react-toastify";

/**
 * Custom error class for handling ResponseInterceptor-specific errors.
 */
export class ResponseInterceptorError extends Error {
  /**
   * Creates an instance of ResponseInterceptorError.
   * @param {string} message - The error message.
   */
  constructor(message) {
    super(message);
    this.name = "ResponseInterceptorError";
  }
}

/**
 * ResponseInterceptor is responsible for handling a response object and
 * ensuring that its methods are not called until the response is applied
 * using the `apply()` method.
 */
export class ResponseInterceptor {
  /**
   * Private flag to track whether the `apply()` method has been called.
   * @private
   * @type {boolean}
   */
  #isApplied = false;

  /**
   * Creates an instance of ResponseInterceptor.
   * @param {string} [tag="Interceptor"] - An optional tag for logging purposes.
   */
  constructor(tag = "Interceptor") {
    this.tag = tag;
  }

  /**
   * Applies the response data to the interceptor.
   * This method must be called before using other methods.
   *
   * @param {Object} response - The response object containing data.
   * @param {*} response.data - The data associated with the response.
   * @param {string} response.message - The message associated with the response.
   * @param {boolean} response.success - Whether the response was successful.
   */
  apply(response) {
    const { data, message, success } = response;
    this.data = data;
    this.message = message;
    this.success = success;
    this.#isApplied = true;
  }

  /**
   * Checks if the `apply()` method has been called.
   * Throws an error if `apply()` has not been invoked.
   *
   * @private
   * @throws {Error} Throws an error if `apply()` is not called before using other methods.
   */
  _checkIfApplied() {
    if (!this.#isApplied) {
      throw new ResponseInterceptorError(
        "apply() must be called before using this method."
      );
    }
  }

  /**
   * Alerts the consumer with the message from the response.
   * This method can only be called after `apply()` is invoked.
   *
   * @throws {Error} Throws an error if `apply()` is not called first.
   */
  alertConsumer() {
    this._checkIfApplied();
    alert(this.message);
  }

  /**
   * Logs the message to the console and shows an alert.
   * This method can only be called after `apply()` is invoked.
   *
   * @throws {Error} Throws an error if `apply()` is not called first.
   */
  lotAndAlert() {
    this._checkIfApplied();
    console.log(`${this.tag}: ${this.message}`);
    alert(`${this.tag}: ${this.message}`);
  }

  /**
   * Logs the message to the console.
   * This method can only be called after `apply()` is invoked.
   *
   * @throws {Error} Throws an error if `apply()` is not called first.
   */
  log() {
    this._checkIfApplied();
    console.log(`${this.tag}: ${this.message}`);
  }
}

/**
 * RequestInterceptor class to handle common request patterns for creating, updating,
 * and deleting resources, while managing responses and errors consistently.
 */
export class RequestInterceptor {
  /**
   * Handles requests such as create, update, or delete operations.
   * This method wraps the request in try-catch logic and provides support
   * for confirmation dialogs, success/error messages, and callbacks.
   *
   * @param {Function} request - The request function that returns a promise (e.g., an API call).
   * @param {Object} [options={}] - Configuration options for the request handling.
   * @param {string} [options.confirmMessage] - Optional confirmation message (used for delete actions or other sensitive operations).
   * @param {string} [options.successMessage] - The success message to display on successful response.
   * @param {string} [options.errorMessage] - The error message to display if the request fails.
   * @param {Function} [options.onSuccess] - Optional callback function to be executed after a successful request.
   * @param {Function} [options.onError] - Optional callback function to be executed when an error occurs.
   * @param {boolean} [options.shouldAlert=true] - Indicates whether alert is required after performing the request.
   * @param {boolean} [options.shouldConfirm=false] - Indicates whether confirmation is required before performing the request.
   *
   * @example
   * // Example usage for deleting a resource
   * await RequestInterceptor.handleRequest(
   *   () => deleteResource(resourceId),
   *   {
   *     confirmMessage: "Are you sure you want to delete this resource?",
   *     successMessage: "Resource deleted successfully.",
   *     errorMessage: "Error deleting resource.",
   *     onSuccess: () => console.log("Deleted successfully"),
   *   }
   * );
   *
   * @example
   * // Example usage for creating a resource
   * await RequestInterceptor.handleRequest(
   *   () => createResource(data),
   *   {
   *     successMessage: "Resource created successfully.",
   *     errorMessage: "Error creating resource.",
   *     onSuccess: () => closeModal(),
   *   }
   * );
   *
   * @returns {Promise<void>} Resolves when the request is completed.
   */
  static async handleRequest(request, options = {}, tag = "REQUEST") {
    const {
      confirmMessage,
      successMessage,
      errorMessage,
      onSuccess,
      onError,
      shouldAlert = true,
      shouldConfirm = false,
    } = options;

    try {
      // Optionally confirm the action (for delete or sensitive actions)
      if (shouldConfirm) {
        const hasConfirmed = confirm(
          confirmMessage || `Are you sure you want to ${tag}?`
        );
        if (!hasConfirmed) {
          return;
        }
      }

      // Execute the request and handle the response
      const { message, success, data } = await request().unwrap();

      // Show success message
      console.debug(`${tag}: ${successMessage || message}`);
      shouldAlert && toastMessage(success, successMessage ?? message);

      // Optional success callback
      if (onSuccess && success) {
        onSuccess();
      }
      return data;
    } catch (error) {
      // Show error message
      console.error(`${tag}:`, error);

      // Optional error callback
      if (onError) {
        onError(error);
      }
      function constructErrorMessage(error) {
        if (error.data?.errors) {
          const errorMessages = Object.entries(error.data.errors)
            .map(([field, messages]) =>
              Array.isArray(messages)
                ? messages.map((msg) => `${field}: ${msg}`).join("; ")
                : `${field}: ${messages}`
            )
            .join("; ");

          return errorMessages || "Validation Error";
        }
        if (error.error) {
          return error.error;
        }

        if (error.data.message) {
          return error.data.message;
        }
        return "Something went wrong. Please try again.";
      }
      shouldAlert &&
        toastMessage(false, errorMessage ?? constructErrorMessage(error));
      return {
        message: errorMessage ?? constructErrorMessage(error),
        success: false,
        tag,
      };
    }
  }

  static async downloadFile(downloadFunction, query, fileName, mimeType) {
    try {
      const url = await downloadFunction(query, mimeType);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      a.click();
      a.remove();
      URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Error Downloading File.");
      console.error("Error downloading file", error);
    }
  }
}

function toastMessage(success, message) {
  if (success) {
    toast.info(message, {
      position: "top-right",
      autoClose: 1500,
    });
  } else {
    toast.error(message, {
      position: "top-right",
      autoClose: 1500,
    });
  }
}
