.icon {
  display: none;
}

@media screen and (max-width: 934px) {
  .hide {
    width: 45%;
  }

  .header {
    width: 100%;
  }

  .side {
    z-index: 1;
    transition: 0.5s;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
  }
}

@media screen and (max-width: 897px) {
  .hide {
    display: none;
  }

  img.logo {
    display: none;
  }

  .sidebar {
    background: #f0f1f3;
    height: 100vh;
  }

  .icon {
    height: 48px;
    width: 44px;
    display: flex;
    justify-self: center;
    align-self: center;
  }

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
  }

  .side {
    z-index: 1;
    transition: 0.5s;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
  }
}
