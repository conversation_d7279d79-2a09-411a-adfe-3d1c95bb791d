import api from './api';

// Lecturer Dashboard APIs
export const lecturerApi = {
  // Dashboard
  getDashboardStats: () => api.get('/lecturer/dashboard/stats'),
  getRecentActivities: () => api.get('/lecturer/dashboard/activities'),

  // Courses
  getCourses: (params = {}) => api.get('/lecturer/courses', { params }),
  getCourseById: (id) => api.get(`/lecturer/courses/${id}`),
  createCourse: (data) => api.post('/lecturer/courses', data),
  updateCourse: (id, data) => api.put(`/lecturer/courses/${id}`, data),
  deleteCourse: (id) => api.delete(`/lecturer/courses/${id}`),
  getCourseStudents: (id) => api.get(`/lecturer/courses/${id}/students`),
  getCourseAnalytics: (id) => api.get(`/lecturer/courses/${id}/analytics`),

  // Course Materials
  getCourseMaterials: (courseId) => api.get(`/lecturer/courses/${courseId}/materials`),
  uploadMaterial: (courseId, formData) => api.post(`/lecturer/courses/${courseId}/materials`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  updateMaterial: (courseId, materialId, data) => api.put(`/lecturer/courses/${courseId}/materials/${materialId}`, data),
  deleteMaterial: (courseId, materialId) => api.delete(`/lecturer/courses/${courseId}/materials/${materialId}`),

  // Assignments
  getAssignments: (params = {}) => api.get('/lecturer/assignments', { params }),
  getAssignmentById: (id) => api.get(`/lecturer/assignments/${id}`),
  createAssignment: (data) => api.post('/lecturer/assignments', data),
  updateAssignment: (id, data) => api.put(`/lecturer/assignments/${id}`, data),
  deleteAssignment: (id) => api.delete(`/lecturer/assignments/${id}`),
  getAssignmentSubmissions: (id) => api.get(`/lecturer/assignments/${id}/submissions`),
  gradeSubmission: (assignmentId, submissionId, data) => api.put(`/lecturer/assignments/${assignmentId}/submissions/${submissionId}/grade`, data),

  // Assessments
  getAssessments: (params = {}) => api.get('/lecturer/assessments', { params }),
  getAssessmentById: (id) => api.get(`/lecturer/assessments/${id}`),
  createAssessment: (data) => api.post('/lecturer/assessments', data),
  updateAssessment: (id, data) => api.put(`/lecturer/assessments/${id}`, data),
  deleteAssessment: (id) => api.delete(`/lecturer/assessments/${id}`),
  getAssessmentResults: (id) => api.get(`/lecturer/assessments/${id}/results`),
  getAssessmentQuestions: (id) => api.get(`/lecturer/assessments/${id}/questions`),
  addQuestion: (assessmentId, data) => api.post(`/lecturer/assessments/${assessmentId}/questions`, data),
  updateQuestion: (assessmentId, questionId, data) => api.put(`/lecturer/assessments/${assessmentId}/questions/${questionId}`, data),
  deleteQuestion: (assessmentId, questionId) => api.delete(`/lecturer/assessments/${assessmentId}/questions/${questionId}`),

  // Analytics
  getStudentAnalytics: (params = {}) => api.get('/lecturer/analytics/students', { params }),
  getCoursePerformance: (courseId) => api.get(`/lecturer/analytics/courses/${courseId}/performance`),
  getAssignmentAnalytics: (assignmentId) => api.get(`/lecturer/analytics/assignments/${assignmentId}`),
  getAssessmentAnalytics: (assessmentId) => api.get(`/lecturer/analytics/assessments/${assessmentId}`),

  // Profile
  getProfile: () => api.get('/lecturer/profile'),
  updateProfile: (data) => api.put('/lecturer/profile', data),
  changePassword: (data) => api.put('/lecturer/profile/password', data),
  uploadProfileImage: (formData) => api.post('/lecturer/profile/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),

  // Notifications
  getNotifications: () => api.get('/lecturer/notifications'),
  markNotificationRead: (id) => api.put(`/lecturer/notifications/${id}/read`),
  markAllNotificationsRead: () => api.put('/lecturer/notifications/read-all'),

  // Students
  getStudents: (params = {}) => api.get('/lecturer/students', { params }),
  getStudentById: (id) => api.get(`/lecturer/students/${id}`),
  getStudentProgress: (studentId, courseId) => api.get(`/lecturer/students/${studentId}/progress/${courseId}`),
};

export default lecturerApi;
