module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  settings: {
    react: {
      version: "18.2.0",
    },
  },
  extends: [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended",
  ],
  ignorePatterns: ["dist", ".eslintrc.cjs"],
  parserOptions: { ecmaVersion: "latest", sourceType: "module" },
  plugins: ["react-refresh", "unused-imports"],
  rules: {
    // React rules
    "react/jsx-no-target-blank": "off",
    "react/react-in-jsx-scope": "off", // Disable the need for React in scope
    "react-refresh/only-export-components": [
      "warn",
      { allowConstantExport: true },
    ],

    // Enforce strict variable usage
    "no-undef": "error",
    "no-unused-vars": "off",
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "error",
      {
        vars: "all",
        varsIgnorePattern: "^_",
        args: "after-used",
        argsIgnorePattern: "^_",
      },
    ],

    // Other custom rules
    "import/no-unresolved": "error", // Ensure imports are resolved correctly
  },
};
